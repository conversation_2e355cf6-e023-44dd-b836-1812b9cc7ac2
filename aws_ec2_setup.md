1. 使用 public subnet
2. 确认是否使用了 ENA 网卡，ethtool -i <interface_name>
3. https://talawah.io/blog/extreme-http-performance-tuning-one-point-two-million/#_6-interrupt-optimizations
  1. 这个调节之后让下单的 P20 稳定在了 1.2ms
sudo ethtool -K ens5 gro off gso off tso off lro off
sudo ethtool -C ens5 adaptive-rx off tx-usecs 0  // maybe try 20us
4. 设置 busy poll
sudo sysctl -w net.core.busy_poll=50
sudo sysctl -w net.core.busy_read=50
5. 设置tcp参数
sudo sysctl -w net.ipv4.tcp_window_scaling=1
sudo sysctl -w net.ipv4.tcp_low_latency=1
6. 设置cpu affinity
7. https://github.com/amzn/amzn-drivers/blob/master/kernel/linux/ena/ENA_Linux_Best_Practices.rst
8. https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/ena-improve-network-latency-linux.html
9. https://talawah.io/blog/extreme-http-performance-tuning-one-point-two-million

测试 rtt 的命令
sudo hping3 -S -p 443 <Binance_IP> -i u1000 -c 1000
