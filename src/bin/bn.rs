use libwebsocket_rs::{
    CallbackD<PERSON>, Message, Result, Settings, WebSocket, WebSocketHandle,
    configs::{GATE_IN_LEN, GATE_OUT_LEN},
    encoding::{
        agg_trades::parse_futures_agg_trade,
        binance_futures::{
            generate_futures_position_snapshot_request, generate_futures_session_logon_request,
            generate_futures_user_data_start_request, generate_futures_user_data_stream_url,
        },
        bn_futures_funding::{self, generate_funding_sub_url},
        bn_futures_order::OrderSide,
        book_ticker::parse_futures_bookticker,
        futures_order_response::{
            FuturesOrderResponse, OrderStatus, UserDataResponse, parse_futures_order_response,
            parse_position_risk_json, parse_user_data_response,
        },
        futures_orderbook::parse_futures_orderbook_snapshot_as_bbo,
        gate::{self, GateWebsocketRsp, OrderPlaceMsg, generate_position_snapshot_request},
        gate_futures_funding,
        gate_orderbook::{self, MAX_LEVELS, generate_orderbook_sub_request},
    },
    engine::{
        binance::{
            generate_futures_agg_trade_url_by_symbols, generate_futures_book_ticker_url,
            generate_futures_depth_snapshot_5_url, generate_futures_order_url,
        },
        bn_maker_engine::BnMaker,
        cross_exchange::{
            gate_const::{
                BN_FUNDING_FEE, GATE_BASE_ASSETS, GATE_FUNDING_FEE, GATE_QUANTO_MULTIPLIER,
                UnifiedCurrency,
            },
            hedge_manager::HedgeStatus,
            token::*,
        },
        net_utils::connection_watch_dog::{ConnectionType, ConnectionWatchDog},
    },
    error, flush_logs, info,
    net::{message::http::StatusCode, utils::url::Url},
    utils::{self, perf::circles_to_ns},
};
use std::env;

const GATE_WS_URL: &str = "wss://fx-ws.gateio.ws/v4/ws/usdt";

fn generate_bbo_url() -> String {
    GATE_WS_URL.to_string()
}

fn print_usage() {
    info!("Usage: gate [OPTIONS]\n");
    info!("Options:\n");
    info!("  --stdout         Enable stdout logging (logs will be printed to console)\n");
    info!("  --help, -h       Show this help message\n");
}

fn main() -> std::result::Result<(), Box<dyn std::error::Error>> {
    let args: Vec<String> = env::args().collect();

    // 解析命令行参数
    let mut enable_stdout = false; // 默认不启用stdout日志

    for arg in args.iter().skip(1) {
        match arg.as_str() {
            "--stdout" => {
                enable_stdout = true;
            }
            "--help" | "-h" => {
                print_usage();
                return Ok(());
            }
            _ => {
                libwebsocket_rs::error!("Unknown argument: {}", arg);
                print_usage();
                return Err("Invalid arguments".into());
            }
        }
    }

    // 启用stdout日志输出（如果指定了--stdout参数）
    libwebsocket_rs::utils::logger::enable_stdout_logging(enable_stdout);
    let mut engine = BnMaker::new();
    let mut listen_key_created = false;
    let mut watch_dog = ConnectionWatchDog::new();
    let mut bn_user_data_stream_connected = false;
    let callback = move |handle: &mut WebSocketHandle<GATE_IN_LEN, GATE_OUT_LEN>,
                         cd: CallbackData|
          -> Result<()> {
        match cd {
            CallbackData::Message(token, msg) => match msg {
                Message::WebsocketPayload(data) => match token {
                    BN_FUNDING_RATE_TOKEN => {
                        if let Some((symbol, funding_fee)) =
                            bn_futures_funding::parse_funding_fee(data.as_ref())
                        {
                            let curreny = UnifiedCurrency::from_symbol(&symbol).unwrap();
                            unsafe {
                                BN_FUNDING_FEE[curreny as usize] = funding_fee;
                            }
                        }
                    }
                    GATE_FUNDING_RATE_TOKEN => {
                        if let Some((symbol, funding_fee)) =
                            gate_futures_funding::parse_funding_fee(data.as_ref())
                        {
                            let curreny = UnifiedCurrency::from_symbol(&symbol).unwrap();
                            unsafe {
                                GATE_FUNDING_FEE[curreny as usize] = funding_fee;
                            }
                        }
                    }
                    BN_TRADE_TOKEN_1 => {
                        if let Some(agg_trade) = parse_futures_agg_trade(data.as_ref()) {
                            if let Some(result) = engine.update_bn_agg_trade(&agg_trade) {
                                if result.len() > 0 {
                                    handle.send_message(GATE_ORDER_TOKEN_1, result)?;
                                }
                            }
                        } else {
                            info!(
                                "failed to parse agg trade: {}",
                                String::from_utf8_lossy(data.as_ref())
                            );
                        }
                    }
                    BN_ORDERBOOK_SNAPSHOT_TOKEN_1 => {
                        if let Some(depth) = parse_futures_orderbook_snapshot_as_bbo(data.as_ref())
                        {
                            if let Some(result) = engine.update_bn_bbo(&depth) {
                                handle.send_message(GATE_ORDER_TOKEN_1, result)?;
                            }
                        } else {
                            info!("failed to parse orderbook snapshot");
                        }
                    }
                    GATE_ORDERBOOK_TOKEN_1 => {
                        let mut copied_data = data.to_vec();
                        if let Some(ob) = gate_orderbook::parse_obu::<MAX_LEVELS>(&mut copied_data)
                        {
                            if !engine.apply_gate_orderbook(&ob) {
                                handle.reconnect_connection(token)?;
                                return Ok(());
                            }
                            if let Some(orders) = engine.update_gate_orderbook(ob) {
                                for order in orders.into_iter() {
                                    handle.send_message(BN_ORDER_TOKEN, order)?;
                                }
                            }
                        } else {
                            crate::info!(
                                "failed to parse orderbook: {}",
                                String::from_utf8_lossy(data.as_ref())
                            );
                        }
                    }
                    GATE_TRADE_TOKEN_1 => {
                        if let Some(trades) = gate::parse_trades(data.as_ref()) {
                            for trade in trades {
                                if let Some(result) = engine.update_gate_trade(trade) {
                                    for order in result.into_iter() {
                                        handle.send_message(BN_ORDER_TOKEN, order)?;
                                    }
                                }
                            }
                        } else {
                            crate::info!(
                                "failed to parse trades: {}",
                                String::from_utf8_lossy(data.as_ref())
                            );
                        }
                    }
                    GATE_BBO_TOKEN_1 | GATE_BBO_TOKEN_2 => match gate::decode_bbo(data.as_ref()) {
                        Some(bt) => {
                            if let Some(orders) = engine.update_gate_bbo(&bt) {
                                for order in orders.into_iter() {
                                    handle.send_message(BN_ORDER_TOKEN, order)?;
                                }
                            }
                        }
                        None => {
                            libwebsocket_rs::debug!(
                                "failed to parse gate bbo: {}",
                                String::from_utf8_lossy(data.as_ref())
                            );
                        }
                    },
                    BN_BBO_TOKEN_1 => {
                        if let Some(bt) = parse_futures_bookticker(data.as_ref()) {
                            if let Some(result) = engine.update_bn_bbo(&bt) {
                                if result.len() > 0 {
                                    handle.send_message(GATE_ORDER_TOKEN_1, result)?;
                                }
                            }
                        } else {
                            info!(
                                "failed to parse bbo: {}",
                                String::from_utf8_lossy(data.as_ref())
                            );
                        }
                        watch_dog.update(handle)?;
                    }
                    GATE_ORDER_TOKEN_1 => match gate::parse_order_place(data.as_ref()) {
                        Some(msg) => match msg {
                            GateWebsocketRsp::OrderPlace(msg) => match msg {
                                OrderPlaceMsg::Ack {
                                    request_id: _,
                                    header: _,
                                    req_param: _,
                                } => {}
                                OrderPlaceMsg::Response {
                                    request_id: _req_id,
                                    header: _header,
                                    order: _rsp,
                                } => {}
                                OrderPlaceMsg::Error {
                                    request_id: _req_id,
                                    header: _header,
                                    err: _err,
                                } => {
                                    error!("gate order place error: {:?}", _err);
                                    flush_logs!();
                                }
                            },
                            GateWebsocketRsp::CancelCp(rsp) => {
                                for order in rsp.data.result {
                                    let symbol = order.contract;
                                    crate::info!("canceled order: {}", symbol);
                                    engine.update_cancel_order(&symbol);
                                }
                                if let Some(err) = rsp.data.errs {
                                    crate::info!("cancel order err: {:?}", err);
                                }
                                crate::flush_logs!();
                            }
                            GateWebsocketRsp::OrderStatus(s) => {
                                crate::error!("order status: {:?}", s);
                            }
                        },
                        None => {
                            libwebsocket_rs::info!(
                                "failed to parse order place response: {}",
                                String::from_utf8_lossy(data.as_ref())
                            );
                        }
                    },
                    GATE_ORDER_SUB_TOKEN => {
                        let start = utils::perf::now();
                        if let Some(orders) = gate::parse_order_update(data.as_ref()) {
                            for order in orders {
                                let status = gate::unify_gate_status(
                                    order.status.as_ref(),
                                    &order.finish_as,
                                    order.left,
                                    order.size,
                                    &order.tif,
                                );

                                // Precompute frequently used fields to avoid repeated casts/borrows
                                let contract = &order.contract;
                                let order_id = order.id;
                                let size_f = order.size as f64;
                                let price = order.fill_price.unwrap_or(0.0);
                                engine.update_gate_order_status(
                                    contract, order_id, status, price, size_f,
                                );
                            }
                        } else {
                            libwebsocket_rs::debug!(
                                "order sub response: {}",
                                String::from_utf8_lossy(data.as_ref())
                            );
                        }
                        libwebsocket_rs::info!(
                            "order sub process latency: {:.1}",
                            circles_to_ns(utils::perf::now() - start)
                        );
                    }
                    BN_ORDER_TOKEN => {
                        flush_logs!();
                        if !listen_key_created {
                            listen_key_created = true;
                            handle
                                .send_message(token, generate_futures_user_data_start_request())?;
                            crate::info!("creating listen key");
                            flush_logs!();
                            return Ok(());
                        }
                        match parse_futures_order_response(data.as_ref()) {
                            Some(FuturesOrderResponse::ListenKey(key)) => {
                                if !bn_user_data_stream_connected {
                                    info!("listen key: {}", key);
                                    flush_logs!();
                                    let url = generate_futures_user_data_stream_url(key);
                                    handle.connect(url.into(), BN_USER_DATA_TOKEN, None)?;
                                }
                            }
                            Some(FuturesOrderResponse::Error(order_error)) => {
                                crate::info!("order response error: {}", order_error);
                                if let Some(id) = order_error.id {
                                    if order_error.error.contains("-2011") {
                                        if let Some(result) = engine.update_bn_order_status(
                                            OrderStatus::Filled,
                                            "",
                                            id,
                                            0.0,
                                            0.0,
                                            OrderSide::Buy,
                                        ) {
                                            handle.send_message(GATE_ORDER_TOKEN_1, result.1)?;
                                        }
                                    } else {
                                        engine.cancel_bn_order(id);
                                    }
                                }
                            }
                            Some(FuturesOrderResponse::OrderResponse(
                                order_id,
                                order_status,
                                symbol,
                                side,
                            )) => {
                                engine.update_bn_order_status(
                                    order_status,
                                    &symbol,
                                    order_id,
                                    0.0,
                                    0.0,
                                    side,
                                );
                                if engine.stopped() {
                                    if engine.all_bn_order_canceled() {
                                        handle.stop();
                                    }
                                }
                            }
                            Some(FuturesOrderResponse::Unkown(id)) => {
                                crate::info!(
                                    "order response unkown: {:?}",
                                    String::from_utf8_lossy(data.as_ref())
                                );
                                if let Some(id) = id {
                                    engine.cancel_bn_order(id);
                                }
                            }
                            None => {
                                crate::info!(
                                    "order response is none: {}",
                                    String::from_utf8_lossy(data.as_ref())
                                );
                            }
                        }
                        flush_logs!();
                    }
                    BN_USER_DATA_TOKEN => {
                        if let Some(order_trade_update) = parse_user_data_response(data.as_ref()) {
                            match order_trade_update {
                                UserDataResponse::OrderTradeUpdate(order_trade_update) => {
                                    if let Some(result) = engine.update_bn_order_status(
                                        order_trade_update.order_status,
                                        &order_trade_update.symbol,
                                        order_trade_update.order_id,
                                        order_trade_update.price,
                                        order_trade_update.last_filled,
                                        order_trade_update.order_side,
                                    ) {
                                        handle.send_message(GATE_ORDER_TOKEN_1, result.1)?;
                                    }
                                    if engine.stopped() {
                                        if engine.all_bn_order_canceled() {
                                            handle.stop();
                                        }
                                    }
                                }
                                UserDataResponse::AccountUpdate(_) => {}
                            }
                        }
                    }
                    _ => (),
                },
                Message::HttpResponse(response) => {
                    if response.status.unwrap() != StatusCode::OK || response.body.is_none() {
                        crate::info!("position snapshot request failed {:?}", response);
                        return Ok(());
                    }
                    let body = response.body.as_ref().unwrap();
                    match token {
                        BN_POSITION_SNAPSHOT_TOKEN => {
                            if let Some(positions) = parse_position_risk_json(body.as_ref()) {
                                for pos in positions {
                                    crate::info!(
                                        "bn position {} {} {}",
                                        pos.symbol,
                                        pos.position_amt,
                                        pos.entry_price
                                    );
                                    let signed_qty: f64 = pos.position_amt;
                                    let side = if signed_qty > 0.0 {
                                        OrderSide::Buy
                                    } else {
                                        OrderSide::Sell
                                    };
                                    let qty = signed_qty.abs();
                                    let index = match UnifiedCurrency::from_symbol(&pos.symbol) {
                                        Some(currency) => currency as usize,
                                        None => {
                                            crate::error!("unknown symbol: {}", pos.symbol);
                                            continue;
                                        }
                                    };
                                    HedgeStatus::init(index, pos.entry_price, qty, side, true);
                                }
                                flush_logs!();
                                engine.bn_position_inited();
                            } else {
                                crate::info!(
                                    "failed to parse position snapshot: {}",
                                    String::from_utf8_lossy(body.as_ref())
                                );
                            }
                        }
                        GATE_POSITION_SNAPSHOT_TOKEN => {
                            if let Some(positions) = gate::parse_positions(body.as_ref()) {
                                for pos in positions {
                                    let size = match pos.size {
                                        Some(size) => size,
                                        None => continue,
                                    };
                                    if size == 0 {
                                        continue;
                                    }
                                    crate::info!("gate position {} {}", pos.contract, size);
                                    let index = match UnifiedCurrency::from_symbol(&pos.contract) {
                                        Some(currency) => currency as usize,
                                        None => {
                                            crate::error!("unknown symbol: {}", pos.contract);
                                            continue;
                                        }
                                    };
                                    let signed_qty = size as f64 * GATE_QUANTO_MULTIPLIER[index];
                                    let side = if signed_qty > 0.0 {
                                        OrderSide::Buy
                                    } else {
                                        OrderSide::Sell
                                    };
                                    let qty = signed_qty.abs();
                                    let entry_price = match pos.entry_price {
                                        Some(price) => match price.parse::<f64>() {
                                            Ok(price) => price,
                                            Err(_) => 0.0,
                                        },
                                        None => 0.0,
                                    };
                                    HedgeStatus::init(index, entry_price, qty, side, false);
                                }
                            } else {
                                crate::info!(
                                    "failed to parse gate position snapshot: {}",
                                    String::from_utf8_lossy(body.as_ref())
                                );
                            }
                            flush_logs!();
                            engine.gate_position_inited();
                        }
                        _ => (),
                    }
                }
                _ => (),
            },
            CallbackData::ConnectionOpen(token) => match token {
                GATE_BBO_TOKEN_1 | GATE_BBO_TOKEN_2 => {
                    let req = gate::generate_bbo_subscribe_request();
                    info!("gate bbo subscribe request: {}", req);
                    handle.send_message(token, req)?;
                    watch_dog.add_connection(token, 50, ConnectionType::GateBbo);
                }
                BN_BBO_TOKEN_1 | BN_BBO_TOKEN_2 => {
                    info!("bn bbo connected");
                }
                GATE_TRADE_TOKEN_1 => {
                    let req = gate::generate_trade_subscribe_request();
                    info!("trade subscribe request: {}", req);
                    handle.send_message(token, req)?;
                    watch_dog.add_connection(token, 50, ConnectionType::GateBbo);
                }
                GATE_ORDERBOOK_TOKEN_1 => {
                    let req = generate_orderbook_sub_request();
                    info!("orderbook subscribe request: {}", req);
                    handle.send_message(token, req)?;
                    watch_dog.add_connection(token, 50, ConnectionType::GateBbo);
                }
                BN_FUNDING_RATE_TOKEN => {
                    info!("bn funding rate connected");
                }
                GATE_FUNDING_RATE_TOKEN => {
                    info!("gate funding rate connected");
                    let req = gate_futures_funding::generate_funding_sub_request(GATE_BASE_ASSETS);
                    info!("funding sub request: {}", req);
                    handle.send_message(token, req)?;
                }
                BN_TRADE_TOKEN_1 => {
                    info!("bn trade connected");
                }
                GATE_ORDER_TOKEN_1 => {
                    info!("gate order connection opened {:?}", token);
                    let req = gate::generate_login_request();
                    info!("login request: {}", req);
                    crate::flush_logs!();
                    engine.set_should_check_all_order_status(true);
                    handle.send_message(token, gate::generate_login_request())?;
                    watch_dog.add_connection(token, 50, ConnectionType::GateOrder);
                }
                GATE_ORDER_SUB_TOKEN => {
                    let req = gate::generate_order_sub_request();
                    info!("order sub request: {}", req);
                    crate::flush_logs!();
                    handle.send_message(token, req)?;
                    watch_dog.add_connection(token, 50, ConnectionType::GateUserData);
                }
                BN_ORDER_TOKEN => {
                    info!("Order connection opened: {:?}", token);
                    let req = generate_futures_session_logon_request();
                    handle.send_message(token, req)?;
                }
                BN_USER_DATA_TOKEN => {
                    watch_dog.add_connection(
                        BN_ORDER_TOKEN,
                        50,
                        ConnectionType::BinanceFuturesUserData,
                    );
                    bn_user_data_stream_connected = true;
                    engine.bn_user_stream_connected();
                    info!("user data stream connected");
                    flush_logs!();
                }
                BN_POSITION_SNAPSHOT_TOKEN => {
                    if engine.stopped() {
                        info!("bn position snapshot connected");
                        let req = generate_futures_position_snapshot_request();
                        info!("position snapshot request: {:?}", req);
                        handle.send_message(token, req)?;
                    }
                }
                GATE_POSITION_SNAPSHOT_TOKEN => {
                    if engine.stopped() {
                        info!("gate position snapshot connected");
                        let req = generate_position_snapshot_request();
                        info!("position snapshot request: {:?}", req);
                        handle.send_message(token, req)?;
                    }
                }
                BN_ORDERBOOK_SNAPSHOT_TOKEN_1 => {
                    info!("orderbook snapshot connected");
                }
                _ => (),
            },
            CallbackData::ConnectionClose(token, err) => {
                info!("connection close: {:?} {:?}", token, err);
                flush_logs!();
                match token {
                    BN_ORDER_TOKEN => {
                        listen_key_created = false;
                        bn_user_data_stream_connected = false;
                    }
                    BN_USER_DATA_TOKEN => {
                        listen_key_created = false;
                        bn_user_data_stream_connected = false;
                    }
                    GATE_ORDER_TOKEN_1 => {}
                    _ => {}
                }
            }
            CallbackData::ConnectionError(token, error) => {
                error!("connection err: {:?}: {:?}", token, error);
                flush_logs!();
            }
            CallbackData::WakerWake(_) => {
                info!("waker wake");
                engine.stop();
                let all_cancel_orders = engine.cancel_all_bn_orders();
                for order in all_cancel_orders {
                    handle.send_message(BN_ORDER_TOKEN, order)?;
                }
            }
        }
        Ok(())
    };
    let mut settings = Settings::default();
    settings.event_loop_timeout = Some(std::time::Duration::from_millis(100));
    let mut websocket = WebSocket::new(settings, callback)?;

    let gate_bbo_url: Url = generate_bbo_url().into();
    info!("gate bbo url: {}", gate_bbo_url);
    websocket.connect(gate_bbo_url.clone(), GATE_BBO_TOKEN_1)?;
    websocket.connect(gate_bbo_url.clone(), GATE_BBO_TOKEN_2)?;

    let gate_trades_url: Url = "wss://fx-ws.gateio.ws/v4/ws/usdt".into();
    info!("gate trades url: {}", gate_trades_url);
    websocket.connect(gate_trades_url.clone(), GATE_TRADE_TOKEN_1)?;

    let gate_orderbook_url: Url = "wss://fx-ws.gateio.ws/v4/ws/usdt".into();
    info!("gate orderbook url: {}", gate_orderbook_url);
    websocket.connect(gate_orderbook_url.clone(), GATE_ORDERBOOK_TOKEN_1)?;

    let bn_bbo_url: Url = generate_futures_book_ticker_url(GATE_BASE_ASSETS).into();
    info!("bbo url: {}", bn_bbo_url);
    websocket.connect(bn_bbo_url.clone(), BN_BBO_TOKEN_1)?;

    let bn_funding_url: Url = generate_funding_sub_url(GATE_BASE_ASSETS).into();
    info!("bn funding url: {}", bn_funding_url);
    websocket.connect(bn_funding_url.clone(), BN_FUNDING_RATE_TOKEN)?;

    let gate_funding_url: Url = "wss://fx-ws.gateio.ws/v4/ws/usdt".into();
    info!("gate funding url: {}", gate_funding_url);
    websocket.connect(gate_funding_url.clone(), GATE_FUNDING_RATE_TOKEN)?;

    let gate_order_url: Url = generate_bbo_url().into();
    info!("order url: {}", gate_order_url);
    websocket.connect(gate_order_url.clone(), GATE_ORDER_TOKEN_1)?;
    websocket.connect(gate_order_url.clone(), GATE_ORDER_SUB_TOKEN)?;

    let order_url: Url = generate_futures_order_url().into();
    info!("order url: {}", order_url);
    websocket.connect(order_url.clone(), BN_ORDER_TOKEN)?;

    let bn_http_url = "https://fapi.binance.com";
    websocket.connect(bn_http_url, BN_POSITION_SNAPSHOT_TOKEN)?;

    let gate_http_url = "https://api.gateio.ws";
    websocket.connect(gate_http_url, GATE_POSITION_SNAPSHOT_TOKEN)?;

    let agg_trade_url: Url = generate_futures_agg_trade_url_by_symbols(GATE_BASE_ASSETS).into();
    websocket.connect(agg_trade_url.clone(), BN_TRADE_TOKEN_1)?;

    let depth_snapshot_url: Url = generate_futures_depth_snapshot_5_url(GATE_BASE_ASSETS).into();
    info!("depth snapshot url: {}", depth_snapshot_url);
    websocket.connect(depth_snapshot_url.clone(), BN_ORDERBOOK_SNAPSHOT_TOKEN_1)?;

    let waker = websocket.create_waker(STOP_WAKER_TOKEN);

    match ctrlc::set_handler(move || {
        libwebsocket_rs::info!("Ctrl-C pressed, exiting");
        waker.wake().expect("Failed to wake waker");
    }) {
        Ok(_) => (),
        Err(e) => {
            libwebsocket_rs::error!("Failed to set Ctrl-C handler: {:?}", e);
            libwebsocket_rs::flush_logs!();
        }
    }

    match websocket.run() {
        Ok(_) => (),
        Err(e) => {
            libwebsocket_rs::error!("Websocket run error: {:?}", e);
            libwebsocket_rs::flush_logs!();
        }
    }
    Ok(())
}
