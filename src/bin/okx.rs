use libwebsocket_rs::{
    CallbackD<PERSON>, Message, Result, Settings, WebSocket, WebSocketHandle,
    configs::{GATE_IN_LEN, GATE_OUT_LEN},
    encoding::{
        agg_trades::parse_futures_agg_trade_as_bbo,
        binance_futures::{
            generate_futures_position_snapshot_request, generate_futures_session_logon_request,
            generate_futures_user_data_start_request, generate_futures_user_data_stream_url,
        },
        bn_futures_funding::{self, generate_funding_sub_url},
        bn_futures_order::{OrderSide, generate_futures_order_ioc_request},
        bn_gate_model::UnifiedOrderStatus,
        book_ticker::parse_futures_bookticker,
        futures_order_response::{
            FuturesOrderResponse, OrderStatus, UserDataResponse, parse_futures_order_response,
            parse_position_snapshot, parse_user_data_response,
        },
        futures_orderbook::parse_futures_orderbook_snapshot_as_bbo,
        gate::{self, Gate<PERSON>ebsocketRsp, OrderPlaceMsg, generate_position_snapshot_request},
        gate_futures_funding,
        okx::{self, OKX_PUB_URL},
    },
    engine::{
        arbitrage_engine_gate::ArbitrageGate,
        binance::{
            generate_futures_agg_trade_url_by_symbols, generate_futures_book_ticker_url,
            generate_futures_depth_snapshot_5_url, generate_futures_order_url,
        },
        cross_exchange::{
            gate_const::{
                BN_BBO, BN_FUNDING_FEE, BN_PRICE_TICK, GATE_BASE_ASSETS, GATE_FUNDING_FEE,
                GATE_QUANTO_MULTIPLIER, UnifiedCurrency,
            },
            hedge_manager::HedgeStatus,
            order_manager::OrderCancelReason,
            token::*,
        },
        net_utils::{
            connection_rotator::ConnectionRotator,
            connection_watch_dog::{ConnectionType, ConnectionWatchDog},
        },
    },
    error, flush_logs, info,
    net::{message::http::StatusCode, utils::url::Url},
    utils::{self, perf::circles_to_ns},
};
use std::env;

const GATE_WS_URL: &str = "wss://fx-ws.gateio.ws/v4/ws/usdt";

fn generate_bbo_url() -> String {
    GATE_WS_URL.to_string()
}

fn print_usage() {
    info!("Usage: gate [OPTIONS]\n");
    info!("Options:\n");
    info!("  --stdout         Enable stdout logging (logs will be printed to console)\n");
    info!("  --help, -h       Show this help message\n");
}

fn main() -> std::result::Result<(), Box<dyn std::error::Error>> {
    let args: Vec<String> = env::args().collect();

    // 解析命令行参数
    let mut enable_stdout = false; // 默认不启用stdout日志

    for arg in args.iter().skip(1) {
        match arg.as_str() {
            "--stdout" => {
                enable_stdout = true;
            }
            "--help" | "-h" => {
                print_usage();
                return Ok(());
            }
            _ => {
                libwebsocket_rs::error!("Unknown argument: {}", arg);
                print_usage();
                return Err("Invalid arguments".into());
            }
        }
    }

    // 启用stdout日志输出（如果指定了--stdout参数）
    libwebsocket_rs::utils::logger::enable_stdout_logging(enable_stdout);
    let mut engine = ArbitrageGate::new();
    let mut listen_key_created = false;
    let mut watch_dog = ConnectionWatchDog::new();
    let mut gate_order_connections = ConnectionRotator::<10>::new();
    let mut bn_user_data_stream_connected = false;
    let mut last_test_bn_order_send_time = 0;
    let callback = move |handle: &mut WebSocketHandle<GATE_IN_LEN, GATE_OUT_LEN>,
                         cd: CallbackData|
          -> Result<()> {
        match cd {
            CallbackData::Message(token, msg) => match msg {
                Message::WebsocketPayload(data) => match token {
                    BN_FUNDING_RATE_TOKEN => {
                        if let Some((symbol, funding_fee)) =
                            bn_futures_funding::parse_funding_fee(data.as_ref())
                        {
                            let curreny = UnifiedCurrency::from_symbol(&symbol).unwrap();
                            unsafe {
                                BN_FUNDING_FEE[curreny as usize] = funding_fee;
                            }
                        }
                    }
                    GATE_FUNDING_RATE_TOKEN => {
                        if let Some((symbol, funding_fee)) =
                            gate_futures_funding::parse_funding_fee(data.as_ref())
                        {
                            let curreny = UnifiedCurrency::from_symbol(&symbol).unwrap();
                            unsafe {
                                GATE_FUNDING_FEE[curreny as usize] = funding_fee;
                            }
                        }
                    }
                    OKX_BBO_TOKEN => match okx::parse_bbo(data.as_ref()) {
                        Some(bt) => {
                            if let Some(hedge_order) = engine.update_gate_bbo(&bt) {
                                handle.send_message(BN_ORDER_TOKEN, hedge_order)?;
                            }
                        }
                        None => {
                            libwebsocket_rs::debug!(
                                "failed to parse gate bbo: {}",
                                String::from_utf8_lossy(data.as_ref())
                            );
                        }
                    },
                    BN_TRADE_TOKEN_1 => {
                        if let Some(mut trade_bbo) = parse_futures_agg_trade_as_bbo(data.as_ref()) {
                            let index =
                                UnifiedCurrency::from_symbol(&trade_bbo.symbol).unwrap() as usize;
                            let bbo = unsafe { BN_BBO[index] };
                            if trade_bbo.transaction_time > bbo.transaction_time {
                                let price_tick = BN_PRICE_TICK[index];
                                trade_bbo.bid_price -= price_tick;
                                trade_bbo.ask_price += price_tick;
                                trade_bbo.update_id = bbo.book_update_id + 1;
                                if let Some(result) = engine.update_bn_bbo(&trade_bbo) {
                                    if result.len() > 0 {
                                        for order in result.into_iter() {
                                            handle.send_message(
                                                gate_order_connections.next().unwrap(),
                                                order,
                                            )?;
                                        }
                                    }
                                }
                            }
                        } else {
                            info!(
                                "failed to parse agg trade: {}",
                                String::from_utf8_lossy(data.as_ref())
                            );
                        }
                    }
                    BN_BBO_TOKEN_1 | BN_BBO_TOKEN_2 => {
                        if let Some(bt) = parse_futures_bookticker(data.as_ref()) {
                            if let Some(result) = engine.update_bn_bbo(&bt) {
                                if result.len() > 0 {
                                    for order in result.into_iter() {
                                        handle.send_message(
                                            gate_order_connections.next().unwrap(),
                                            order,
                                        )?;
                                    }
                                }
                            }
                        } else {
                            info!(
                                "failed to parse bbo: {}",
                                String::from_utf8_lossy(data.as_ref())
                            );
                        }
                        watch_dog.update(handle)?;
                        if circles_to_ns(utils::perf::now() - last_test_bn_order_send_time)
                            > 1_000_000_000.0
                        {
                            last_test_bn_order_send_time = utils::perf::now();
                            let price = unsafe { BN_BBO[0].bid_price * 0.95 };
                            let symbol = UnifiedCurrency::from_usize(0).unwrap().to_bn_symbol();
                            let qty = 50.0 / price;
                            let test_order_req = generate_futures_order_ioc_request(
                                price,
                                OrderSide::Buy,
                                symbol,
                                last_test_bn_order_send_time,
                                qty,
                            );
                            libwebsocket_rs::debug!("test order request: {}", test_order_req);
                            handle.send_message(BN_ORDER_TOKEN, test_order_req)?;
                        }
                    }
                    BN_ORDERBOOK_SNAPSHOT_TOKEN_1 | BN_ORDERBOOK_SNAPSHOT_TOKEN_2 => {
                        if let Some(depth) = parse_futures_orderbook_snapshot_as_bbo(data.as_ref())
                        {
                            if let Some(result) = engine.update_bn_bbo(&depth) {
                                if result.len() > 0 {
                                    for order in result.into_iter() {
                                        handle.send_message(
                                            gate_order_connections.next().unwrap(),
                                            order,
                                        )?;
                                    }
                                }
                            }
                        } else {
                            info!("failed to parse orderbook snapshot");
                        }
                    }
                    GATE_ORDER_TOKEN_1 | GATE_ORDER_TOKEN_2 | GATE_ORDER_TOKEN_3
                    | GATE_ORDER_TOKEN_4 | GATE_ORDER_TOKEN_5 | GATE_ORDER_TOKEN_6
                    | GATE_ORDER_TOKEN_7 | GATE_ORDER_TOKEN_8 | GATE_ORDER_TOKEN_9
                    | GATE_ORDER_TOKEN_10 => match gate::parse_order_place(data.as_ref()) {
                        Some(msg) => match msg {
                            GateWebsocketRsp::OrderPlace(msg) => match msg {
                                OrderPlaceMsg::Ack {
                                    request_id: _,
                                    header,
                                    req_param: _,
                                } => {
                                    engine.reset_rate_limiter(
                                        header.x_gate_ratelimit_requests_remain,
                                        header.x_gate_ratelimit_limit,
                                        header.x_gat_ratelimit_reset_timestamp,
                                    );
                                }
                                OrderPlaceMsg::Response {
                                    request_id: req_id,
                                    header: _header,
                                    order: rsp,
                                } => {
                                    let status = gate::unify_gate_status(
                                        rsp.status.as_ref(),
                                        &rsp.finish_as,
                                        rsp.left,
                                        rsp.size,
                                        &rsp.tif,
                                    );
                                    let order_id = rsp.id;
                                    let req_id = match req_id.parse::<u64>() {
                                        Ok(id) => id,
                                        Err(_) => {
                                            crate::error!("parse req id error: {}", req_id);
                                            return Ok(());
                                        }
                                    };
                                    if status == UnifiedOrderStatus::Canceled
                                        || status == UnifiedOrderStatus::Expired
                                    {
                                        if let Some(result) = engine.cancel_gate_order(
                                            &rsp.contract,
                                            order_id,
                                            req_id,
                                            rsp.size as f64,
                                            OrderCancelReason::Other,
                                        ) {
                                            if result.0 {
                                                handle.send_message(BN_ORDER_TOKEN, result.1)?;
                                            } else {
                                                handle.send_message(
                                                    gate_order_connections.next().unwrap(),
                                                    result.1,
                                                )?;
                                            }
                                        }
                                    } else if let Some(hedge_order) = engine
                                        .update_gate_order_status(
                                            status,
                                            &rsp.contract,
                                            order_id,
                                            req_id,
                                            rsp.price,
                                            rsp.size as f64,
                                            rsp.left,
                                        )
                                    {
                                        handle.send_message(BN_ORDER_TOKEN, hedge_order.1)?;
                                    }
                                }
                                OrderPlaceMsg::Error {
                                    request_id: req_id,
                                    header,
                                    err,
                                } => {
                                    let req_id = match req_id.parse::<u64>() {
                                        Ok(id) => id,
                                        Err(_) => {
                                            crate::error!("parse req id error: {}", req_id);
                                            return Ok(());
                                        }
                                    };
                                    let err_type = if err.message.contains("ORDER_POC_IMMEDIATE") {
                                        crate::info!("ORDER_POC_IMMEDIATE: {:?}", err);
                                        OrderCancelReason::MakerFailed
                                    } else if err.message.contains("ORDER_NOT_FOUND")
                                        || err.label.contains("ORDER_NOT_FOUND")
                                    {
                                        OrderCancelReason::NotFound
                                    } else if err.label.contains("TOO_MANY_REQUESTS") {
                                        crate::error!("too many requests{:?} {:?}", err, header);
                                        OrderCancelReason::TooManyRequests
                                    } else {
                                        error!("order place error: {} {:?}", req_id, err);
                                        OrderCancelReason::Other
                                    };

                                    if let Some(result) =
                                        engine.cancel_gate_order("", 1, req_id, 0.0, err_type)
                                    {
                                        if result.0 {
                                            handle.send_message(BN_ORDER_TOKEN, result.1)?;
                                        } else {
                                            handle.send_message(
                                                gate_order_connections.next().unwrap(),
                                                result.1,
                                            )?;
                                        }
                                    }
                                }
                            },
                            GateWebsocketRsp::CancelCp(rsp) => {
                                for order in rsp.data.result {
                                    let symbol = order.contract;
                                    crate::info!("canceled order: {}", symbol);
                                    engine.update_cancel_order(&symbol);
                                }
                                if let Some(err) = rsp.data.errs {
                                    crate::info!("cancel order err: {:?}", err);
                                }
                                crate::flush_logs!();
                                if engine.all_order_canceled() {
                                    handle.stop();
                                } else {
                                    let all_cancel_orders = engine.cancel_all_orders();
                                    for order in all_cancel_orders {
                                        handle.send_message(
                                            gate_order_connections.next().unwrap(),
                                            order,
                                        )?;
                                    }
                                }
                            }
                            GateWebsocketRsp::OrderStatus(s) => {
                                crate::error!("order status: {:?}", s);
                            }
                        },
                        None => {
                            libwebsocket_rs::info!(
                                "failed to parse order place response: {}",
                                String::from_utf8_lossy(data.as_ref())
                            );
                        }
                    },
                    GATE_ORDER_SUB_TOKEN => {
                        if let Some(orders) = gate::parse_order_update(data.as_ref()) {
                            for order in orders {
                                let status = gate::unify_gate_status(
                                    order.status.as_ref(),
                                    &order.finish_as,
                                    order.left,
                                    order.size,
                                    &order.tif,
                                );

                                // Precompute frequently used fields to avoid repeated casts/borrows
                                let contract = &order.contract;
                                let order_id = order.id;
                                let size_f = order.size as f64;

                                // Unify the two branches to reduce branching and duplicate work
                                let result = if status == UnifiedOrderStatus::Canceled
                                    || status == UnifiedOrderStatus::Expired
                                {
                                    engine.cancel_gate_order(
                                        contract,
                                        order_id,
                                        0,
                                        size_f,
                                        OrderCancelReason::Other,
                                    )
                                } else {
                                    engine.update_gate_order_status(
                                        status,
                                        contract,
                                        order_id,
                                        0,
                                        order.price,
                                        size_f,
                                        order.left,
                                    )
                                };

                                if let Some(result) = result {
                                    let token = if result.0 {
                                        BN_ORDER_TOKEN
                                    } else {
                                        gate_order_connections.next().unwrap()
                                    };
                                    handle.send_message(token, result.1)?;
                                }
                            }
                        } else {
                            libwebsocket_rs::debug!(
                                "order sub response: {}",
                                String::from_utf8_lossy(data.as_ref())
                            );
                        }
                    }
                    GATE_USER_TRADE_SUB_TOKEN => {
                        if let Some(trades) = gate::parser_user_trade(data.as_ref()) {
                            if let Some(result) = engine.update_gate_user_trade(&trades) {
                                if result.0 {
                                    handle.send_message(BN_ORDER_TOKEN, result.1)?;
                                } else {
                                    handle.send_message(
                                        gate_order_connections.next().unwrap(),
                                        result.1,
                                    )?;
                                }
                            }
                        } else {
                            crate::info!(
                                "failed to parse user trade: {}",
                                String::from_utf8_lossy(data.as_ref())
                            );
                        }
                    }
                    BN_ORDER_TOKEN => {
                        if !listen_key_created {
                            handle
                                .send_message(token, generate_futures_user_data_start_request())?;
                        }
                        match parse_futures_order_response(data.as_ref()) {
                            Some(FuturesOrderResponse::ListenKey(key)) => {
                                if !bn_user_data_stream_connected {
                                    listen_key_created = true;
                                    info!("listen key: {}", key);
                                    let url = generate_futures_user_data_stream_url(key);
                                    handle.connect(url.into(), BN_USER_DATA_TOKEN, None)?;
                                }
                            }
                            Some(FuturesOrderResponse::Error(order_error)) => {
                                crate::info!("order response error: {}", order_error);
                                if let Some(id) = order_error.id {
                                    engine.cancel_bn_order_status_by_id(id);
                                }
                            }
                            Some(FuturesOrderResponse::OrderResponse(
                                order_id,
                                order_status,
                                _,
                                _,
                            )) => {
                                if order_status == OrderStatus::Canceled {
                                    crate::info!(
                                        "bn latency order cancel: {:.1}",
                                        circles_to_ns(utils::perf::now() - order_id)
                                    );
                                    engine.cancel_bn_order_status_by_id(order_id);
                                    crate::info!("order canceled: {}", order_id);
                                }
                            }
                            Some(FuturesOrderResponse::Unkown(_id)) => {
                                crate::info!(
                                    "order response unkown: {:?}",
                                    String::from_utf8_lossy(data.as_ref())
                                );
                            }
                            None => {
                                crate::info!(
                                    "order response is none: {}",
                                    String::from_utf8_lossy(data.as_ref())
                                );
                            }
                        }
                    }
                    BN_USER_DATA_TOKEN => {
                        if let Some(order_trade_update) = parse_user_data_response(data.as_ref()) {
                            match order_trade_update {
                                UserDataResponse::OrderTradeUpdate(order_trade_update) => {
                                    if order_trade_update.order_status == OrderStatus::Filled {
                                        engine.update_bn_order_status(&order_trade_update);
                                        crate::info!(
                                            "bn latency order filled: {:.1}",
                                            circles_to_ns(
                                                utils::perf::now() - order_trade_update.order_id
                                            )
                                        );
                                    }
                                }
                                UserDataResponse::AccountUpdate(_) => {}
                            }
                        }
                    }
                    _ => (),
                },
                Message::HttpResponse(response) => {
                    if response.status.unwrap() != StatusCode::OK || response.body.is_none() {
                        crate::info!("position snapshot request failed {:?}", response);
                        return Ok(());
                    }
                    let body = response.body.as_ref().unwrap();
                    match token {
                        BN_POSITION_SNAPSHOT_TOKEN => {
                            if let Some(positions) = parse_position_snapshot(body.as_ref()) {
                                for pos in positions.positions {
                                    crate::info!("bn position {} {}", pos.symbol, pos.positionAmt);
                                    let signed_qty: f64 = pos.positionAmt.parse().unwrap();
                                    let side = if signed_qty > 0.0 {
                                        OrderSide::Buy
                                    } else {
                                        OrderSide::Sell
                                    };
                                    let qty = signed_qty.abs();
                                    let index = match UnifiedCurrency::from_symbol(&pos.symbol) {
                                        Some(currency) => currency as usize,
                                        None => {
                                            crate::error!("unknown symbol: {}", pos.symbol);
                                            continue;
                                        }
                                    };
                                    HedgeStatus::init(index, 0.0, qty, side, true);
                                }
                            }
                            engine.bn_position_inited();
                        }
                        GATE_POSITION_SNAPSHOT_TOKEN => {
                            if let Some(positions) = gate::parse_positions(body.as_ref()) {
                                for pos in positions {
                                    let size = match pos.size {
                                        Some(size) => size,
                                        None => continue,
                                    };
                                    if size == 0 {
                                        continue;
                                    }
                                    crate::info!("gate position {} {}", pos.contract, size);
                                    let index = match UnifiedCurrency::from_symbol(&pos.contract) {
                                        Some(currency) => currency as usize,
                                        None => {
                                            crate::error!("unknown symbol: {}", pos.contract);
                                            continue;
                                        }
                                    };
                                    let signed_qty = size as f64 * GATE_QUANTO_MULTIPLIER[index];
                                    let side = if signed_qty > 0.0 {
                                        OrderSide::Buy
                                    } else {
                                        OrderSide::Sell
                                    };
                                    let qty = signed_qty.abs();
                                    let entry_price = match pos.entry_price {
                                        Some(price) => match price.parse::<f64>() {
                                            Ok(price) => price,
                                            Err(_) => 0.0,
                                        },
                                        None => 0.0,
                                    };
                                    HedgeStatus::init(index, entry_price, qty, side, false);
                                }
                            }
                            engine.gate_position_inited();
                        }
                        _ => (),
                    }
                }
                _ => (),
            },
            CallbackData::ConnectionOpen(token) => match token {
                OKX_BBO_TOKEN => {
                    let req = okx::generate_bbo_subscribe_request("BTC-USDT-SWAP");
                    info!("okx bbo subscribe request: {}", req);
                    handle.send_message(token, req)?;
                }
                BN_BBO_TOKEN_1 | BN_BBO_TOKEN_2 => {
                    info!("bn bbo connected");
                }
                BN_FUNDING_RATE_TOKEN => {
                    info!("bn funding rate connected");
                }
                GATE_FUNDING_RATE_TOKEN => {
                    info!("gate funding rate connected");
                    let req = gate_futures_funding::generate_funding_sub_request(GATE_BASE_ASSETS);
                    info!("funding sub request: {}", req);
                    handle.send_message(token, req)?;
                }
                GATE_ORDER_TOKEN_1 | GATE_ORDER_TOKEN_2 | GATE_ORDER_TOKEN_3
                | GATE_ORDER_TOKEN_4 | GATE_ORDER_TOKEN_5 | GATE_ORDER_TOKEN_6
                | GATE_ORDER_TOKEN_7 | GATE_ORDER_TOKEN_8 | GATE_ORDER_TOKEN_9
                | GATE_ORDER_TOKEN_10 => {
                    info!("gate order connection opened {:?}", token);
                    let req = gate::generate_login_request();
                    info!("login request: {}", req);
                    crate::flush_logs!();
                    engine.set_should_check_all_order_status(true);
                    handle.send_message(token, gate::generate_login_request())?;
                    watch_dog.add_connection(token, 50, ConnectionType::GateOrder);
                    gate_order_connections.add_connection(token);
                }
                GATE_ORDER_SUB_TOKEN => {
                    let req = gate::generate_order_sub_request();
                    info!("order sub request: {}", req);
                    crate::flush_logs!();
                    handle.send_message(token, req)?;
                    watch_dog.add_connection(token, 50, ConnectionType::GateUserData);
                }
                GATE_USER_TRADE_SUB_TOKEN => {
                    let req = gate::generate_user_trade_sub_request();
                    info!("user trade sub request: {}", req);
                    crate::flush_logs!();
                    handle.send_message(token, req)?;
                    watch_dog.add_connection(token, 50, ConnectionType::GateUserData);
                }
                BN_ORDER_TOKEN => {
                    info!("Order connection opened: {:?}", token);
                    let req = generate_futures_session_logon_request();
                    handle.send_message(token, req)?;
                    last_test_bn_order_send_time = utils::perf::now();
                }
                BN_USER_DATA_TOKEN => {
                    watch_dog.add_connection(
                        BN_ORDER_TOKEN,
                        50,
                        ConnectionType::BinanceFuturesUserData,
                    );
                    bn_user_data_stream_connected = true;
                    info!("user data stream connected");
                    flush_logs!();
                }
                BN_POSITION_SNAPSHOT_TOKEN => {
                    if engine.stopped() {
                        info!("bn position snapshot connected");
                        let req = generate_futures_position_snapshot_request();
                        info!("position snapshot request: {:?}", req);
                        handle.send_message(token, req)?;
                    }
                }
                GATE_POSITION_SNAPSHOT_TOKEN => {
                    if engine.stopped() {
                        info!("gate position snapshot connected");
                        let req = generate_position_snapshot_request();
                        info!("position snapshot request: {:?}", req);
                        handle.send_message(token, req)?;
                    }
                }
                BN_ORDERBOOK_SNAPSHOT_TOKEN_1 | BN_ORDERBOOK_SNAPSHOT_TOKEN_2 => {
                    info!("orderbook snapshot connected");
                }
                BN_TRADE_TOKEN_1 => {
                    info!("bn trade connected");
                }
                _ => (),
            },
            CallbackData::ConnectionClose(token, err) => {
                info!("connection close: {:?} {:?}", token, err);
                flush_logs!();
                match token {
                    BN_ORDER_TOKEN => {
                        listen_key_created = false;
                        bn_user_data_stream_connected = false;
                    }
                    BN_USER_DATA_TOKEN => {
                        listen_key_created = false;
                        bn_user_data_stream_connected = false;
                    }
                    GATE_ORDER_TOKEN_1 | GATE_ORDER_TOKEN_2 | GATE_ORDER_TOKEN_3
                    | GATE_ORDER_TOKEN_4 | GATE_ORDER_TOKEN_5 | GATE_ORDER_TOKEN_6
                    | GATE_ORDER_TOKEN_7 | GATE_ORDER_TOKEN_8 | GATE_ORDER_TOKEN_9
                    | GATE_ORDER_TOKEN_10 => {
                        gate_order_connections.remove_connection(token);
                    }
                    _ => {}
                }
            }
            CallbackData::ConnectionError(token, error) => {
                error!("connection err: {:?}: {:?}", token, error);
                flush_logs!();
            }
            CallbackData::WakerWake(_) => {
                info!("waker wake");
                engine.stop();
                let all_cancel_orders = engine.cancel_all_orders();
                for order in all_cancel_orders {
                    handle.send_message(gate_order_connections.next().unwrap(), order)?;
                }
            }
        }
        Ok(())
    };
    let mut settings = Settings::default();
    settings.event_loop_timeout = Some(std::time::Duration::from_millis(100));
    let mut websocket = WebSocket::new(settings, callback)?;

    let okx_bbo_url: Url = OKX_PUB_URL.into();
    info!("okx bbo url: {}", okx_bbo_url);
    websocket.connect(okx_bbo_url, OKX_BBO_TOKEN)?;

    let bn_bbo_url: Url = generate_futures_book_ticker_url(GATE_BASE_ASSETS).into();
    info!("bbo url: {}", bn_bbo_url);
    websocket.connect(bn_bbo_url.clone(), BN_BBO_TOKEN_1)?;
    websocket.connect(bn_bbo_url.clone(), BN_BBO_TOKEN_2)?;

    let gate_order_url: Url = generate_bbo_url().into();
    info!("order url: {}", gate_order_url);
    websocket.connect(gate_order_url.clone(), GATE_ORDER_TOKEN_1)?;
    websocket.connect(gate_order_url.clone(), GATE_ORDER_TOKEN_2)?;
    websocket.connect(gate_order_url.clone(), GATE_ORDER_TOKEN_3)?;
    websocket.connect(gate_order_url.clone(), GATE_ORDER_TOKEN_4)?;
    websocket.connect(gate_order_url.clone(), GATE_ORDER_TOKEN_5)?;
    websocket.connect(gate_order_url.clone(), GATE_ORDER_TOKEN_6)?;
    websocket.connect(gate_order_url.clone(), GATE_ORDER_TOKEN_7)?;
    websocket.connect(gate_order_url.clone(), GATE_ORDER_TOKEN_8)?;
    websocket.connect(gate_order_url.clone(), GATE_ORDER_TOKEN_9)?;
    websocket.connect(gate_order_url.clone(), GATE_ORDER_TOKEN_10)?;
    websocket.connect(gate_order_url.clone(), GATE_ORDER_SUB_TOKEN)?;
    websocket.connect(gate_order_url.clone(), GATE_USER_TRADE_SUB_TOKEN)?;

    let order_url: Url = generate_futures_order_url().into();
    info!("order url: {}", order_url);
    websocket.connect(order_url.clone(), BN_ORDER_TOKEN)?;

    let bn_http_url = "https://fapi.binance.com";
    websocket.connect(bn_http_url, BN_POSITION_SNAPSHOT_TOKEN)?;

    let gate_http_url = "https://api.gateio.ws";
    websocket.connect(gate_http_url, GATE_POSITION_SNAPSHOT_TOKEN)?;

    let depth_snapshot_url: Url = generate_futures_depth_snapshot_5_url(GATE_BASE_ASSETS).into();
    info!("depth snapshot url: {}", depth_snapshot_url);
    websocket.connect(depth_snapshot_url.clone(), BN_ORDERBOOK_SNAPSHOT_TOKEN_1)?;
    websocket.connect(depth_snapshot_url.clone(), BN_ORDERBOOK_SNAPSHOT_TOKEN_2)?;

    let agg_trade_url: Url = generate_futures_agg_trade_url_by_symbols(GATE_BASE_ASSETS).into();
    websocket.connect(agg_trade_url.clone(), BN_TRADE_TOKEN_1)?;

    let bn_funding_url: Url = generate_funding_sub_url(GATE_BASE_ASSETS).into();
    info!("bn funding url: {}", bn_funding_url);
    websocket.connect(bn_funding_url.clone(), BN_FUNDING_RATE_TOKEN)?;

    let gate_funding_url: Url = "wss://fx-ws.gateio.ws/v4/ws/usdt".into();
    info!("gate funding url: {}", gate_funding_url);
    websocket.connect(gate_funding_url.clone(), GATE_FUNDING_RATE_TOKEN)?;

    let waker = websocket.create_waker(STOP_WAKER_TOKEN);

    match ctrlc::set_handler(move || {
        libwebsocket_rs::info!("Ctrl-C pressed, exiting");
        waker.wake().expect("Failed to wake waker");
    }) {
        Ok(_) => (),
        Err(e) => {
            libwebsocket_rs::error!("Failed to set Ctrl-C handler: {:?}", e);
            libwebsocket_rs::flush_logs!();
        }
    }

    match websocket.run() {
        Ok(_) => (),
        Err(e) => {
            libwebsocket_rs::error!("Websocket run error: {:?}", e);
            libwebsocket_rs::flush_logs!();
        }
    }
    Ok(())
}
