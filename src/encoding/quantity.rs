#[derive(Debug, PartialEq)]
pub struct HedgeResult {
    pub base_qty_common: f64,
    pub binance_qty: f64,
    pub gate_contracts: f64,
    pub notional_usdt: f64,
}

pub fn compute_min_hedge(
    price: f64,
    bn_lot_size: f64,
    bn_min_qty: f64,
    bn_min_notional: f64,
    gate_lot_size: f64,
    gate_min_notional: f64,
    gate_multiplier: f64,
) -> Option<HedgeResult> {
    if !(price.is_finite() && price > 0.0) {
        return None;
    }
    if !(bn_lot_size > 0.0 && gate_multiplier > 0.0 && gate_lot_size > 0.0) {
        return None;
    }

    // -------- helpers --------
    fn ceil_to_step(x: f64, step: f64) -> f64 {
        if step <= 0.0 {
            return x;
        }
        let k = (x / step).ceil();
        (k * step).max(0.0)
    }
    fn round_to_int(x: f64) -> i128 {
        // Safe rounding to i128 for scaled integers
        x.round() as i128
    }
    fn gcd_i128(mut a: i128, mut b: i128) -> i128 {
        if a < 0 {
            a = -a;
        }
        if b < 0 {
            b = -b;
        }
        while b != 0 {
            let r = a % b;
            a = b;
            b = r;
        }
        a
    }
    fn lcm_i128(a: i128, b: i128) -> i128 {
        if a == 0 || b == 0 {
            return 0;
        }
        (a / gcd_i128(a, b)).saturating_mul(b)
    }
    // get decimal precision <= 9 (enough for typical steps)
    fn infer_decimals(step: f64) -> u32 {
        let mut dec = 0u32;
        let mut v = step;
        while dec < 9 && (v.fract() != 0.0) {
            v *= 10.0;
            dec += 1;
            // guard against FP noise
            if (v - v.round()).abs() < 1e-9 {
                break;
            }
        }
        dec
    }
    // Scale LCM on base-unit steps
    let bin_base_unit = bn_lot_size; // base qty granularity on Binance
    let gate_base_unit = gate_multiplier * gate_lot_size; // base qty granularity induced by Gate contracts
    if !(bin_base_unit > 0.0 && gate_base_unit > 0.0) {
        return None;
    }

    // 1) Compute each side's MINIMUM BASE quantity after own constraints
    // Binance: must satisfy min_qty and min_notional; then align to step_qty
    let b_min_qty_step = ceil_to_step(bn_min_qty, bn_lot_size);
    let b_min_qty_from_notional = if bn_min_notional > 0.0 {
        ceil_to_step(bn_min_notional / price, bn_lot_size)
    } else {
        0.0
    };
    let binance_min_base = b_min_qty_step.max(b_min_qty_from_notional).max(bn_lot_size);

    // Gate: compute minimum contracts, then convert to base by multiplier
    let g_min_size_step = ceil_to_step(gate_multiplier, gate_multiplier);
    let g_min_size_from_notional = if gate_min_notional > 0.0 {
        let need_qty = gate_min_notional / price;
        ceil_to_step(need_qty, gate_multiplier)
    } else {
        0.0
    };
    let gate_min_base = g_min_size_step.max(g_min_size_from_notional);
    // 2) Find the smallest common base quantity >= both minima
    // Convert steps to integer units via scaling to avoid float LCM
    let decimals = infer_decimals(bin_base_unit)
        .max(infer_decimals(gate_base_unit))
        .min(9);
    let scale: i128 = 10i128.pow(decimals);
    let unit_a = round_to_int(bin_base_unit * scale as f64); // Binance unit in scaled ints
    let unit_b = round_to_int(gate_base_unit * scale as f64); // Gate unit in scaled ints
    if unit_a <= 0 || unit_b <= 0 {
        return None;
    }

    let lcm_units = lcm_i128(unit_a, unit_b);
    if lcm_units <= 0 {
        return None;
    }

    // Starting requirement in scaled ints
    let min_a = round_to_int((binance_min_base * scale as f64).ceil());
    let min_b = round_to_int((gate_min_base * scale as f64).ceil());
    let start = min_a.max(min_b);

    // Ceil to multiple of LCM
    let k = (start + lcm_units - 1) / lcm_units;
    let q_int = k.saturating_mul(lcm_units);
    if q_int <= 0 {
        return None;
    }

    let base_qty_common = (q_int as f64) / (scale as f64); // Q

    // 3) Map back to each venue's order fields (already aligned by construction)
    // Binance order quantity (base)
    let binance_qty = base_qty_common;

    // Gate contracts = Q / multiplier, and should be multiple of size_step
    let gate_contracts = base_qty_common / gate_multiplier;

    // Final safety checks for notionals (should pass; re-assert)
    let b_notional = binance_qty * price;
    let g_notional = gate_contracts * gate_multiplier * price;

    if bn_min_notional > 0.0 && b_notional + 1e-9 < bn_min_notional {
        return None;
    }
    if gate_min_notional > 0.0 && g_notional + 1e-9 < gate_min_notional {
        return None;
    }

    Some(HedgeResult {
        base_qty_common,
        binance_qty,
        gate_contracts,
        notional_usdt: base_qty_common * price,
    })
}

pub fn normalize_quantity(quantity: f64, lot_size: f64) -> String {
    assert!(lot_size > 0.0, "lot_size must be > 0");

    // 推断步进的小数位数（最多 12 位，足够覆盖主流交易所）
    fn infer_decimals(step: f64) -> usize {
        let mut dec = 0usize;
        let mut v = step;
        while dec < 12 && (v.fract() != 0.0) {
            v *= 10.0;
            dec += 1;
            if (v - v.round()).abs() < 1e-12 {
                break;
            }
        }
        dec
    }

    // 向下对齐到 lot_size 的倍数
    fn round_to_step(x: f64, step: f64) -> f64 {
        ((x / step).round()) * step
    }

    // 去掉多余的尾随 0 和小数点
    fn trim_trailing_zeros(mut s: String) -> String {
        if let Some(_dot) = s.find('.') {
            // 去掉尾随 0
            while s.ends_with('0') {
                s.pop();
            }
            // 如果末尾是小数点，也去掉
            if s.ends_with('.') {
                s.pop();
            }
            // 特殊情况：全部被去掉了，或仅剩 "-0"
            if s.is_empty() || s == "-0" {
                s = "0".to_string();
            }
            // 处理诸如 "-0.000" 的情况
            if s == "-0" {
                s = "0".to_string();
            }
            // 处理小数点前只有负号的情况（极端浮点误差）
            if s == "-" {
                s = "0".to_string();
            }
        }
        s
    }

    let decimals = infer_decimals(lot_size);
    let q = round_to_step(quantity.max(0.0), lot_size);

    // 避免 -0 或极小负数
    let q = if q.abs() < 1e-15 { 0.0 } else { q };

    // 先按推断小数位格式化，再裁剪多余 0
    let s = format!("{:.*}", decimals, q);
    trim_trailing_zeros(s)
}

#[cfg(test)]
mod tests {
    use super::*;
    #[test]
    fn test_basic() {
        // Example:
        // Binance: step_qty=0.1, min_qty=0.1, min_notional=5 USDT
        // Gate: multiplier=1, size_step=1, min_size=1, min_notional=0
        // price=3.2 USDT
        let res = compute_min_hedge(0.11, 1.0, 1.0, 20.0, 1.0, 1.2, 10.0).unwrap();
        println!("res: {:?}", res);

        // let res = compute_min_hedge(0.16, 1.0, 1.0, 15.0, 1.0, 0.16, 10.0).unwrap();
        // println!("res: {:?}", res);

        // let res = compute_min_hedge(12.1, 1.0, 1.0, 15.0, 1.0, 12.1, 1.0).unwrap();
        // println!("res: {:?}", res);

        // let res = compute_min_hedge(4300.1, 0.01, 0.01, 15.0, 1.0, 43.0, 0.01).unwrap();
        // println!("res: {:?}", res);

        // let res = compute_min_hedge(0.10422, 1.0, 1.0, 5.0, 1.0, 0.10428, 1.0).unwrap();
        // println!("res: {:?}", res);
        // assert!((res.base_qty_common - 48.0).abs() < 1e-9);
        // assert!((res.binance_qty - 48.0).abs() < 1e-9);
        // assert!((res.gate_contracts - 48.0).abs() < 1e-9);
    }
}
