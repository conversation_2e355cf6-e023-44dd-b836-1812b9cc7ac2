use ring::hmac;
use std::time::{SystemTime, UNIX_EPOCH};

use base64::prelude::*;
use ed25519_dalek::{SigningKey, ed25519::signature::SignerMut, pkcs8::DecodePrivateKey};

use crate::{
    Message,
    engine::futures_const::{
        FUTURES_API_KEY, FUTURES_API_SECRET, FUTURES_HMAC_API_KEY, FUTURES_HMAC_API_SECRET,
    },
    net::message::http::{HeaderMap, HttpRequest, Method},
};

fn futures_ed25519_sign(query: &str) -> String {
    let mut signing_key = SigningKey::from_pkcs8_pem(FUTURES_API_SECRET).unwrap();
    let signature = signing_key.sign(query.as_bytes());
    BASE64_STANDARD.encode(signature.to_bytes())
}

fn futures_hmac_sign(query: &str) -> String {
    let key = hmac::Key::new(hmac::HMAC_SHA256, FUTURES_HMAC_API_SECRET.as_bytes());
    let tag = hmac::sign(&key, query.as_bytes());
    hex::encode(tag.as_ref())
}

pub fn generate_futures_session_logon_request() -> String {
    let timestamp = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap()
        .as_millis() as u64;
    let ts_str = timestamp.to_string();
    let mut params = vec![("apiKey", FUTURES_API_KEY), ("timestamp", ts_str.as_str())];
    params.sort_by(|a, b| a.0.cmp(b.0));
    let param_str = params
        .iter()
        .map(|(k, v)| format!("{}={}", k, v))
        .collect::<Vec<_>>()
        .join("&");
    let signature = futures_ed25519_sign(&param_str);
    let request = format!(
        r#"
{{
  "id": "56374a46-3061-486b-a311-99ee972eb648",
  "method": "session.logon",
  "params": {{
    "apiKey": "{}",
    "signature": "{}",
    "timestamp": {}
    }}
}}
"#,
        FUTURES_API_KEY, signature, timestamp
    );
    request.to_string()
}

pub fn generate_futures_user_data_start_request() -> String {
    format!(
        r#"
{{
  "id": "d3df8a61-98ea-4fe0-8f4e-0fcea5d418b0",
  "method": "userDataStream.start",
  "params": {{
    "apiKey": "{}"
    }}
    }}
"#,
        FUTURES_API_KEY
    )
}

pub fn generate_futures_user_data_stream_url(listen_key: String) -> String {
    format!("wss://fstream.binance.com/ws/{}", listen_key)
}

pub fn generate_futures_position_snapshot_request() -> Message<'static> {
    let timestamp = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap()
        .as_millis() as u64;
    let ts_str = timestamp.to_string();
    let params = format!("timestamp={}", ts_str);
    let signature = futures_hmac_sign(params.as_str());
    let query = format!("{}&signature={}", params, signature);
    let uri = format!("/fapi/v3/positionRisk?{}", query);
    let mut headers = HeaderMap::new();
    headers.add("X-MBX-APIKEY", FUTURES_HMAC_API_KEY);
    Message::HttpRequest(HttpRequest {
        method: Method::GET,
        headers: headers,
        uri: uri.leak(),
    })
}
