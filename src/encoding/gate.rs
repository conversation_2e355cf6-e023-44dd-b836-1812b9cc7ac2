use ring::hmac;
use serde::Deserialize;
use serde::de::{Deserializer, Error as DeError};
use serde_json::Value;
use serde_json::json;

use crate::{
    Message,
    encoding::{
        bn_futures_order::OrderSide,
        cross_exchange::model::UnifiedOrderStatus,
        price::{RoundingMode, format_price_with_tick_mode},
        quantity::normalize_quantity,
    },
    engine::cross_exchange::cross_exchange_const::{
        GATE_BASE_ASSETS, GATE_LOT_SIZE, GATE_PRICE_TICKS, GATE_QUANTO_MULTIPLIER, UnifiedCurrency,
    },
    net::message::http::{HeaderMap, HttpRequest, Method},
    utils::perf::{system_now_in_ms, system_now_in_secs},
};

use sha2::{Digest, Sha512};

const GATE_USER_ID: &str = "20464758";
#[cfg(feature = "hlc")]
const GATE_API_KEY: &str = "efb5fb382886a7bb26a3302f22a7a4a7";
#[cfg(feature = "hlc")]
const GATE_SECRET_KEY: &str = "adc72b8896c9692114f35cf330a9ee8775d2a200b1b540eba5e33f112230eca6";

#[cfg(feature = "yfq")]
const GATE_API_KEY: &str = "40cce406f4c42f4ecf7a371f82260903";
#[cfg(feature = "yfq")]
const GATE_SECRET_KEY: &str = "066f9e5b957d1c28be390ccc0382b2e308081e7badb81145d3b2003f5bc8d10f";
// https://api.gateio.ws/api/v4/futures/usdt/contracts/LTC_USDT

#[allow(dead_code)]
#[derive(Debug, Deserialize)]
struct Envelope {
    #[allow(dead_code)]
    time: Option<i64>,
    #[allow(dead_code)]
    time_ms: Option<i64>,
    channel: Option<String>,
    event: Option<String>,
    result: Option<serde_json::Value>,
    // 也可能返回 "error"
    error: Option<serde_json::Value>,
}

#[derive(Debug, Deserialize)]
pub struct GateBookTicker {
    /// 生成时间(毫秒)
    #[serde(default, rename = "t")]
    pub ts: u64,

    /// order book update id
    #[serde(default, rename = "u")]
    pub update_id: u64,

    /// 合约名，如 "BTC_USDT"
    #[serde(default, rename = "s")]
    pub symbol: String,

    /// 最优买价
    #[serde(
        default,
        rename = "b",
        deserialize_with = "serde_aux::field_attributes::deserialize_number_from_string"
    )]
    pub bid_price: f64,

    /// 最优买量
    #[serde(
        default,
        rename = "B",
        deserialize_with = "serde_aux::field_attributes::deserialize_number_from_string"
    )]
    pub bid_qty: f64,

    /// 最优卖价
    #[serde(
        default,
        rename = "a",
        deserialize_with = "serde_aux::field_attributes::deserialize_number_from_string"
    )]
    pub ask_price: f64,

    /// 最优卖量
    #[serde(
        default,
        rename = "A",
        deserialize_with = "serde_aux::field_attributes::deserialize_number_from_string"
    )]
    pub ask_qty: f64,
}

#[allow(dead_code)]
#[derive(Debug, Deserialize)]
pub struct FuturesOrder {
    pub contract: String,
    pub id: u64,
    pub size: i64,
    pub price: f64,
    pub status: String,
    pub tif: String,
    #[serde(default)]
    pub text: String,

    #[serde(default)]
    create_time_ms: Option<i64>,
    #[serde(default)]
    pub finish_as: Option<String>,
    #[serde(default)]
    finish_time_ms: Option<i64>,
    #[serde(default)]
    pub fill_price: Option<f64>,
    #[serde(default)]
    pub left: Option<i64>,
    #[serde(default)]
    tkfr: Option<f64>,
    #[serde(default)]
    mkfr: Option<f64>,
    #[serde(default)]
    user: Option<String>,
    #[serde(default)]
    update_id: Option<u64>,
    #[serde(default)]
    pub update_time: Option<i64>,
    #[serde(default)]
    stop_loss_price: Option<String>,
    #[serde(default)]
    stop_profit_price: Option<String>,
    #[serde(default)]
    is_close: Option<bool>,
    #[serde(default)]
    is_reduce_only: Option<bool>,
    #[serde(default)]
    is_liq: Option<bool>,
    #[serde(default)]
    refu: Option<u64>,
    #[serde(default)]
    refr: Option<f64>,
    #[serde(default)]
    iceberg: Option<i64>,
    #[serde(default)]
    biz_info: Option<serde_json::Value>,
}

#[allow(dead_code)]
#[derive(Debug, Deserialize)]
struct WsMsg<T> {
    channel: String,
    event: String,
    #[serde(default)]
    time: Option<i64>,
    #[serde(default)]
    time_ms: Option<i64>,
    result: T,
}

pub fn decode_bbo(input: &[u8]) -> Option<GateBookTicker> {
    match serde_json::from_slice::<Envelope>(input) {
        Ok(env) => {
            if env.channel.as_deref() == Some("futures.book_ticker")
                && env.event.as_deref() == Some("update")
            {
                if let Some(res) = env.result {
                    if let Ok(bt) = serde_json::from_value::<GateBookTicker>(res) {
                        return Some(bt);
                    }
                }
            }
            None
        }
        Err(_) => None,
    }
}

#[derive(Debug, Deserialize)]
pub struct GatePubTrade {
    pub size: i64,
    pub id: u64,
    pub create_time: i64,
    pub create_time_ms: i64,
    pub price: String,
    pub contract: String,
    pub is_internal: Option<bool>,
}

pub fn parse_trades(input: &[u8]) -> Option<Vec<GatePubTrade>> {
    /*
        {
      "channel": "futures.trades",
      "event": "update",
      "time": 1541503698,
      "time_ms": 1541503698123,
      "result": [
        {
          "size": -108,
          "id": 27753479,
          "create_time": 1545136464,
          "create_time_ms": 1545136464123,
          "price": "96.4",
          "contract": "BTC_USD",
          "is_internal": true
        }
      ]
    } */
    match serde_json::from_slice::<Envelope>(input) {
        Ok(env) => {
            if env.event.as_deref() == Some("update") {
                if let Some(res) = env.result {
                    match serde_json::from_value::<Vec<GatePubTrade>>(res) {
                        Ok(trades) => return Some(trades),
                        Err(e) => {
                            crate::error!("parse trades error: {}", e);
                            return None;
                        }
                    }
                }
            }
            None
        }
        Err(e) => {
            crate::error!("parse trades error: {}", e);
            None
        }
    }
}

fn gate_sign(secret: &str, event: &str, channel: &str, req_param: &[u8], ts: &str) -> String {
    let key = hmac::Key::new(hmac::HMAC_SHA512, secret.as_bytes());

    // 构造消息
    let mut msg = Vec::with_capacity(event.len() + channel.len() + req_param.len() + ts.len() + 3);
    msg.extend_from_slice(event.as_bytes());
    msg.extend_from_slice(b"\n");
    msg.extend_from_slice(channel.as_bytes());
    msg.extend_from_slice(b"\n");
    msg.extend_from_slice(req_param);
    msg.extend_from_slice(b"\n");
    msg.extend_from_slice(ts.as_bytes());

    let tag = hmac::sign(&key, &msg);
    hex::encode(tag.as_ref())
}

/*
{
  "time": 1735516800,
  "channel": "futures.login",
  "event": "api",
  "payload": {
    "api_key": "YOUR_API_KEY",
    "signature": "YOUR_HEX_SIGNATURE",
    "timestamp": "1735516800",
    "req_id": "login-001"
  }
} */
pub fn generate_login_request() -> String {
    let ts_sec = system_now_in_ms() / 1000;
    let ts_sec_str = ts_sec.to_string();
    let channel = "futures.login";
    let event = "api";
    let signature = gate_sign(GATE_SECRET_KEY, event, channel, &[], &ts_sec_str);
    format!(
        r#"
{{
    "time": {},
    "channel": "{}",
    "event": "{}",
    "payload": {{
        "api_key": "{}",
        "signature": "{}",
        "timestamp": "{}",
        "req_id": "{}"
    }}
}}"#,
        ts_sec, channel, event, GATE_API_KEY, signature, ts_sec_str, ts_sec_str
    )
}

pub fn place_market_order(
    req_id: u64,
    qty: f64,
    side: OrderSide,
    symbol: &str,
    reduce_only: bool,
) -> String {
    let ts = system_now_in_secs();
    let index = UnifiedCurrency::from_symbol(symbol).unwrap() as usize;
    let lot_size = GATE_LOT_SIZE[index];
    let multiplier = GATE_QUANTO_MULTIPLIER[index];
    let size = normalize_quantity(qty / multiplier, lot_size);
    let reduce_only = if reduce_only { "true" } else { "false" };
    let size = match side {
        OrderSide::Buy => size,
        OrderSide::Sell => format!("-{size}"),
    };
    format!(
        r#"
{{
    "time": {},
    "channel": "futures.order_place",
    "event": "api",
    "payload": {{
        "req_id": "{}",
        "req_param": {{
            "contract": "{}",
            "size": {},
            "price": "0",
            "tif": "ioc",
            "reduce_only": {}
        }}
    }}
}}"#,
        ts, req_id, symbol, size, reduce_only
    )
}

pub fn place_order(
    req_id: u64,
    price: f64,
    qty: f64,
    side: OrderSide,
    tif: &str,
    symbol: &str,
    reduce_only: bool,
) -> String {
    let id = system_now_in_secs();
    let index = UnifiedCurrency::from_symbol(symbol).unwrap() as usize;
    let tick = GATE_PRICE_TICKS[index];
    let mode = match side {
        OrderSide::Buy => RoundingMode::Floor,
        OrderSide::Sell => RoundingMode::Ceil,
    };
    let price = format_price_with_tick_mode(price, tick, mode);
    let lot_size = GATE_LOT_SIZE[index];
    let multiplier = GATE_QUANTO_MULTIPLIER[index];
    let size = normalize_quantity(qty / multiplier, lot_size);
    let reduce_only = if reduce_only { "true" } else { "false" };
    let size = match side {
        OrderSide::Buy => size,
        OrderSide::Sell => format!("-{size}"),
    };
    format!(
        r#"
{{
    "time": {},
    "channel": "futures.order_place",
    "event": "api",
    "payload": {{
        "req_id": "{}",
        "req_param": {{
            "contract": "{}",
            "size": {},
            "price": "{}",
            "tif": "{}",
            "reduce_only": {}
        }}
    }}
}}"#,
        id, req_id, symbol, size, price, tif, reduce_only
    )
}

pub fn modify_order(
    req_id: u64,
    price: f64,
    order_id: u64,
    symbol: &str,
    qty: f64,
    side: OrderSide,
) -> String {
    let time_in_secs = system_now_in_secs();
    let index = UnifiedCurrency::from_symbol(symbol).unwrap() as usize;
    let tick = GATE_PRICE_TICKS[index];
    let mode = match side {
        OrderSide::Buy => RoundingMode::Floor,
        OrderSide::Sell => RoundingMode::Ceil,
    };
    let price = format_price_with_tick_mode(price, tick, mode);
    let lot_size = GATE_LOT_SIZE[index];
    let multiplier = GATE_QUANTO_MULTIPLIER[index];
    let size = normalize_quantity(qty / multiplier, lot_size);
    let size = match side {
        OrderSide::Buy => size,
        OrderSide::Sell => format!("-{size}"),
    };
    format!(
        r#"
{{
    "time": {},
    "channel": "futures.order_amend",
    "event": "api",
    "payload": {{
        "req_id": "{}",
        "req_param": {{
            "order_id": "{}",
            "price": "{}",
            "size": {}
        }}
    }}
}}"#,
        time_in_secs, req_id, order_id, price, size
    )
}

pub fn cancel_all_orders(req_id: u64, symbol: &str) -> String {
    let time_in_secs = system_now_in_secs();
    format!(
        r#"
{{
    "time": {},
    "channel": "futures.order_cancel_cp",
    "event": "api",
    "payload": {{
        "req_id": "{}",
        "req_param": {{
            "contract": "{}"
        }}
    }}
}}"#,
        time_in_secs, req_id, symbol
    )
}

pub fn cancel_order(req_id: u64, order_id: u64) -> String {
    let time_in_secs = system_now_in_secs();
    format!(
        r#"
{{
    "time": {},
    "channel": "futures.order_cancel",
    "event": "api",
    "payload": {{
        "req_id": "{}",
        "req_param": {{
            "order_id": "{}"
        }}
    }}
}}"#,
        time_in_secs, req_id, order_id
    )
}

fn gen_sign(secret: &str, channel: &str, event: &str, ts_sec: u64) -> String {
    let key = hmac::Key::new(hmac::HMAC_SHA512, secret.as_bytes());
    let message = format!("channel={}&event={}&time={}", channel, event, ts_sec);
    let tag = hmac::sign(&key, &message.as_bytes());
    hex::encode(tag.as_ref())
}

pub fn generate_order_sub_request() -> String {
    let ts_sec = system_now_in_secs();
    let channel = "futures.orders";
    let event = "subscribe";
    let sign = gen_sign(GATE_SECRET_KEY, channel, event, ts_sec);
    format!(
        r#"
{{
    "time": {},
    "channel": "{}",
    "event": "{}",
    "payload": ["{}", "!all"],
    "auth": {{
        "method": "api_key",
        "KEY": "{}",
        "SIGN": "{}"
    }}
}}"#,
        ts_sec, channel, event, GATE_USER_ID, GATE_API_KEY, sign
    )
}

pub fn generate_user_trade_sub_request() -> String {
    let ts_sec = system_now_in_secs();
    let channel = "futures.usertrades";
    let event = "subscribe";
    let sign = gen_sign(GATE_SECRET_KEY, channel, event, ts_sec);
    format!(
        r#"
{{
    "time": {},
    "channel": "{}",
    "event": "{}",
    "payload": ["{}", "!all"],
    "auth": {{
        "method": "api_key",
        "KEY": "{}",
        "SIGN": "{}"
    }}
}}"#,
        ts_sec, channel, event, GATE_USER_ID, GATE_API_KEY, sign
    )
}

pub fn generate_position_sub_request() -> String {
    let ts_sec = system_now_in_secs();
    let channel = "futures.positions";
    let event = "subscribe";
    let sign = gen_sign(GATE_SECRET_KEY, channel, event, ts_sec);
    format!(
        r#"
        {{
            "time": {},
            "channel": "{}",
            "event": "{}",
            "payload": ["{}", "!all"],
            "auth": {{
                "method": "api_key",
                "KEY": "{}",
                "SIGN": "{}"
            }}
        }}"#,
        ts_sec, channel, event, GATE_USER_ID, GATE_API_KEY, sign
    )
}

pub fn generate_bbo_subscribe_request() -> String {
    let payload = GATE_BASE_ASSETS
        .iter()
        .map(|s| format!("{}_USDT", s))
        .collect::<Vec<_>>();
    let subscribe_msg = json!({
        "time": chrono::Utc::now().timestamp(),
        "channel": "futures.book_ticker",
        "event": "subscribe",
        "payload": payload,
    });
    subscribe_msg.to_string()
}

pub fn generate_trade_subscribe_request() -> String {
    let payload = GATE_BASE_ASSETS
        .iter()
        .map(|s| format!("{}_USDT", s))
        .collect::<Vec<_>>();
    format!(
        r#"
{{
    "time": {},
    "channel": "futures.trades",
    "event": "subscribe",
    "payload": {}
}}"#,
        system_now_in_secs(),
        serde_json::to_string(&payload).unwrap()
    )
}

pub fn parse_order_update(input: &[u8]) -> Option<Vec<FuturesOrder>> {
    let v: serde_json::Value = match serde_json::from_slice(input) {
        Ok(v) => v,
        Err(_) => return None,
    };
    let channel = v
        .get("channel")
        .and_then(|x| x.as_str())
        .unwrap_or_default();
    let event = v.get("event").and_then(|x| x.as_str()).unwrap_or_default();

    if channel == "futures.orders" && event == "subscribe" {
        return None;
    }
    if channel == "futures.pong" {
        return None;
    }
    if channel == "futures.orders" && event == "update" {
        let msg: WsMsg<Vec<FuturesOrder>> = match serde_json::from_value(v) {
            Ok(msg) => msg,
            Err(_) => return None,
        };
        return Some(msg.result);
    }
    None
}

#[derive(Debug, Deserialize)]
pub struct PositionsPush {
    pub time: i64,
    pub time_ms: Option<i64>,
    pub channel: String, // "futures.positions"
    pub event: String,   // "update"
    pub result: Vec<Position>,
}

/// 单条仓位
#[derive(Debug, Deserialize)]
pub struct Position {
    pub contract: String, // "BTC_USD" / "BTC_USDT" 等
    pub cross_leverage_limit: Option<f64>,
    pub entry_price: Option<f64>,
    pub history_pnl: Option<f64>,
    pub history_point: Option<f64>,
    pub last_close_pnl: Option<f64>,
    pub leverage: Option<i64>, // 0=cross，其它=逐仓杠杆倍数
    pub leverage_max: Option<i64>,
    pub liq_price: Option<f64>,
    pub maintenance_rate: Option<f64>,
    pub margin: Option<f64>,
    pub mode: Option<String>, // "single" 等
    pub realised_pnl: Option<f64>,
    pub realised_point: Option<f64>,
    pub risk_limit: Option<i64>,
    pub size: Option<i64>,    // 张数：正=多，负=空（Gate 文档语义）
    pub time: Option<i64>,    // update unix ts (s)
    pub time_ms: Option<i64>, // update unix ts (ms)
    pub user: Option<String>,
    pub update_id: Option<i64>,
}

pub fn parse_position_update(input: &[u8]) -> Option<Vec<Position>> {
    let v: serde_json::Value = match serde_json::from_slice(input) {
        Ok(v) => v,
        Err(_) => return None,
    };
    let channel = v
        .get("channel")
        .and_then(|x| x.as_str())
        .unwrap_or_default();
    let event = v.get("event").and_then(|x| x.as_str()).unwrap_or_default();

    if channel == "futures.positions" && event == "update" {
        let msg: PositionsPush = match serde_json::from_value(v) {
            Ok(msg) => msg,
            Err(_) => return None,
        };
        return Some(msg.result);
    }
    None
}

pub fn unify_gate_status(
    status: &str,
    finish_as: &Option<String>,
    left: Option<i64>,
    size: i64,
    tif: &String,
) -> UnifiedOrderStatus {
    let s = status.to_ascii_lowercase();
    if s == "open" {
        if let Some(l) = left
            && l != size
        {
            return UnifiedOrderStatus::PartialFilled;
        }
        return UnifiedOrderStatus::Open;
    }
    if s == "finished" {
        // 先看是否实满成交
        if finish_as
            .as_deref()
            .map(|x| x.eq_ignore_ascii_case("filled"))
            .unwrap_or(false)
            || left == Some(0)
        {
            return UnifiedOrderStatus::Filled;
        }

        let fa = finish_as
            .as_ref()
            .map(|x| x.to_ascii_lowercase())
            .unwrap_or_default();

        // 系统/人工取消类
        let is_canceled = matches!(
            fa.as_str(),
            "cancelled" | "canceled" | "liquidated" | "reduce_only" | "adl" | "auto_deleveraged"
        );

        if is_canceled {
            if let Some(l) = left
                && l != size
            {
                return UnifiedOrderStatus::Filled;
            }
            return UnifiedOrderStatus::Canceled;
        }

        // 规则或时效导致未成交（等价“过期”）
        let tif_l = tif.to_ascii_lowercase();
        let is_expired = matches!(
            fa.as_str(),
            "ioc" | "post_only" | "poc" | "po" | "gtx" | "expired"
        ) || matches!(tif_l.as_str(), "ioc" | "gtx");

        if is_expired {
            return UnifiedOrderStatus::Expired;
        }

        crate::info!(
            "unknown order status: {} {:?} {:?} we consider it as canceled",
            status,
            finish_as,
            left
        );
        return UnifiedOrderStatus::Canceled;
    }

    UnifiedOrderStatus::Open
}

pub fn generate_ping_req() -> String {
    json!({"time": system_now_in_secs(), "channel": "futures.ping"}).to_string()
}

fn de_f64_from_any<'de, D>(de: D) -> Result<f64, D::Error>
where
    D: serde::Deserializer<'de>,
{
    use serde::Deserialize;
    match serde_json::Value::deserialize(de)? {
        serde_json::Value::Number(n) => n
            .as_f64()
            .ok_or_else(|| serde::de::Error::custom("invalid f64")),
        serde_json::Value::String(s) => s
            .parse::<f64>()
            .map_err(|_| serde::de::Error::custom("invalid f64 string")),
        v => Err(serde::de::Error::custom(format!(
            "expected number/string, got {v}"
        ))),
    }
}

fn de_u64_from_any<'de, D>(de: D) -> Result<u64, D::Error>
where
    D: serde::Deserializer<'de>,
{
    use serde::Deserialize;
    match serde_json::Value::deserialize(de)? {
        serde_json::Value::Number(n) => n
            .as_u64()
            .ok_or_else(|| serde::de::Error::custom("invalid u64")),
        serde_json::Value::String(s) => s
            .parse::<u64>()
            .map_err(|_| serde::de::Error::custom("invalid u64 string")),
        v => Err(serde::de::Error::custom(format!(
            "expected number/string, got {v}"
        ))),
    }
}

fn de_user_from_any<'de, D>(de: D) -> Result<Option<String>, D::Error>
where
    D: serde::Deserializer<'de>,
{
    use serde::Deserialize;
    match serde_json::Value::deserialize(de)? {
        serde_json::Value::Number(n) => Ok(Some(n.to_string())),
        serde_json::Value::String(s) => Ok(Some(s)),
        serde_json::Value::Null => Ok(None),
        v => Err(serde::de::Error::custom(format!(
            "expected number/string/null, got {v}"
        ))),
    }
}

fn de_timestamp_from_any<'de, D>(de: D) -> Result<Option<i64>, D::Error>
where
    D: serde::Deserializer<'de>,
{
    use serde::Deserialize;
    match serde_json::Value::deserialize(de)? {
        serde_json::Value::Number(n) => {
            if let Some(f) = n.as_f64() {
                // 将秒转换为毫秒
                Ok(Some((f * 1000.0) as i64))
            } else if let Some(i) = n.as_i64() {
                // 如果已经是整数，检查是否需要转换
                if i > 1_000_000_000_000 {
                    // 已经是毫秒
                    Ok(Some(i))
                } else {
                    // 是秒，转换为毫秒
                    Ok(Some(i * 1000))
                }
            } else {
                Err(serde::de::Error::custom("invalid timestamp"))
            }
        }
        serde_json::Value::String(s) => {
            if let Ok(f) = s.parse::<f64>() {
                Ok(Some((f * 1000.0) as i64))
            } else {
                Err(serde::de::Error::custom("invalid timestamp string"))
            }
        }
        serde_json::Value::Null => Ok(None),
        v => Err(serde::de::Error::custom(format!(
            "expected number/string/null, got {v}"
        ))),
    }
}

fn de_f64_option_from_any<'de, D>(de: D) -> Result<Option<f64>, D::Error>
where
    D: serde::Deserializer<'de>,
{
    use serde::Deserialize;
    match serde_json::Value::deserialize(de)? {
        serde_json::Value::Number(n) => n
            .as_f64()
            .map(Some)
            .ok_or_else(|| serde::de::Error::custom("invalid f64")),
        serde_json::Value::String(s) => {
            if s.is_empty() || s == "0" {
                Ok(Some(0.0))
            } else {
                s.parse::<f64>()
                    .map(Some)
                    .map_err(|_| serde::de::Error::custom("invalid f64 string"))
            }
        }
        serde_json::Value::Null => Ok(None),
        v => Err(serde::de::Error::custom(format!(
            "expected number/string/null, got {v}"
        ))),
    }
}

#[derive(Deserialize, Debug)]
pub struct OrderPlaceAck {
    pub req_id: String,
    #[serde(rename = "req_param")]
    pub param: OrderPlaceParam,
}

// =============== 你发出去的 req_param 结构（ack 时会原样回显） ===============
#[derive(Debug, Deserialize, Clone)]
pub struct OrderPlaceParam {
    pub contract: String,
    pub size: i64,
    #[serde(deserialize_with = "de_f64_from_any")]
    pub price: f64,
    pub tif: String,
    #[serde(default)]
    pub text: Option<String>,
    // 这里保留空间：iceberg、stp_act、reduce_only 等需要时再加
}

// =============== 最终响应里的订单对象（常用字段；其余用 Option 兜底） ===============
#[derive(Debug, Deserialize, Clone)]
pub struct OrderEntity {
    #[serde(deserialize_with = "de_u64_from_any")]
    pub id: u64,
    pub contract: String,
    pub size: i64,
    #[serde(deserialize_with = "de_f64_from_any")]
    pub price: f64,
    pub tif: String,

    pub status: String, // "open" / "finished"
    #[serde(default)]
    pub finish_as: Option<String>, // "filled" / "canceled" / "ioc" / "gtx" / "expired" ...
    #[serde(default)]
    pub left: Option<i64>, // 剩余张数
    #[serde(default, deserialize_with = "de_user_from_any")]
    pub user: Option<String>, // 文档例子里有时是数字/字符串
    #[serde(
        default,
        rename = "create_time",
        deserialize_with = "de_timestamp_from_any"
    )]
    pub create_time_ms: Option<i64>,
    #[serde(
        default,
        rename = "update_time",
        deserialize_with = "de_timestamp_from_any"
    )]
    pub update_time_ms: Option<i64>,
    #[serde(default)]
    pub finish_time_ms: Option<i64>,
    #[serde(default)]
    pub update_id: Option<u64>,
    #[serde(default)]
    pub text: Option<String>,
    #[serde(default)]
    pub biz_info: Option<String>,
    #[serde(default)]
    pub amend_text: Option<String>,
    #[serde(default)]
    pub stp_act: Option<String>,
    #[serde(default, deserialize_with = "de_f64_option_from_any")]
    pub fill_price: Option<f64>,
}

#[derive(Debug, Deserialize)]
pub struct WsHeader {
    pub response_time: String,
    pub status: String,
    pub channel: String,
    pub event: String,
    #[serde(default)]
    pub client_id: Option<String>,
    #[serde(default)]
    pub conn_id: Option<String>,
    #[serde(default)]
    pub conn_trace_id: Option<String>,
    #[serde(default)]
    pub trace_id: Option<String>,
    pub x_gate_ratelimit_requests_remain: Option<u64>,
    pub x_gate_ratelimit_limit: Option<u64>,
    pub x_gat_ratelimit_reset_timestamp: Option<u64>,
}

#[derive(Debug, Deserialize)]
pub struct WsErr {
    pub label: String,
    pub message: String,
}

#[allow(dead_code)]
#[derive(Debug, Deserialize)]
struct WsData<R> {
    pub result: Option<R>,
    #[serde(default)]
    pub errs: Option<WsErr>,
}

#[allow(dead_code)]
#[derive(Debug, Deserialize)]
#[serde(bound(deserialize = "R: serde::de::DeserializeOwned"))] // 或 "R: Deserialize<'de>"
struct OrderPlaceEnvelope<R> {
    pub request_id: String,
    #[serde(default)]
    pub ack: Option<bool>, // 文档：ack 仅在确认消息里返回；最终响应通常不返回该字段。:contentReference[oaicite:1]{index=1}
    pub header: WsHeader,
    pub data: WsData<R>,
}

// =============== 统一后的高层结果枚举（你直接用这个） ===============
#[derive(Debug)]
pub enum OrderPlaceMsg {
    /// ack 确认：data.result 回显的是你发出去的 req_param
    Ack {
        request_id: String,
        header: WsHeader,
        req_param: OrderPlaceParam,
    },
    /// 最终响应：data.result 是订单对象（可能已成交/部分成交/挂单）
    Response {
        request_id: String,
        header: WsHeader,
        order: OrderEntity,
    },
    /// 出错：data.errs 携带 label/message
    Error {
        request_id: String,
        header: WsHeader,
        err: WsErr,
    },
}

/// 顶层响应：futures.order_cancel_cp -> event: "api"
#[derive(Debug, Deserialize)]
pub struct CancelCpWsResponse {
    #[serde(rename = "request_id")]
    pub request_id: String,
    pub header: CancelCpHeader,
    pub data: CancelCpData,
}

/// header 元信息（含限速/追踪字段）
#[derive(Debug, Deserialize)]
pub struct CancelCpHeader {
    #[serde(rename = "response_time")]
    pub response_time_ms: String, // 文档是字符串毫秒
    pub status: String,  // "200" 表示成功
    pub channel: String, // "futures.order_cancel_cp"
    pub event: String,   // "api"
    #[serde(rename = "client_id")]
    pub client_id: String,

    #[serde(rename = "conn_id")]
    pub conn_id: Option<String>,
    #[serde(rename = "conn_trace_id")]
    pub conn_trace_id: Option<String>,
    #[serde(rename = "trace_id")]
    pub trace_id: Option<String>,

    // 限速相关：文档里字段可能为 0 时隐藏
    #[serde(rename = "x_gate_ratelimit_requests_remain")]
    pub ratelimit_remain: Option<i64>,
    #[serde(rename = "x_gate_ratelimit_limit")]
    pub ratelimit_limit: Option<i64>,
    #[serde(rename = "x_gat_ratelimit_reset_timestamp")]
    pub ratelimit_reset_ts_ms: Option<i64>,
}

#[derive(Debug, Deserialize)]
pub struct CancelCpData {
    pub result: Vec<CanceledOrder>,
    /// 如果请求失败才会出现 errs
    pub errs: Option<CancelCpErr>,
}

/// 单个被取消的订单（文档示例字段）
#[derive(Debug, Deserialize)]
pub struct CanceledOrder {
    pub id: i64,
    pub user: Option<i64>,
    pub contract: String,
    pub size: i64,
    pub price: String,
    pub tif: Option<String>,
    pub left: Option<i64>,
    #[serde(rename = "fill_price")]
    pub fill_price: Option<String>,
    pub text: Option<String>,
    pub tkfr: Option<String>,
    pub mkfr: Option<String>,
    #[serde(rename = "stp_id")]
    pub stp_id: Option<i64>,
    #[serde(rename = "stp_act")]
    pub stp_act: Option<String>,
    #[serde(rename = "amend_text")]
    pub amend_text: Option<String>,

    // 生命周期相关
    #[serde(rename = "status")]
    pub status: String, // "finished"
    #[serde(rename = "finish_as")]
    pub finish_as: String, // "cancelled"

    // create_time / finish_time 文档为秒(可带小数)，这里先收成 f64 再转
    #[serde(rename = "create_time")]
    pub create_time_s: Option<f64>,
    #[serde(rename = "finish_time")]
    pub finish_time_s: Option<f64>,
}

#[derive(Debug, Deserialize)]
pub struct CancelCpErr {
    pub label: Option<String>,
    pub message: Option<String>,
}

#[derive(Debug)]
pub enum GateWebsocketRsp {
    OrderPlace(OrderPlaceMsg),
    CancelCp(CancelCpWsResponse),
    OrderStatus(WsOrderStatusMsg),
}

fn de_i64_from_any<'de, D>(de: D) -> Result<i64, D::Error>
where
    D: Deserializer<'de>,
{
    #[derive(Deserialize)]
    #[serde(untagged)]
    enum I64OrString {
        I64(i64),
        U64(u64),
        F64(f64),
        Str(String),
    }

    match I64OrString::deserialize(de)? {
        I64OrString::I64(v) => Ok(v),
        I64OrString::U64(v) => Ok(v as i64),
        I64OrString::F64(v) => Ok(v as i64),
        I64OrString::Str(s) => s.parse::<i64>().map_err(serde::de::Error::custom),
    }
}

/// -------- 业务数据结构 --------
#[derive(Debug, Deserialize)]
pub struct WsOrderStatusMsg {
    pub request_id: String,
    pub header: WsHeader,
    pub data: DataWrap,
}

#[derive(Debug, Deserialize)]
pub struct DataWrap {
    pub result: OrderStatus,
}

#[derive(Debug, Deserialize)]
pub struct OrderStatus {
    pub id: i64,
    pub user: i64,
    #[serde(deserialize_with = "de_f64_from_any")]
    pub create_time: f64,
    pub status: String,   // e.g. "open"
    pub contract: String, // e.g. "BTC_USDT"
    #[serde(deserialize_with = "de_i64_from_any")]
    pub size: i64,
    pub price: String, // 注意：返回里是字符串
    pub tif: String,   // e.g. "gtc"
    #[serde(deserialize_with = "de_i64_from_any")]
    pub left: i64,
    pub fill_price: String, // 注意：返回里是字符串，如 "0"
}

/// 解析一条 futures.order_place 的回包（ack 或最终响应）
/// 用法： let msg = parse_order_place(json_str)?;
/// {"header":{
/// "response_time":"1757553851803",
/// "status":"200",
/// "channel":"futures.order_place",
/// "event":"api","client_id":"18.183.228.11-0xc03f07adc8",
/// "conn_id":"fe92e0824b70f766",
/// "conn_trace_id":"********************************",
/// "trace_id":"b81f65eb899ae8b809d8988980e030e3",
/// "x_in_time":1757553851803049,"x_out_time":1757553851803148,
/// "x_gate_ratelimit_requests_remain":99,"x_gate_ratelimit_limit":100,
/// "x_gate_ratelimit_reset_timestamp":1757553851803},
/// "data":{
/// "result":{"req_id":"90083664941578",
/// "req_param":{"size":1,"price":"17.1803","tif":"poc","reduce_only":false,"text":"apiv4-ws","contract":"MYX_USDT"}}},
/// "request_id":"90083664941578","ack":true}
pub fn parse_order_place(json: &[u8]) -> Option<GateWebsocketRsp> {
    let v: serde_json::Value = match serde_json::from_slice(json) {
        Ok(v) => v,
        Err(_) => {
            crate::error!("parse order place error: {}", String::from_utf8_lossy(json));
            return None;
        }
    };
    let mut channel = v
        .get("channel")
        .and_then(|x| x.as_str())
        .unwrap_or_default();
    if channel == "" {
        channel = v
            .get("header")
            .and_then(|x| x.get("channel"))
            .and_then(|x| x.as_str())
            .unwrap_or_default();
    }

    if channel == "futures.pong" {
        return None;
    }

    if channel == "futures.order_status" {
        match serde_json::from_slice::<WsOrderStatusMsg>(json) {
            Ok(rsp) => return Some(GateWebsocketRsp::OrderStatus(rsp)),
            Err(e) => {
                crate::error!("parse order status error: {}", e);
                return None;
            }
        }
    }

    if channel == "futures.order_cancel_cp" {
        match serde_json::from_slice::<CancelCpWsResponse>(json) {
            Ok(rsp) => return Some(GateWebsocketRsp::CancelCp(rsp)),
            Err(e) => {
                crate::error!("parse cancel cp error: {}", e);
                return None;
            }
        }
    }

    // 公共元信息
    let request_id = v
        .get("request_id")
        .and_then(|x| x.as_str())
        .unwrap_or_default()
        .to_string();
    let header: WsHeader = match serde_json::from_value(match v.get("header").cloned() {
        Some(v) => v,
        None => return None,
    }) {
        Ok(v) => v,
        Err(_) => return None,
    };

    // data.errs -> Error
    if let Some(errs) = v.get("data").and_then(|d| d.get("errs")).cloned() {
        let err: WsErr = match serde_json::from_value(errs) {
            Ok(v) => v,
            Err(_) => return None,
        };
        return Some(GateWebsocketRsp::OrderPlace(OrderPlaceMsg::Error {
            request_id,
            header,
            err,
        }));
    }

    // 存在 ack 字段（无论 true/false）都按 ack 处理；文档处写 ack 出现在确认消息里。:contentReference[oaicite:2]{index=2}
    let is_ack = v.get("ack").is_some();

    if is_ack {
        // ack: result 回显的是 req_param
        let env: OrderPlaceEnvelope<OrderPlaceAck> = match serde_json::from_value(v) {
            Ok(v) => v,
            Err(e) => {
                crate::info!("parse order place ack error: {}", e);
                return None;
            }
        };
        let req_param = match env.data.result {
            Some(v) => match v {
                OrderPlaceAck { param, .. } => param,
            },
            None => return None,
        };
        return Some(GateWebsocketRsp::OrderPlace(OrderPlaceMsg::Ack {
            request_id: env.request_id,
            header: env.header,
            req_param,
        }));
    } else {
        // 最终响应：result 是订单对象
        let env: OrderPlaceEnvelope<OrderEntity> = match serde_json::from_value(v) {
            Ok(v) => v,
            Err(e) => {
                crate::info!("parse order place response error: {}", e);
                return None;
            }
        };
        let order = match env.data.result {
            Some(v) => v,
            None => return None,
        };
        return Some(GateWebsocketRsp::OrderPlace(OrderPlaceMsg::Response {
            request_id: env.request_id,
            header: env.header,
            order,
        }));
    }
}

fn de_opt_i64<'de, D>(de: D) -> Result<Option<i64>, D::Error>
where
    D: Deserializer<'de>,
{
    let v: Option<Value> = Option::deserialize(de)?;
    Ok(match v {
        None => None,
        Some(Value::Null) => None,
        Some(Value::Number(n)) => n.as_i64(),
        Some(Value::String(s)) => {
            if s.is_empty() {
                None
            } else {
                s.parse::<i64>().map(Some).map_err(D::Error::custom)?
            }
        }
        other => {
            return Err(D::Error::custom(format!(
                "expect i64 or string, got {:?}",
                other
            )));
        }
    })
}

fn de_opt_f64<'de, D>(de: D) -> Result<Option<f64>, D::Error>
where
    D: Deserializer<'de>,
{
    let v: Option<Value> = Option::deserialize(de)?;
    Ok(match v {
        None => None,
        Some(Value::Null) => None,
        Some(Value::Number(n)) => n.as_f64(),
        Some(Value::String(s)) => {
            if s.is_empty() {
                None
            } else {
                s.parse::<f64>().map(Some).map_err(D::Error::custom)?
            }
        }
        other => {
            return Err(D::Error::custom(format!(
                "expect f64 or string, got {:?}",
                other
            )));
        }
    })
}

#[derive(Debug, Deserialize)]
pub struct PositionSnapshot {
    pub contract: String, // e.g. "BTC_USDT"

    #[serde(default, deserialize_with = "de_opt_i64")]
    pub update_id: Option<i64>,

    // 仓位与价格
    #[serde(default, deserialize_with = "de_opt_i64")]
    pub size: Option<i64>, // 正=多，负=空（有时返回 "0"）
    #[serde(default)]
    pub entry_price: Option<String>,
    #[serde(default)]
    pub mark_price: Option<String>,
    #[serde(default)]
    pub liq_price: Option<String>,

    // 杠杆/保证金/模式
    #[serde(default, deserialize_with = "de_opt_i64")]
    pub leverage: Option<i64>, // 0 表示全仓；可能返回 "0"
    #[serde(default, deserialize_with = "de_opt_f64")]
    pub cross_leverage_limit: Option<f64>,
    #[serde(default, deserialize_with = "de_opt_i64")]
    pub risk_limit: Option<i64>,
    #[serde(default, deserialize_with = "de_opt_f64")]
    pub maintenance_rate: Option<f64>,
    #[serde(default)]
    pub margin: Option<String>,
    #[serde(default)]
    pub mode: Option<String>, // "single" 等

    // PnL 与历史（这些通常就是字符串，保持 String 更安全）
    #[serde(default)]
    pub realised_pnl: Option<String>,
    #[serde(default)]
    pub history_pnl: Option<String>,
    #[serde(default)]
    pub last_close_pnl: Option<String>,
    #[serde(default)]
    pub pnl_pnl: Option<String>,
    #[serde(default)]
    pub pnl_fund: Option<String>,
    #[serde(default)]
    pub pnl_fee: Option<String>,

    // 时间
    #[serde(default, deserialize_with = "de_opt_i64")]
    pub time: Option<i64>, // 可能返回 "0"
    #[serde(default, deserialize_with = "de_opt_i64")]
    pub time_ms: Option<i64>,

    // 兜底未来新增字段
    #[serde(flatten)]
    pub extra: serde_json::Value,
}

/// 解析函数：传入 HTTP 响应 body（JSON 文本）→ Vec<Position>
pub fn parse_positions(json: &[u8]) -> Option<Vec<PositionSnapshot>> {
    match serde_json::from_slice::<Vec<PositionSnapshot>>(json) {
        Ok(v) => Some(v),
        Err(e) => {
            crate::error!("parse positions error: {}", e);
            None
        }
    }
}

/// 计算 SHA512(Body) 的十六进制（用于 sign_string 第4段）
fn sha512_hex(body: &[u8]) -> String {
    let mut hasher = Sha512::new();
    hasher.update(body);
    hex::encode(&hasher.finalize())
}

fn gate_hmac_sign(secret: &str, message: &str) -> String {
    let key = hmac::Key::new(hmac::HMAC_SHA512, secret.as_bytes());
    let tag = hmac::sign(&key, message.as_bytes());
    hex::encode(tag.as_ref())
}

pub fn generate_position_snapshot_request() -> Message<'static> {
    let timestamp = system_now_in_secs();
    let ts_str = timestamp.to_string();
    let path = "/api/v4/futures/usdt/positions";
    let query = ""; // 本接口一般无需 query；若有拼到这里
    let body: Vec<u8> = vec![]; // GET 通常空体
    let hashed_payload = sha512_hex(&body);
    let sign_string = format!("GET\n{path}\n{query}\n{hashed_payload}\n{ts_str}");

    let signature = gate_hmac_sign(GATE_SECRET_KEY, sign_string.as_str());
    let mut headers = HeaderMap::new();
    headers.add("Accept", "application/json");
    headers.add("Content-Type", "application/json");
    headers.add("KEY", GATE_API_KEY);
    headers.add("Timestamp", ts_str);
    headers.add("SIGN", signature);
    Message::HttpRequest(HttpRequest {
        method: Method::GET,
        headers: headers,
        uri: path.to_string().leak(),
    })
}

pub fn check_order(req_id: u64, order_id: u64) -> String {
    let ts_secs = system_now_in_secs();
    format!(
        r#"
{{
    "time": {},
    "channel": "futures.order_status",
    "event": "api",
    "payload": {{
        "req_id": "{}",
        "req_param": {{
            "order_id": "{}"
        }}
    }}
}}
"#,
        ts_secs, req_id, order_id
    )
}

#[derive(Debug, Deserialize)]
pub struct WsEnvelope<T> {
    pub time: Option<i64>,
    pub time_ms: Option<i64>,
    pub channel: String,
    pub event: String,
    pub result: T,
}

#[derive(Debug, Deserialize)]
pub struct UserTrade {
    #[serde(deserialize_with = "de_str_or_int_to_u64")]
    pub id: u64,

    pub contract: String,

    #[serde(rename = "order_id", deserialize_with = "de_str_or_int_to_u64")]
    pub order_id: u64,

    pub size: i64,

    // 价格通常以字符串给出，避免浮点误差，保留为 String
    pub price: String,

    pub role: TradeRole,

    // 有时存在
    pub text: Option<String>,

    #[serde(deserialize_with = "de_str_or_num_to_f64")]
    pub fee: f64,

    #[serde(default, deserialize_with = "de_opt_str_or_num_to_f64")]
    pub point_fee: Option<f64>,

    pub create_time: Option<i64>,
    pub create_time_ms: Option<i64>,
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum TradeRole {
    Maker,
    Taker,
    #[serde(other)]
    Unknown,
}

// ---- 兼容反序列化辅助函数 ----
use serde::de;

fn de_str_or_int_to_u64<'de, D>(de: D) -> Result<u64, D::Error>
where
    D: Deserializer<'de>,
{
    let v: Value = Value::deserialize(de)?;
    match v {
        Value::Number(n) => n
            .as_u64()
            .ok_or_else(|| de::Error::custom("expected u64 number")),
        Value::String(s) => s
            .parse::<u64>()
            .map_err(|_| de::Error::custom("expected u64 string")),
        _ => Err(de::Error::custom("expected string or number for u64")),
    }
}

fn de_str_or_num_to_f64<'de, D>(de: D) -> Result<f64, D::Error>
where
    D: Deserializer<'de>,
{
    let v: Value = Value::deserialize(de)?;
    match v {
        Value::Number(n) => n
            .as_f64()
            .ok_or_else(|| de::Error::custom("expected f64 number")),
        Value::String(s) => s
            .parse::<f64>()
            .map_err(|_| de::Error::custom("expected f64 string")),
        _ => Err(de::Error::custom("expected string or number for f64")),
    }
}

fn de_opt_str_or_num_to_f64<'de, D>(de: D) -> Result<Option<f64>, D::Error>
where
    D: Deserializer<'de>,
{
    let v: Option<Value> = Option::deserialize(de)?;
    match v {
        None | Some(Value::Null) => Ok(None),
        Some(Value::Number(n)) => n
            .as_f64()
            .ok_or_else(|| de::Error::custom("expected f64 number"))
            .map(Some),
        Some(Value::String(s)) => s
            .parse::<f64>()
            .map(Some)
            .map_err(|_| de::Error::custom("expected f64 string")),
        Some(other) => Err(de::Error::custom(format!(
            "expected string/number/null, got {other}"
        ))),
    }
}

pub fn parser_user_trade(input: &[u8]) -> Option<Vec<UserTrade>> {
    let env: WsEnvelope<Vec<UserTrade>> = match serde_json::from_slice(input) {
        Ok(v) => v,
        Err(_) => return None,
    };
    Some(env.result)
}

#[cfg(test)]
mod test {
    #[test]
    fn test_parser_order_place() {
        let content = r#"{"header":{"response_time":"1756897167486","status":"200","channel":"futures.order_cancel_cp","event":"api","client_id":"54.249.77.207-0xc10c783348","conn_id":"26ce88384bdd0676","conn_trace_id":"f65b5711f5095398eb5b1644f52e6a82","trace_id":"7f717f5955d56766c2839e5c01294247","x_in_time":1756897167485803,"x_out_time":1756897167486433,"x_gate_ratelimit_reset_timestamp":1756897167486},"data":{"result":[{"text":"apiv4-ws","price":"0.9672","biz_info":"-","tif":"poc","amend_text":"-","status":"finished","contract":"ONDO_USDT","stp_act":"-","finish_as":"cancelled","fill_price":"0","id":133137664744870578,"create_time":1756897165.352,"size":11,"finish_time":1756897167.486,"update_time":1756897167.486,"left":11,"user":36554434}]},"request_id":"728104496564874"}"#;
        let rsp = super::parse_order_place(content.as_bytes());
        assert!(rsp.is_some());
    }
}
