use crate::utils::perf::system_now_in_secs;

pub fn generate_funding_sub_request(symbols: &[&str]) -> String {
    let ts = system_now_in_secs();
    let symbols = symbols
        .iter()
        .map(|s| format!("\"{}_USDT\"", s))
        .collect::<Vec<_>>()
        .join(",");
    format!(
        r#"
{{
    "time" : {},
    "channel" : "futures.tickers",
    "event": "subscribe",
    "payload" : [{}]
}}"#,
        ts, symbols
    )
}

pub fn parse_funding_fee(input: &[u8]) -> Option<(String, f64)> {
    let contract_pattern = b"\"contract\":\"";
    let start = memchr::memmem::find(input, contract_pattern)?;
    let start = start + contract_pattern.len();
    let end = memchr::memchr(b'"', &input[start..])?;
    let contract = &input[start..start + end];
    let contract = unsafe { std::str::from_utf8_unchecked(contract) };
    let contract = contract.to_uppercase();
    let funding_fee_pattern = b"\"funding_rate\":\"";
    let start = memchr::memmem::find(input, funding_fee_pattern)?;
    let start = start + funding_fee_pattern.len();
    let end = memchr::memchr(b'"', &input[start..])?;
    let funding_fee = &input[start..start + end];
    let funding_fee = unsafe { std::str::from_utf8_unchecked(funding_fee) };
    let funding_fee = funding_fee.parse().ok()?;
    Some((contract, funding_fee))
}
