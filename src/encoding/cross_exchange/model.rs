use crate::encoding::bn_futures_order::OrderSide;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ialEq, Eq)]
#[repr(u8)]
pub enum UnifiedOrderStatus {
    Uninitialized,
    Opening,
    Amending,
    Open,
    PartialFilled,
    Filled,
    Cancelling,
    Canceled,
    Expired,
}

#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq)]
pub struct HedgeOrder {
    pub qty: f64,
    pub order_id: u64,
    pub req_id: u64,
    pub side: OrderSide,
}

impl Default for HedgeOrder {
    fn default() -> Self {
        Self {
            qty: 0.0,
            order_id: 0,
            req_id: 0,
            side: OrderSide::Buy,
        }
    }
}

#[derive(Debug, <PERSON>lone, Copy, PartialEq)]
pub struct MakerOrder {
    pub req_id: u64,
    pub order_id: u64,
    pub last_order_id: u64,
    pub price: f64,
    pub quantity: f64,
    pub filled_qty: f64,
    pub filled_qty_by_trade: f64,
    pub side: OrderSide,
    pub last_modify_time: u64,
    pub status: UnifiedOrderStatus,
    pub canceling_by_timeout: bool,
}

impl Default for MakerOrder {
    fn default() -> Self {
        Self {
            req_id: 0,
            order_id: 0,
            last_order_id: 0,
            price: 0.0,
            quantity: 0.0,
            filled_qty: 0.0,
            filled_qty_by_trade: 0.0,
            side: OrderSide::Buy,
            last_modify_time: 0,
            status: UnifiedOrderStatus::Uninitialized,
            canceling_by_timeout: false,
        }
    }
}

#[derive(Debug, Clone, Copy)]
pub struct HedgeStatus {
    pub maker_avg_price: f64,
    pub maker_qty: f64,
    pub maker_pending: f64,
    pub taker_avg_price: f64,
    pub taker_qty: f64,
    pub taker_pending: f64,
}

impl Default for HedgeStatus {
    fn default() -> Self {
        Self {
            maker_avg_price: 0.0,
            maker_qty: 0.0,
            maker_pending: 0.0,
            taker_avg_price: 0.0,
            taker_qty: 0.0,
            taker_pending: 0.0,
        }
    }
}

impl std::fmt::Display for HedgeStatus {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "HedgeStatus")?;
        write!("")
    }
}

#[derive(Debug, Clone, Copy)]
pub struct UnifiedBbo {
    pub bid_price: f64,
    pub bid_qty: f64,
    pub ask_price: f64,
    pub ask_qty: f64,
    pub book_update_id: u64,
    pub transaction_time: u64,
}

impl Default for UnifiedBbo {
    fn default() -> Self {
        Self {
            bid_price: 0.0,
            bid_qty: 0.0,
            ask_price: 0.0,
            ask_qty: 0.0,
            book_update_id: 0,
            transaction_time: 0,
        }
    }
}
