pub fn parse_funding_fee(input: &[u8]) -> Option<(String, f64)> {
    let symbol_pattern = b"\"s\":\"";
    let start = memchr::memmem::find(input, symbol_pattern)?;
    let start = start + symbol_pattern.len();
    let end = memchr::memchr(b'"', &input[start..])?;
    let symbol = &input[start..start + end];
    let symbol = unsafe { std::str::from_utf8_unchecked(symbol) };

    let funding_fee_pattern = b"\"r\":\"";
    let start = memchr::memmem::find(input, funding_fee_pattern)?;
    let start = start + funding_fee_pattern.len();
    let end = memchr::memchr(b'"', &input[start..])?;
    let funding_fee = &input[start..start + end];
    let funding_fee = unsafe { std::str::from_utf8_unchecked(funding_fee) };
    let funding_fee = funding_fee.parse().ok()?;

    Some((symbol.to_string(), funding_fee))
}

pub fn generate_funding_sub_url(symbols: &[&str]) -> String {
    let streams = symbols
        .iter()
        .map(|s| format!("{}usdt@markPrice", s.to_lowercase()))
        .collect::<Vec<String>>()
        .join("/");
    format!("wss://fstream.binance.com:443/stream?streams={}", streams)
}
