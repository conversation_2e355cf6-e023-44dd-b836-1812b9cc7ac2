use std::{fmt::Display, str::FromStr};

use serde::Deserialize;
use serde_aux::prelude::deserialize_number_from_string;

use crate::encoding::bn_futures_order::OrderSide;

#[derive(Debug)]
pub struct OrderError {
    pub id: Option<u64>,
    pub error: String,
}

impl Display for OrderError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "OrderError: id: {:?}, error: {}", self.id, self.error)
    }
}

#[derive(Debug)]
pub enum FuturesOrderResponse {
    ListenKey(String),
    Error(OrderError),
    OrderResponse(u64, OrderStatus, String, OrderSide),
    Unkown(Option<u64>),
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Or<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>)]
pub enum OrderStatus {
    New,
    PartiallyFilled,
    Filled,
    Canceled,
    Rejected,
    Expired,
}

impl FromStr for OrderStatus {
    type Err = ();
    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s {
            "NEW" => Ok(OrderStatus::New),
            "PARTIALLY_FILLED" => Ok(OrderStatus::PartiallyFilled),
            "FILLED" => Ok(OrderStatus::Filled),
            "CANCELED" => Ok(OrderStatus::Canceled),
            "REJECTED" => Ok(OrderStatus::Rejected),
            "EXPIRED" => Ok(OrderStatus::Expired),
            _ => Err(()),
        }
    }
}

#[derive(Debug)]
pub struct OrderTradeUpdate {
    pub order_id: u64,
    pub order_status: OrderStatus,
    pub order_side: OrderSide,
    pub price: f64,
    pub quantity: f64,
    pub last_filled: f64,
    pub acc_filled: f64,
    pub avg_price: f64,
    pub symbol: String,
}

#[derive(Debug)]
pub enum UserDataResponse {
    OrderTradeUpdate(OrderTradeUpdate),
    AccountUpdate(AccountUpdateEvent),
}

pub fn parse_futures_order_response(input: &[u8]) -> Option<FuturesOrderResponse> {
    // {"id":"d3df8a61-98ea-4fe0-8f4e-0fcea5d418b0","status":200,"result":{"listenKey":"YNKRAR1MIVOA6Qn90Vv8JgwqXvbAKdpBTBzx1M04b1jfzNlPKa4uRUDx8glXTwWL"},"rateLimits":[{"rateLimitType":"REQUEST_WEIGHT","interval":"MINUTE","intervalNum":1,"limit":2400,"count":8}]}
    // {"id":"****************","status":429,"error":{"code":-1015,"msg":"Too many new orders; current limit is 300 orders per TEN_SECONDS."},"rateLimits":[{"rateLimitType":"REQUEST_WEIGHT","interval":"MINUTE","intervalNum":1,"limit":-1,"count":-1},{"rateLimitType":"ORDERS","interval":"SECOND","intervalNum":10,"limit":300,"count":2757}]}
    // "{\"id\":\"****************\",\"status\":200,\"result\":{\"orderId\":*********,\"symbol\":\"TRUMPUSDC\",\"status\":\"NEW\",\"clientOrderId\":\"****************\",\"price\":\"8.380000\",\"avgPrice\":\"0.00\",\"origQty\":\"1.00\",\"executedQty\":\"0.00\",\"cumQty\":\"0.00\",\"cumQuote\":\"0.00000000\",\"timeInForce\":\"GTX\",\"type\":\"LIMIT\",\"reduceOnly\":false,\"closePosition\":false,\"side\":\"BUY\",\"positionSide\":\"BOTH\",\"stopPrice\":\"0.000000\",\"workingType\":\"CONTRACT_PRICE\",\"priceProtect\":false,\"origType\":\"LIMIT\",\"priceMatch\":\"NONE\",\"selfTradePreventionMode\":\"EXPIRE_MAKER\",\"goodTillDate\":0,\"updateTime\":1756474070702},\"rateLimits\":[{\"rateLimitType\":\"REQUEST_WEIGHT\",\"interval\":\"MINUTE\",\"intervalNum\":1,\"limit\":-1,\"count\":-1},{\"rateLimitType\":\"ORDERS\",\"interval\":\"SECOND\",\"intervalNum\":10,\"limit\":300,\"count\":2},{\"rateLimitType\":\"ORDERS\",\"interval\":\"MINUTE\",\"intervalNum\":1,\"limit\":1200,\"count\":16}]}"
    let listen_key_pattern = b"\"listenKey\":\"";
    match memchr::memmem::find(input, listen_key_pattern) {
        Some(idx) => {
            let start = idx + listen_key_pattern.len();
            let end = memchr::memchr(b'"', &input[start..])?;
            let listen_key = &input[start..start + end];
            let listen_key: &str = unsafe { std::mem::transmute(listen_key) };
            Some(FuturesOrderResponse::ListenKey(listen_key.to_string()))
        }
        None => {
            let id_pattern = b"\"id\":\"";
            let start = memchr::memmem::find(input, id_pattern)? + id_pattern.len();
            let end = memchr::memchr(b'"', &input[start..])?;
            let id: &str = unsafe { std::mem::transmute(&input[start..start + end]) };
            let id: Option<u64> = match id.parse() {
                Ok(id) => Some(id),
                Err(_) => None,
            };
            let status_pattern = b"\"status\":";
            let start = memchr::memmem::find(input, status_pattern)? + status_pattern.len();
            let end = memchr::memchr(b',', &input[start..])?;
            let status: &str = unsafe { std::mem::transmute(&input[start..start + end]) };
            let status: u16 = match status.parse() {
                Ok(status) => status,
                Err(_) => return None,
            };
            if status != 200 {
                return Some(FuturesOrderResponse::Error(OrderError {
                    id,
                    error: String::from_utf8_lossy(input.as_ref()).to_string(),
                }));
            }
            let error_pattern = b"\"error\":";
            if let Some(start) = memchr::memmem::find(input, error_pattern) {
                let start = start + error_pattern.len();
                let end = memchr::memchr(b'}', &input[start..])?;
                let error: &str = unsafe { std::mem::transmute(&input[start..start + end]) };
                Some(FuturesOrderResponse::Error(OrderError {
                    id,
                    error: error.to_string(),
                }))
            } else {
                let order_id_pattern = b"\"clientOrderId\":\"";
                if let Some(start) = memchr::memmem::find(input, order_id_pattern) {
                    let start = start + order_id_pattern.len();
                    let end = memchr::memchr(b'"', &input[start..])?;
                    let order_id: &str = unsafe { std::mem::transmute(&input[start..start + end]) };
                    let order_id: u64 = match order_id.parse() {
                        Ok(id) => id,
                        Err(_) => {
                            crate::error!("parse order id error: {}", order_id);
                            return None;
                        }
                    };
                    let status_pattern = b"\"status\":\"";
                    if let Some(start) = memchr::memmem::find(input, status_pattern) {
                        let start = start + status_pattern.len();
                        let end = memchr::memchr(b'"', &input[start..])?;
                        let status: &str =
                            unsafe { std::mem::transmute(&input[start..start + end]) };
                        let status: OrderStatus = match status.parse() {
                            Ok(status) => status,
                            Err(_) => {
                                crate::error!("parse order status error: {}", status);
                                return None;
                            }
                        };
                        let symbol_pattern = b"\"symbol\":\"";
                        let start =
                            memchr::memmem::find(input, symbol_pattern)? + symbol_pattern.len();
                        let end = memchr::memchr(b'"', &input[start..])?;
                        let symbol: &str =
                            unsafe { std::mem::transmute(&input[start..start + end]) };
                        let side_pattern = b"\"side\":\"";
                        let start = memchr::memmem::find(input, side_pattern)? + side_pattern.len();
                        let end = memchr::memchr(b'"', &input[start..])?;
                        let side: &str = unsafe { std::mem::transmute(&input[start..start + end]) };
                        let side: OrderSide = match side.parse() {
                            Ok(side) => side,
                            Err(_) => {
                                crate::error!("parse order side error: {}", side);
                                return None;
                            }
                        };
                        Some(FuturesOrderResponse::OrderResponse(
                            order_id,
                            status,
                            symbol.to_string(),
                            side,
                        ))
                    } else {
                        Some(FuturesOrderResponse::Unkown(id))
                    }
                } else {
                    Some(FuturesOrderResponse::Unkown(id))
                }
            }
        }
    }
}

#[derive(Debug, Deserialize)]
pub struct AccountUpdateEvent {
    #[serde(rename = "e")]
    pub event_type: String, // "ACCOUNT_UPDATE"
    #[serde(rename = "E")]
    pub event_time: i64, // ms
    #[serde(rename = "T")]
    pub transaction_time: i64, // ms
    #[serde(rename = "a")]
    pub data: AccountUpdateData,
}

/// a: Update Data
#[derive(Debug, Deserialize)]
pub struct AccountUpdateData {
    /// reason type, e.g. ORDER / FUNDING_FEE / DEPOSIT ...
    #[serde(rename = "m")]
    pub reason: String,
    /// balances
    #[serde(rename = "B", default)]
    pub balances: Vec<AccountBalance>,
    /// positions (only changed symbols for USDⓈ-M per doc)
    #[serde(rename = "P", default)]
    pub positions: Vec<AccountPosition>,
}

/// 余额
#[derive(Debug, Deserialize)]
pub struct AccountBalance {
    #[serde(rename = "a")]
    pub asset: String, // "USDT" / "BUSD" (历史)
    #[serde(rename = "wb")]
    pub wallet_balance: String, // 钱包余额
    #[serde(rename = "cw")]
    pub cross_wallet_balance: String, // 全仓余额
    /// Balance Change except PnL and Commission
    #[serde(rename = "bc")]
    pub balance_change_ex_fee_pnl: Option<String>,
}

/// 持仓
#[derive(Debug, Deserialize)]
pub struct AccountPosition {
    #[serde(rename = "s")]
    pub symbol: String, // "BTCUSDT"
    #[serde(rename = "pa")]
    pub position_amt: String, // 数量：正=多，负=空（单向模式下）；双向模式看 ps
    #[serde(rename = "ep")]
    pub entry_price: String, // 入场价
    #[serde(rename = "bep")]
    pub break_even_price: Option<String>, // breakeven price
    #[serde(rename = "cr")]
    pub cum_realized: String, // 预扣前累计已实现
    #[serde(rename = "up")]
    pub unrealized_pnl: String, // 未实现盈亏
    #[serde(rename = "mt")]
    pub margin_type: String, // "isolated" / "cross"
    #[serde(rename = "iw")]
    pub isolated_wallet: Option<String>, // 逐仓钱包
    #[serde(rename = "ps")]
    pub position_side: String, // "BOTH" / "LONG" / "SHORT"
}

fn parse_account_update(input: &[u8]) -> Option<AccountUpdateEvent> {
    match serde_json::from_slice::<AccountUpdateEvent>(input) {
        Ok(account_update) => Some(account_update),
        Err(_) => None,
    }
}

pub fn parse_user_data_response(input: &[u8]) -> Option<UserDataResponse> {
    /*
    {"e":"ORDER_TRADE_UPDATE","T":*************,"E":*************,"o":{"s":"BTCUSDC","c":"web_jZvk9nkynDRC1GOKdqGv","S":"BUY","o":"MARKET","f":"GTC","q":"0.005","p":"0","ap":"113497.5","sp":"0","x":"TRADE","X":"FILLED","i":***********,"l":"0.005","z":"0.005","L":"113497.5","n":"0.********","N":"BNB","T":*************,"t":*********,"b":"0","a":"113.6578","m":false,"R":true,"wt":"CONTRACT_PRICE","ot":"MARKET","ps":"BOTH","cp":false,"rp":"0.********","pP":false,"si":0,"ss":0,"V":"EXPIRE_MAKER","pm":"NONE","gtd":0}}
     */
    let trade_lite_pattern = b"\"e\":\"TRADE_LITE\"";
    if let Some(_) = memchr::memmem::find(input, trade_lite_pattern) {
        return None;
    }
    let account_update_pattern = b"\"e\":\"ACCOUNT_UPDATE\"";
    if let Some(_) = memchr::memmem::find(input, account_update_pattern) {
        return parse_account_update(input).map(UserDataResponse::AccountUpdate);
    }
    let order_trade_update_pattern = b"\"e\":\"ORDER_TRADE_UPDATE\"";
    memchr::memmem::find(input, order_trade_update_pattern)?;
    let order_id_pattern = b"\"c\":\"";
    let start = memchr::memmem::find(input, order_id_pattern)? + order_id_pattern.len();
    let end = memchr::memchr(b'"', &input[start..])?;
    let order_id: &str = unsafe { std::mem::transmute(&input[start..start + end]) };
    let order_id: u64 = match order_id.parse() {
        Ok(id) => id,
        Err(_) => {
            crate::error!("parse order id error: {}", order_id);
            return None;
        }
    };
    let order_status_pattern = b"\"X\":\"";
    let start = memchr::memmem::find(input, order_status_pattern)? + order_status_pattern.len();
    let end = match memchr::memchr(b'"', &input[start..]) {
        Some(end) => end,
        None => {
            crate::error!("parse order status error: {}", order_id);
            return None;
        }
    };
    let order_status: &str = unsafe { std::mem::transmute(&input[start..start + end]) };
    let order_side_pattern = b"\"S\":\"";
    let start = memchr::memmem::find(input, order_side_pattern)? + order_side_pattern.len();
    let end = match memchr::memchr(b'"', &input[start..]) {
        Some(end) => end,
        None => {
            crate::error!("parse order side error: {}", order_id);
            return None;
        }
    };
    let order_side: &str = unsafe { std::mem::transmute(&input[start..start + end]) };
    let order_side: OrderSide = order_side.parse().unwrap();
    let price_pattern = b"\"L\":\"";
    let start = memchr::memmem::find(input, price_pattern)? + price_pattern.len();
    let end = match memchr::memchr(b'"', &input[start..]) {
        Some(end) => end,
        None => {
            crate::error!("parse order price error: {}", order_id);
            return None;
        }
    };
    let price: &str = unsafe { std::mem::transmute(&input[start..start + end]) };
    let price: f64 = price.parse().unwrap();

    let last_filled_pattern = b"\"l\":\"";
    let start = memchr::memmem::find(input, last_filled_pattern)? + last_filled_pattern.len();
    let end = match memchr::memchr(b'"', &input[start..]) {
        Some(end) => end,
        None => {
            crate::error!("parse order last filled error: {}", order_id);
            return None;
        }
    };
    let last_filled: &str = unsafe { std::mem::transmute(&input[start..start + end]) };
    let last_filled: f64 = last_filled.parse().unwrap();

    let acc_filled_pattern = b"\"z\":\"";
    let start = memchr::memmem::find(input, acc_filled_pattern)? + acc_filled_pattern.len();
    let end = match memchr::memchr(b'"', &input[start..]) {
        Some(end) => end,
        None => {
            crate::error!("parse order acc filled error: {}", order_id);
            return None;
        }
    };
    let acc_filled: &str = unsafe { std::mem::transmute(&input[start..start + end]) };
    let acc_filled: f64 = acc_filled.parse().unwrap();

    let quantity_pattern = b"\"q\":\"";
    let start = memchr::memmem::find(input, quantity_pattern)? + quantity_pattern.len();
    let end = match memchr::memchr(b'"', &input[start..]) {
        Some(end) => end,
        None => {
            crate::error!("parse order quantity error: {}", order_id);
            return None;
        }
    };
    let quantity: &str = unsafe { std::mem::transmute(&input[start..start + end]) };
    let quantity: f64 = quantity.parse().unwrap();
    let average_price_pattern = b"\"ap\":\"";
    let start = memchr::memmem::find(input, average_price_pattern)? + average_price_pattern.len();
    let end = match memchr::memchr(b'"', &input[start..]) {
        Some(end) => end,
        None => {
            crate::error!("parse order average price error: {}", order_id);
            return None;
        }
    };
    let average_price: &str = unsafe { std::mem::transmute(&input[start..start + end]) };
    let average_price: f64 = average_price.parse().unwrap();

    let symbol_pattern = b"\"s\":\"";
    let start = memchr::memmem::find(input, symbol_pattern)? + symbol_pattern.len();
    let end = match memchr::memchr(b'"', &input[start..]) {
        Some(end) => end,
        None => {
            crate::error!("parse order symbol error: {}", order_id);
            return None;
        }
    };
    let symbol: &str = unsafe { std::mem::transmute(&input[start..start + end]) };

    Some(UserDataResponse::OrderTradeUpdate(OrderTradeUpdate {
        order_id,
        order_status: order_status.parse().unwrap(),
        order_side,
        price,
        quantity,
        avg_price: average_price,
        symbol: symbol.to_string(),
        last_filled,
        acc_filled,
    }))
}

#[derive(Debug, Deserialize)]
#[allow(non_snake_case)]
pub struct AccountInfoV3 {
    // —— 汇总（字符串表示的十进制）——
    pub totalInitialMargin: String,
    pub totalMaintMargin: String,
    pub totalWalletBalance: String,
    pub totalUnrealizedProfit: String,
    pub totalMarginBalance: String,
    pub totalPositionInitialMargin: String,
    pub totalOpenOrderInitialMargin: String,
    pub totalCrossWalletBalance: String,
    pub totalCrossUnPnl: String,
    pub availableBalance: String,
    pub maxWithdrawAmount: String,

    // —— 明细 ——
    pub assets: Vec<AssetInfo>,
    pub positions: Vec<PositionInfo>,
}

/// 资产条目（USDT/USDC/BTC 等报价资产）
#[derive(Debug, Deserialize)]
#[allow(non_snake_case)]
pub struct AssetInfo {
    pub asset: String,
    pub walletBalance: String,
    pub unrealizedProfit: String,
    pub marginBalance: String,
    pub maintMargin: String,
    pub initialMargin: String,
    pub positionInitialMargin: String,
    pub openOrderInitialMargin: String,
    pub crossWalletBalance: String,
    pub crossUnPnl: String,
    pub availableBalance: String,
    pub maxWithdrawAmount: String,
    pub updateTime: i64, // ms
}

#[derive(Debug, Deserialize)]
#[allow(non_snake_case)]
pub struct PositionInfo {
    pub symbol: String,
    pub positionSide: String, // "BOTH" | "LONG" | "SHORT"
    pub positionAmt: String,  // 正=多 负=空（单向模式下）
    pub unrealizedProfit: String,
    pub isolatedMargin: String,
    pub notional: String,
    pub isolatedWallet: String,
    pub initialMargin: String,
    pub maintMargin: String,
    pub updateTime: i64, // ms
}

pub fn parse_position_snapshot(input: &[u8]) -> Option<AccountInfoV3> {
    match serde_json::from_slice::<AccountInfoV3>(input) {
        Ok(account_info) => Some(account_info),
        Err(_) => None,
    }
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "UPPERCASE")]
pub enum PositionSide {
    BOTH,
    LONG,
    SHORT,
}

#[derive(Debug, Deserialize)]
pub struct PositionRisk {
    pub symbol: String,

    #[serde(rename = "positionSide")]
    pub position_side: PositionSide,

    #[serde(
        rename = "positionAmt",
        deserialize_with = "deserialize_number_from_string"
    )]
    pub position_amt: f64,

    #[serde(
        rename = "entryPrice",
        deserialize_with = "deserialize_number_from_string"
    )]
    pub entry_price: f64,

    #[serde(
        rename = "breakEvenPrice",
        deserialize_with = "deserialize_number_from_string"
    )]
    pub break_even_price: f64,

    #[serde(
        rename = "markPrice",
        deserialize_with = "deserialize_number_from_string"
    )]
    pub mark_price: f64,

    #[serde(
        rename = "unRealizedProfit",
        deserialize_with = "deserialize_number_from_string"
    )]
    pub unrealized_profit: f64,

    #[serde(
        rename = "liquidationPrice",
        deserialize_with = "deserialize_number_from_string"
    )]
    pub liquidation_price: f64,

    #[serde(rename = "updateTime")]
    pub update_time_ms: i64,
}

pub fn parse_position_risk_json(input: &[u8]) -> Option<Vec<PositionRisk>> {
    match serde_json::from_slice::<Vec<PositionRisk>>(input) {
        Ok(positions) => Some(positions),
        Err(e) => {
            crate::error!("parse position risk error: {}", e);
            None
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_parse_user_data_response() {
        let input = b"{\"e\":\"ORDER_TRADE_UPDATE\",\"T\":*************,\"E\":*************,\"o\":{\"s\":\"BTCUSDC\",\"c\":\"1234556678\",\"S\":\"BUY\",\"o\":\"MARKET\",\"f\":\"GTC\",\"q\":\"0.005\",\"p\":\"0\",\"ap\":\"113497.5\",\"sp\":\"0\",\"x\":\"TRADE\",\"X\":\"FILLED\",\"i\":***********,\"l\":\"0.005\",\"z\":\"0.005\",\"L\":\"113497.5\",\"n\":\"0.********\",\"N\":\"BNB\",\"T\":*************,\"t\":*********,\"b\":\"0\",\"a\":\"113.6578\",\"m\":false,\"R\":true,\"wt\":\"CONTRACT_PRICE\",\"ot\":\"MARKET\",\"ps\":\"BOTH\",\"cp\":false,\"rp\":\"0.********\",\"pP\":false,\"si\":0,\"ss\":0,\"V\":\"EXPIRE_MAKER\",\"pm\":\"NONE\",\"gtd\":0}}";
        let response = parse_user_data_response(input).unwrap();
        println!("{:?}", response);
    }
}
