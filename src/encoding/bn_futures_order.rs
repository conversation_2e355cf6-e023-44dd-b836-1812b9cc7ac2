use std::str::FromStr;

use crate::{
    encoding::{
        price::{RoundingMode, format_price_with_tick_mode},
        quantity::normalize_quantity,
    },
    engine::{
        cross_exchange::cross_exchange_const::{BN_LOT_SIZE, BN_PRICE_TICK, UnifiedCurrency},
        futures_const::{
            FUTURES_API_KEY, FUTURES_ORDER_QUANTITY, FUTURES_QUANTITY_TICK_SIZE,
            FUTURES_USDC_SYMBOL,
        },
        opportunity_simulator::{Opportunity, OpportunityType},
    },
    utils::perf::{system_now_in_ms, system_now_in_us},
};

#[derive(PartialEq, Eq, PartialOrd, Ord, Clone, Copy, Debug)]
pub enum OrderSide {
    Buy,
    Sell,
}

impl OrderSide {
    pub fn to_string(&self) -> &str {
        match self {
            OrderSide::Buy => "BUY",
            OrderSide::Sell => "SELL",
        }
    }

    pub fn sign(&self) -> f64 {
        match self {
            OrderSide::Buy => 1.0,
            OrderSide::Sell => -1.0,
        }
    }

    pub fn reverse(&self) -> OrderSide {
        match self {
            OrderSide::Buy => OrderSide::Sell,
            OrderSide::Sell => OrderSide::Buy,
        }
    }
}

impl FromStr for OrderSide {
    type Err = ();
    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s {
            "BUY" => Ok(OrderSide::Buy),
            "SELL" => Ok(OrderSide::Sell),
            _ => Err(()),
        }
    }
}

pub fn generate_futures_user_data_ping_request() -> String {
    format!(
        r#"{{
  "id": "815d5fce-0880-4287-a567-80badf004c74",
  "method": "userDataStream.ping",
  "params": {{
    "apiKey": "{}"
    }}
}}"#,
        FUTURES_API_KEY
    )
}

pub fn place_order(
    price: f64,
    side: OrderSide,
    qty: f64,
    symbol: &str,
    order_id: u64,
    reduce_only: bool,
    tif: &str,
) -> String {
    let index = UnifiedCurrency::from_symbol(symbol).unwrap() as usize;
    let price_tick = BN_PRICE_TICK[index];
    let price = match side {
        OrderSide::Buy => format_price_with_tick_mode(price, price_tick, RoundingMode::Floor),
        OrderSide::Sell => format_price_with_tick_mode(price, price_tick, RoundingMode::Ceil),
    };
    let lot_size = BN_LOT_SIZE[index];
    let qty = normalize_quantity(qty, lot_size);
    let ts = system_now_in_ms();
    let reuslt = format!(
        r#"
{{
  "id": "{}",
  "method": "order.place",
  "params": {{
        "price": {},
        "newClientOrderId": "{}",
        "quantity": {},
        "side": "{}",
        "symbol": "{}",
        "timeInForce": "{}",
        "timestamp": {},
        "type": "LIMIT",
        "reduceOnly": {}
    }}
}}"#,
        order_id,
        price,
        order_id,
        qty,
        side.to_string(),
        symbol,
        tif,
        ts,
        reduce_only,
    );
    reuslt
}

pub fn generate_futures_order_request(
    price: f64,
    side: OrderSide,
    symbol: &str,
    order_id: u64,
) -> String {
    let reuslt = format!(
        r#"
{{
  "id": "{}",
  "method": "order.place",
  "params": {{
        "price": {:.2},
        "newClientOrderId": "{}",
        "quantity": {:.3},
        "side": "{}",
        "symbol": "{}",
        "timeInForce": "GTX",
        "timestamp": {},
        "type": "LIMIT"
    }}
}}"#,
        order_id,
        price,
        order_id,
        FUTURES_ORDER_QUANTITY,
        side.to_string(),
        symbol,
        order_id / 1000,
    );
    crate::info!(
        "Order request: id: {} side: {} price: {}, qty: {}",
        order_id,
        side.to_string(),
        price,
        FUTURES_ORDER_QUANTITY,
    );
    reuslt
}

pub fn generate_futures_order_ioc_request(
    price: f64,
    side: OrderSide,
    symbol: &str,
    order_id: u64,
    qty: f64,
) -> String {
    let index = UnifiedCurrency::from_symbol(symbol).unwrap() as usize;
    let lot_size = BN_LOT_SIZE[index];
    let price_tick = BN_PRICE_TICK[index];
    let price = format_price_with_tick_mode(price, price_tick, RoundingMode::Floor);
    let qty = normalize_quantity(qty, lot_size);
    let ts = system_now_in_ms();
    let reuslt = format!(
        r#"
{{
  "id": "{}",
  "method": "order.place",
  "params": {{
        "price": {},
        "newClientOrderId": "{}",
        "quantity": {},
        "side": "{}",
        "symbol": "{}",
        "timeInForce": "IOC",
        "timestamp": {},
        "type": "LIMIT"
    }}
}}"#,
        order_id,
        price,
        order_id,
        qty,
        side.to_string(),
        symbol,
        ts,
    );
    reuslt
}

pub fn generate_futures_market_order_request(
    side: OrderSide,
    qty: f64,
    symbol: &str,
    order_id: u64,
    reduce_only: bool,
) -> String {
    let ts = system_now_in_ms();
    let lot_size = BN_LOT_SIZE[UnifiedCurrency::from_symbol(symbol).unwrap() as usize];
    let qty = normalize_quantity(qty, lot_size);
    crate::info!("BN: id: {} {}", order_id, qty);
    let reduce_only_str = if reduce_only { "true" } else { "false" };
    let reuslt = format!(
        r#"
{{
  "id": "{}",
  "method": "order.place",
  "params": {{
        "newClientOrderId": "{}",
        "quantity": {},
        "side": "{}",
        "symbol": "{}",
        "timestamp": {},
        "type": "MARKET",
        "reduceOnly": {}
    }}
}}"#,
        order_id,
        order_id,
        qty,
        side.to_string(),
        symbol,
        ts,
        reduce_only_str,
    );
    reuslt
}

pub fn generate_take_profit_and_stop_loss_order_request(
    opportunity: &Opportunity,
    symbol: &str,
) -> String {
    let order_type = match opportunity.opportunity_type {
        OpportunityType::TakeProfit => "TAKE_PROFIT",
        OpportunityType::StopLoss => "STOP",
        _ => panic!(
            "Invalid opportunity type: {:?}",
            opportunity.opportunity_type
        ),
    };
    let price = opportunity.price;
    let stop_price = opportunity.stop_price;
    let side = opportunity.side;
    let order_id = opportunity.id;
    let result = format!(
        r#"
{{
  "id": "{}",
  "method": "order.place",
  "params": {{
        "price": {:.2},
        "stopPrice": {:.2},
        "newClientOrderId": "{}",
        "quantity": {:.3},
        "side": "{}",
        "symbol": "{}",
        "timeInForce": "GTX",
        "timestamp": {},
        "type": "{}"
    }}
}}"#,
        order_id,
        price,
        stop_price,
        order_id,
        FUTURES_ORDER_QUANTITY,
        side.to_string(),
        symbol,
        order_id / 1000,
        order_type,
    );
    crate::debug!(
        "Order request: side: {} price: {}, qty: {}",
        side.to_string(),
        price,
        FUTURES_QUANTITY_TICK_SIZE,
    );
    result
}

pub fn generate_futures_reduce_only_request(
    price: f64,
    side: OrderSide,
    quantity: f64,
    symbol: &str,
) -> String {
    let order_id = system_now_in_us();
    let quantity = (quantity / FUTURES_QUANTITY_TICK_SIZE).round() * FUTURES_QUANTITY_TICK_SIZE;
    let result = format!(
        r#"
{{
  "id": "{}",
  "method": "order.place",
  "params": {{
        "newClientOrderId": "{}",
        "price": {:.2},
        "quantity": {:.3},
        "side": "{}",
        "symbol": "{}",
        "timeInForce": "IOC",
        "timestamp": {},
        "type": "LIMIT",
        "reduceOnly": true
    }}
}}"#,
        order_id,
        order_id,
        price,
        quantity,
        side.to_string(),
        symbol,
        order_id / 1000,
    );
    crate::debug!("Order request: {}", result);
    result
}

pub fn generate_futures_cancel_order_request(order_id: u64) -> String {
    /*
        {
           "id": "5633b6a2-90a9-4192-83e7-925c90b6a2fd",
        "method": "order.cancel",
        "params": {
            "apiKey": "HsOehcfih8ZRxnhjp2XjGXhsOBd6msAhKz9joQaWwZ7arcJTlD2hGOGQj1lGdTjR",
            "orderId": 283194212,
            "symbol": "BTCUSDT",
            "timestamp": 1703439070722,
            "signature": "b09c49815b4e3f1f6098cd9fbe26a933a9af79803deaaaae03c29f719c08a8a8"
        }
    } */

    let now = system_now_in_us();
    let result = format!(
        r#"
{{
    "id": "{}",
    "method": "order.cancel",
    "params": {{
        "origClientOrderId": "{}",
        "symbol": "{}",
        "timestamp": {}
    }}
}}
"#,
        order_id,
        order_id,
        FUTURES_USDC_SYMBOL,
        now / 1000
    );
    crate::debug!("Cancel order request: order_id: {}", order_id);
    result
}

pub fn cancel_order(order_id: u64) -> String {
    let now = system_now_in_us();
    let result = format!(
        r#"
{{
    "id": "{}",
    "method": "order.cancel",
    "params": {{
        "origClientOrderId": "{}",
        "symbol": "{}",
        "timestamp": {}
    }}
}}
"#,
        order_id,
        order_id,
        FUTURES_USDC_SYMBOL,
        now / 1000
    );
    crate::debug!("Cancel order request: order_id: {}", order_id);
    result
}

pub fn cancel_order_with_symbol(order_id: u64, symbol: &str) -> String {
    let now = system_now_in_us();
    let result = format!(
        r#"
{{
    "id": "{}",
    "method": "order.cancel",
    "params": {{
        "origClientOrderId": "{}",
        "symbol": "{}",
        "timestamp": {}
    }}
}}
"#,
        order_id,
        order_id,
        symbol,
        now / 1000
    );
    crate::debug!("Cancel order request: order_id: {}", order_id);
    result
}
