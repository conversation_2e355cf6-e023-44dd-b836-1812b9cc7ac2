use serde::Deserialize;
use serde_json::Value;

pub fn generate_bbo_subscribe_request(symbol: &str) -> String {
    format!(
        r#"
{{
    "op": "subscribe",
    "args": [
        {{
            "channel": "bbo-tbt",
            "instId": "{}"
        }}
    ]
}}"#,
        symbol
    )
}

pub const OKX_PUB_URL: &str = "wss://wseea.okx.com:8443/ws/v5/public";

#[derive(Debug, Deserialize, Clone, Default)]
struct Arg {
    channel: String,
    #[serde(default)]
    instId: Option<String>,
    #[serde(default)]
    instType: Option<String>,
}

#[derive(Debug, Deserialize)]
struct Envelope<T> {
    #[serde(default)]
    arg: Arg,
    #[serde(default)]
    data: Vec<T>,
    #[serde(default)]
    action: Option<String>,
}

/* BBO */
#[derive(Debug, Deserialize, <PERSON>lone, <PERSON><PERSON>ult)]
struct Bbo {
    instId: String,
    bestBid: StringGATE_BBO_TOKEN_1,
    bestBidSz: String,
    bestAsk: String,
    bestAskSz: String,
    ts: String,
}

/* Books（这里使用不定长的Vec<Vec<String>> 更稳健） */
#[derive(Debug, Deserialize, Clone)]
struct Book {
    instId: String,
    asks: Vec<Vec<String>>,
    bids: Vec<Vec<String>>,
    ts: String,
    #[serde(default)]
    checksum: Option<i64>,
}

/* Trades（聚合） */
#[derive(Debug, Deserialize, Clone)]
struct TradeAgg {
    instId: String,
    tradeId: String,
    px: String,
    sz: String,
    side: String, // buy/sell
    ts: String,
}

/* 统一的市场事件 */
#[derive(Debug, Clone)]
enum MarketEvent {
    Bbo(Bbo),
    BooksTop10 {
        inst_id: String,
        ts: String,
        asks10: Vec<Vec<String>>,
        bids10: Vec<Vec<String>>,
        checksum: Option<i64>,
    },
    Trade(TradeAgg),
}

fn truncate_levels(levels: &[Vec<String>], n: usize) -> Vec<Vec<String>> {
    let m = std::cmp::min(levels.len(), n);
    levels[..m].to_vec()
}

pub fn parse_bbo(data: &[u8]) -> Option<Bbo> {
    match serde_json::from_slice::<Envelope<Bbo>>(data) {
        Ok(env) => env.data.into_iter().next(),
        Err(e) => {
            crate::error!("parse bbo error: {}", e);
            None
        }
    }
}

fn parse_market(txt: &str) -> Vec<MarketEvent> {
    // 快速判断：必须包含 "arg"
    if !txt.as_bytes().contains(&b'"') || !txt.contains("\"arg\"") {
        return vec![];
    }
    let v: Value = match serde_json::from_str(txt) {
        Ok(x) => x,
        Err(_) => return vec![],
    };
    let Some(arg) = v.get("arg") else {
        return vec![];
    };
    let channel = arg
        .get("channel")
        .and_then(|c| c.as_str())
        .unwrap_or_default();

    match channel {
        "bbo-tbt" => {
            if let Ok(env) = serde_json::from_value::<Envelope<Bbo>>(v) {
                return env.data.into_iter().map(MarketEvent::Bbo).collect();
            }
            vec![]
        }
        "books" | "books50-l2-tbt" | "books-l2-tbt" => {
            if let Ok(env) = serde_json::from_value::<Envelope<Book>>(v) {
                let mut out = Vec::new();
                for b in env.data {
                    let asks10 = truncate_levels(&b.asks, 10);
                    let bids10 = truncate_levels(&b.bids, 10);
                    out.push(MarketEvent::BooksTop10 {
                        inst_id: b.instId,
                        ts: b.ts,
                        asks10,
                        bids10,
                        checksum: b.checksum,
                    });
                }
                return out;
            }
            vec![]
        }
        "trades" => {
            if let Ok(env) = serde_json::from_value::<Envelope<TradeAgg>>(v) {
                return env.data.into_iter().map(MarketEvent::Trade).collect();
            }
            vec![]
        }
        _ => vec![],
    }
}
