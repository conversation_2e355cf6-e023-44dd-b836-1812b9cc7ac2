#![allow(clippy::needless_lifetimes)]
use heapless::Vec as HVec;
use lexical_core::FromLexical;
use simd_json::BorrowedValue;
use simd_json::prelude::*;

use crate::encoding::bn_futures_order::OrderSide;
use crate::engine::cross_exchange::cross_exchange_const::GATE_QUANTO_MULTIPLIER;
use crate::utils::perf::system_now_in_secs; // 带来 as_array(), as_str(), as_u64() 等

/// ------------------------ 常量与类型 ------------------------
pub const MAX_LEVELS: usize = 50; // 订阅 400 档（20ms=50档, 100ms=400档，按需改）
/// Bid/Ask 单个价位
#[derive(Clone, Copy, Debug)]
pub struct Level {
    pub price: f64,
    pub qty: i64,
}
impl Default for Level {
    fn default() -> Self {
        Self { price: 0.0, qty: 0 }
    }
}

/// 深度一侧（买 or 卖）。用定长数组 + 当前长度，全部在栈上。
#[derive(Clone, Copy, Debug)]
pub struct SideBook<const N: usize, const IS_BID: bool> {
    pub levels: [Level; N],
    pub len: usize,
}
impl<const N: usize, const IS_BID: bool> SideBook<N, IS_BID> {
    #[inline]
    pub fn new() -> Self {
        Self {
            levels: [Level::default(); N],
            len: 0,
        }
    }
    /// 二分查找：按价位精确匹配，返回 Ok(index) 或 Err(插入位)
    #[inline]
    pub fn bsearch(&self, price: f64) -> Result<usize, usize> {
        let mut lo = 0usize;
        let mut hi = self.len;
        while lo < hi {
            let mid = (lo + hi) >> 1;
            let p = self.levels[mid].price;
            let ord = if IS_BID {
                if price > p {
                    std::cmp::Ordering::Less
                } else if price < p {
                    std::cmp::Ordering::Greater
                } else {
                    std::cmp::Ordering::Equal
                }
            } else {
                if price < p {
                    std::cmp::Ordering::Less
                } else if price > p {
                    std::cmp::Ordering::Greater
                } else {
                    std::cmp::Ordering::Equal
                }
            };
            match ord {
                std::cmp::Ordering::Less => hi = mid,
                std::cmp::Ordering::Greater => lo = mid + 1,
                std::cmp::Ordering::Equal => return Ok(mid),
            }
        }
        Err(lo)
    }
    /// upsert：qty=0 删除；否则插入/替换；保持有序与容量
    #[inline]
    pub fn upsert(&mut self, price: f64, qty: i64) {
        match self.bsearch(price) {
            Ok(idx) => {
                if qty == 0 {
                    // 删除
                    if idx + 1 < self.len {
                        self.levels.copy_within(idx + 1..self.len, idx);
                    }
                    self.len -= 1;
                } else {
                    self.levels[idx].qty = qty;
                }
            }
            Err(pos) => {
                if qty == 0 {
                    return;
                } // 无需插入
                if self.len == N {
                    // 满了：尾部丢弃一档（最坏位）。只在有需要且新价位进入范围时做。
                    if (IS_BID && pos < self.len) || (!IS_BID && pos < self.len) {
                        // 腾出空间
                        // 若插入位置在尾部之外（pos==len）则直接丢弃，不插入
                        if pos < self.len {
                            // 右移
                            self.levels.copy_within(pos..self.len - 1, pos + 1);
                            self.levels[pos] = Level { price, qty };
                        }
                    }
                    return;
                } else {
                    // 右移一格
                    if pos < self.len {
                        self.levels.copy_within(pos..self.len, pos + 1);
                    }
                    self.levels[pos] = Level { price, qty };
                    self.len += 1;
                }
            }
        }
    }
    /// 用收到的 full 快照整体替换（输入已是目标排序）
    #[inline]
    pub fn replace_full(&mut self, src: &[Level]) {
        let take = src.len().min(N);
        // 安全拷贝
        for i in 0..take {
            self.levels[i] = src[i];
        }
        self.len = take;
    }
}

/// 全深度
#[derive(Clone, Copy, Debug)]
pub struct OrderBook<const N: usize> {
    pub bids: SideBook<N, true>,
    pub asks: SideBook<N, false>,
    pub depth_id: u64, // 上次 u
}
impl<const N: usize> OrderBook<N> {
    pub fn new() -> Self {
        Self {
            bids: SideBook::new(),
            asks: SideBook::new(),
            depth_id: 0,
        }
    }
    /// full=true 的快照
    #[inline]
    pub fn apply_full(&mut self, bids: &[Level], asks: &[Level], u: u64) -> bool {
        // Gate 文档：full 推送需完全替换；深度 ID = u。第一次订阅后首条即 full。:contentReference[oaicite:1]{index=1}
        // 我们确保排序：bids 降序、asks 升序（若调用者已保证，可省）
        let mut btmp: [Level; N] = [Level::default(); N];
        let mut atmp: [Level; N] = [Level::default(); N];
        let blen = bids.len().min(N);
        let alen = asks.len().min(N);
        for i in 0..blen {
            btmp[i] = bids[i];
        }
        for i in 0..alen {
            atmp[i] = asks[i];
        }
        // insertion sort（N<=400，足够快且栈内）
        insertion_sort_desc(&mut btmp[..blen]);
        insertion_sort_asc(&mut atmp[..alen]);
        self.bids.replace_full(&btmp[..blen]);
        self.asks.replace_full(&atmp[..alen]);
        self.depth_id = u;
        if !self.check_overlap() {
            crate::info!("apply full failed");
            return false;
        }
        return true;
    }
    /// 增量：要求 U=depth_id+1，完成后 depth_id=u
    #[inline]
    pub fn apply_delta(
        &mut self,
        last_update_id: u64,
        u: u64,
        bids: &[Level],
        asks: &[Level],
    ) -> bool {
        if last_update_id != self.depth_id.saturating_add(1) {
            return false; // 非连续，需要重订阅/重建
        }
        for &l in bids {
            self.bids.upsert(l.price, l.qty);
        }
        for &l in asks {
            self.asks.upsert(l.price, l.qty);
        }
        self.depth_id = u;
        if !self.check_overlap() {
            crate::info!("apply delta failed");
            return false;
        }
        return true;
    }

    pub fn is_imbalance(&self, side: OrderSide) -> bool {
        match side {
            OrderSide::Buy => {
                (self.bids.levels[0].qty + self.bids.levels[1].qty)
                    > (self.asks.levels[0].qty + self.asks.levels[1].qty) * 2
            }
            OrderSide::Sell => {
                (self.asks.levels[0].qty + self.asks.levels[1].qty)
                    > (self.bids.levels[0].qty + self.bids.levels[1].qty) * 2
            }
        }
    }

    pub fn cumulative_qty_price_range(&self, side: OrderSide, amount: f64, index: usize) -> f64 {
        let levels = match side {
            OrderSide::Buy => self.bids.levels,
            OrderSide::Sell => self.asks.levels,
        };
        let mut total_qty = 0.0;
        let begin_price = levels[0].price;
        for level in levels {
            total_qty += level.qty.abs() as f64 * GATE_QUANTO_MULTIPLIER[index] * level.price;
            if total_qty >= amount {
                return (level.price - begin_price).abs() / begin_price;
            }
        }
        1.0
    }

    fn check_overlap(&self) -> bool {
        if self.bids.len > 0 && self.asks.len > 0 {
            if self.bids.levels[0].price >= self.asks.levels[0].price {
                self.print_orderbook();
                return false;
            }
        }
        true
    }

    pub fn print_orderbook(&self) {
        crate::info!("Depth ID: {}", self.depth_id);
        crate::info!(
            "Bid: {}@{}\t\tAsk: {}@{}",
            self.bids.levels[0].price,
            self.bids.levels[0].qty,
            self.asks.levels[0].price,
            self.asks.levels[0].qty,
        );
    }
}

/// 简单插入排序（栈内，无额外分配）
#[inline]
fn insertion_sort_desc(a: &mut [Level]) {
    for i in 1..a.len() {
        let key = a[i];
        let mut j = i;
        while j > 0 && a[j - 1].price < key.price {
            a[j] = a[j - 1];
            j -= 1;
        }
        a[j] = key;
    }
}
#[inline]
fn insertion_sort_asc(a: &mut [Level]) {
    for i in 1..a.len() {
        let key = a[i];
        let mut j = i;
        while j > 0 && a[j - 1].price > key.price {
            a[j] = a[j - 1];
            j -= 1;
        }
        a[j] = key;
    }
}

/// ------------------------ 解析（零拷贝 DOM + 栈向量） ------------------------
#[inline]
fn parse_price_qty_pair<'a>(pair: &'a BorrowedValue) -> Option<(f64, i64)> {
    // pair = ["price", "qty"]
    let arr = pair.as_array()?;
    if arr.len() != 2 {
        return None;
    }
    let p_bytes = arr[0].as_str()?.as_bytes();
    let q_bytes = arr[1].as_str()?.as_bytes();
    // 使用 lexical-core 极快解析
    let price = f64::from_lexical(p_bytes).ok()?;
    // Gate 永续 qty 多为整数；若出现小数，会返回 Err，这里尝试为 f64 再四舍五入
    let qty = i64::from_lexical(q_bytes)
        .or_else(|_| f64::from_lexical(q_bytes).map(|v| v.round() as i64))
        .ok()?;
    Some((price, qty))
}

#[derive(Debug)]
pub struct Parsed<const N: usize> {
    pub contract: String,
    pub is_full: bool,
    pub u: u64,
    pub last_update_id: u64,
    pub bids: HVec<Level, N>,
    pub asks: HVec<Level, N>,
}
#[inline]
pub fn parse_obu<'a, const N: usize>(buf: &'a mut [u8]) -> Option<Parsed<N>> {
    // 文档：futures.obu；full: true 时没有 U；增量包含 U/u/b/a；数量对为 [price, amount]。:contentReference[oaicite:2]{index=2}
    let v: BorrowedValue<'_> = simd_json::to_borrowed_value(buf).ok()?;
    let root = v.as_object()?;
    // 可选快速判断频道
    if root.get("channel")?.as_str()? != "futures.obu" {
        crate::error!("not futures.obu: {:?}", root);
        return None;
    }
    let res = root.get("result")?.as_object()?;

    let symbol = res
        .get("s")
        .and_then(|x| x.as_str())
        .or_else(|| res.get("contract").and_then(|x| x.as_str()))
        .or_else(|| res.get("symbol").and_then(|x| x.as_str()))?;
    let symbol = if let (Some(i1), Some(i2)) = (symbol.find('.'), symbol.rfind('.')) {
        if i1 < i2 && i2 > i1 + 1 {
            &symbol[i1 + 1..i2]
        } else {
            symbol
        }
    } else {
        symbol
    };

    let is_full = res.get("full").and_then(|b| b.as_bool()).unwrap_or(false);
    let mut bids: HVec<Level, N> = HVec::new();
    let mut asks: HVec<Level, N> = HVec::new();

    // 读取数组 b/a
    if let Some(b) = res.get("b").and_then(|x| x.as_array()) {
        for it in b.iter() {
            if let Some((p, q)) = parse_price_qty_pair(it) {
                let _ = bids.push(Level { price: p, qty: q });
            }
        }
    }
    if let Some(a) = res.get("a").and_then(|x| x.as_array()) {
        for it in a.iter() {
            if let Some((p, q)) = parse_price_qty_pair(it) {
                let _ = asks.push(Level { price: p, qty: q });
            }
        }
    }

    if is_full {
        let u = res.get("u")?.as_u64()?;
        Some(Parsed {
            contract: symbol.to_string(),
            is_full,
            u,
            last_update_id: 0,
            bids,
            asks,
        })
    } else {
        let last_update_id = res.get("U")?.as_u64()?;
        let u = res.get("u")?.as_u64()?;
        Some(Parsed {
            contract: symbol.to_string(),
            is_full,
            u,
            last_update_id,
            bids,
            asks,
        })
    }
}

pub fn generate_orderbook_sub_request() -> String {
    /*ws.send('{"time" : 123456, "channel" : "futures.obu",
    "event": "subscribe", "payload" : ["ob.BTC_USDT.400"]}') */
    let ts_sec = system_now_in_secs();
    let symbols = crate::engine::cross_exchange::cross_exchange_const::GATE_BASE_ASSETS
        .iter()
        .map(|s| format!("ob.{}_USDT.50", s))
        .collect::<Vec<_>>();
    format!(
        r#"
{{
    "time": {},
    "channel": "futures.obu",
    "event": "subscribe",
    "payload": {}
}}"#,
        ts_sec,
        serde_json::to_string(&symbols).unwrap()
    )
}
