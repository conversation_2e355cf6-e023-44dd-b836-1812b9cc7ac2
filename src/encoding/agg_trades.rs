// {
//   "e": "aggTrade",    // Event type
//   "E": 1672515782136, // Event time
//   "s": "BNBBTC",      // Symbol
//   "a": 12345,         // Aggregate trade ID
//   "p": "0.001",       // Price
//   "q": "100",         // Quantity
//   "f": 100,           // First trade ID
//   "l": 105,           // Last trade ID
//   "T": 1672515782136, // Trade time
//   "m": true,          // Is the buyer the market maker?
//   "M": true           // Ignore
// }
use memchr;

use crate::encoding::book_ticker::FuturesBookTicker;

// {
//   "e": "aggTrade",  // Event type
//   "E": 123456789,   // Event time
//   "s": "BTCUSDT",    // Symbol
//   "a": 5933014,		// Aggregate trade ID
//   "p": "0.001",     // Price
//   "q": "100",       // Quantity
//   "f": 100,         // First trade ID
//   "l": 105,         // Last trade ID
//   "T": 123456785,   // Trade time
//   "m": true,        // Is the buyer the market maker?
// }
#[derive(Debug, Clone)]
pub struct FuturesAggTrade {
    pub event_time: u64,
    pub trade_time: u64,
    pub price: f64,
    pub quantity: f64,
    pub is_buyer_maker: bool,
    pub first_trade_id: u64,
    pub last_trade_id: u64,
    pub symbol: String,
}

pub fn parse_futures_agg_trade(input: &[u8]) -> Option<FuturesAggTrade> {
    // First, try to find if this is a stream wrapper message
    let data_pattern = b"\"data\":{";
    if let Some(data_start) = memchr::memmem::find(input, data_pattern) {
        // This is a stream wrapper, extract the data portion
        let data_start = data_start + data_pattern.len() - 1; // -1 to include the {
        let data_end = find_matching_brace(&input[data_start..])?;
        let data_section = &input[data_start..data_start + data_end + 1];
        return parse_futures_agg_trade_fast(data_section);
    }

    // If no stream wrapper, try to parse directly
    parse_futures_agg_trade_fast(input)
}

pub fn parse_futures_agg_trade_as_bbo(input: &[u8]) -> Option<FuturesBookTicker> {
    // First, try to find if this is a stream wrapper message
    let data_pattern = b"\"data\":{";
    let agg_trade = if let Some(data_start) = memchr::memmem::find(input, data_pattern) {
        // This is a stream wrapper, extract the data portion
        let data_start = data_start + data_pattern.len() - 1; // -1 to include the {
        let data_end = find_matching_brace(&input[data_start..])?;
        let data_section = &input[data_start..data_start + data_end + 1];
        parse_futures_agg_trade_fast(data_section)
    } else {
        parse_futures_agg_trade_fast(input)
    };
    match agg_trade {
        Some(agg_trade) => Some(FuturesBookTicker {
            event_time: agg_trade.event_time,
            transaction_time: agg_trade.trade_time,
            update_id: 0,
            symbol: agg_trade.symbol,
            bid_price: agg_trade.price,
            bid_qty: agg_trade.quantity,
            ask_price: agg_trade.price,
            ask_qty: agg_trade.quantity,
        }),
        None => return None,
    }
}

/// High-performance parsing using optimized string traversal
/// This function is designed for the specific Binance aggregated trade format
/// and provides the best performance for high-frequency WebSocket messages
#[inline(always)]
fn parse_futures_agg_trade_fast(input: &[u8]) -> Option<FuturesAggTrade> {
    // Expected format: {"e":"aggTrade","E":123456789,"s":"BTCUSDT","a":12345,"p":"123.45","q":"0.001","f":100,"l":105,"T":123456785,"m":false}

    // Quick validation - must start with { and contain aggTrade
    if input.len() < 50 || input[0] != b'{' {
        return None;
    }

    // Parse fields in any order for robustness
    let mut event_time = None;
    let mut price = None;
    let mut quantity = None;
    let mut trade_time = None;
    let mut is_buyer_maker = None;
    let mut first_trade_id = None;
    let mut last_trade_id = None;

    // Find event time (E)
    if let Some(value) = find_and_parse_u64_anywhere(input, b"\"E\":") {
        event_time = Some(value);
    }

    // Find price (p)
    if let Some(value) = find_and_parse_f64_anywhere(input, b"\"p\":\"") {
        price = Some(value);
    }

    // Find quantity (q)
    if let Some(value) = find_and_parse_f64_anywhere(input, b"\"q\":\"") {
        quantity = Some(value);
    }

    // Find trade time (T)
    if let Some(value) = find_and_parse_u64_anywhere(input, b"\"T\":") {
        trade_time = Some(value);
    }

    // Find is buyer maker (m)
    if let Some(value) = find_and_parse_bool_anywhere(input, b"\"m\":") {
        is_buyer_maker = Some(value);
    }

    // Find first trade ID (f)
    if let Some(value) = find_and_parse_u64_anywhere(input, b"\"f\":") {
        first_trade_id = Some(value);
    }

    // Find last trade ID (l)
    if let Some(value) = find_and_parse_u64_anywhere(input, b"\"l\":") {
        last_trade_id = Some(value);
    }

    let symbol = find_and_parse_string_anywhere(input, b"\"s\":\"")?;
    let symbol = symbol.to_string();

    // All fields must be present
    if let (Some(e), Some(p), Some(q), Some(t), Some(m), Some(f), Some(l)) = (
        event_time,
        price,
        quantity,
        trade_time,
        is_buyer_maker,
        first_trade_id,
        last_trade_id,
    ) {
        Some(FuturesAggTrade {
            event_time: e,
            trade_time: t,
            price: p,
            quantity: q,
            is_buyer_maker: m,
            first_trade_id: f,
            last_trade_id: l,
            symbol,
        })
    } else {
        None
    }
}

// Helper function to find matching brace
fn find_matching_brace(data: &[u8]) -> Option<usize> {
    let mut bracket_count = 1; // Start at 1 since we're already inside the first {
    let mut in_string = false;
    let mut escape_next = false;

    for (i, &byte) in data.iter().enumerate() {
        if escape_next {
            escape_next = false;
            continue;
        }

        match byte {
            b'\\' if in_string => escape_next = true,
            b'"' => in_string = !in_string,
            b'{' if !in_string => bracket_count += 1,
            b'}' if !in_string => {
                bracket_count -= 1;
                if bracket_count == 0 {
                    return Some(i);
                }
            }
            _ => {}
        }
    }
    None
}

// Helper function to find and parse u64 values anywhere in the input
fn find_and_parse_u64_anywhere(input: &[u8], pattern: &[u8]) -> Option<u64> {
    if let Some(start) = memchr::memmem::find(input, pattern) {
        let start = start + pattern.len();
        let end = memchr::memchr(b',', &input[start..])
            .or_else(|| memchr::memchr(b'}', &input[start..]))?;
        let value_str = &input[start..start + end];
        unsafe { std::str::from_utf8_unchecked(value_str) }
            .parse::<u64>()
            .ok()
    } else {
        None
    }
}

// Helper function to find and parse f64 values anywhere in the input
fn find_and_parse_f64_anywhere(input: &[u8], pattern: &[u8]) -> Option<f64> {
    if let Some(start) = memchr::memmem::find(input, pattern) {
        let start = start + pattern.len();
        // For quoted strings, we need to find the closing quote
        let end = memchr::memchr(b'"', &input[start..])?;
        let value_str = &input[start..start + end];
        unsafe { std::str::from_utf8_unchecked(value_str) }
            .parse::<f64>()
            .ok()
    } else {
        None
    }
}

// Helper function to find and parse string values anywhere in the input
pub fn find_and_parse_string_anywhere<'a>(input: &'a [u8], pattern: &[u8]) -> Option<&'a str> {
    if let Some(start) = memchr::memmem::find(input, pattern) {
        let start = start + pattern.len();
        let end = memchr::memchr(b'"', &input[start..])?;
        let value_str = &input[start..start + end];
        unsafe { std::str::from_utf8_unchecked(value_str) }.into()
    } else {
        None
    }
}

// Helper function to find and parse bool values anywhere in the input
fn find_and_parse_bool_anywhere(input: &[u8], pattern: &[u8]) -> Option<bool> {
    if let Some(start) = memchr::memmem::find(input, pattern) {
        let start = start + pattern.len();
        let end = memchr::memchr(b',', &input[start..])
            .or_else(|| memchr::memchr(b'}', &input[start..]))?;
        let value_str = &input[start..start + end];
        unsafe { std::str::from_utf8_unchecked(value_str) }
            .parse::<bool>()
            .ok()
    } else {
        None
    }
}

#[derive(Debug, Clone)]
pub struct AggTrade<'a> {
    pub symbol: &'a str,
    pub event_time: u64,
    pub price: f64,
    pub is_buyer_maker: bool,
}

pub fn parse_agg_trade(input: &[u8]) -> Option<AggTrade<'_>> {
    let event_time_pattern = b"\"E\":";
    let start = memchr::memmem::find(input, event_time_pattern)?;
    let start = start + event_time_pattern.len();
    let end = memchr::memchr(b',', &input[start..])?;
    let event_time_str = &input[start..start + end];
    let event_time = unsafe { std::str::from_utf8_unchecked(event_time_str) }
        .parse()
        .unwrap();

    let symbol_pattern = b"\"s\":\"";
    let start = memchr::memmem::find(input, symbol_pattern)?;
    let start = start + symbol_pattern.len();
    let end = memchr::memchr(b'"', &input[start..])?;
    let symbol = &input[start..start + end];
    let symbol = unsafe { std::str::from_utf8_unchecked(symbol) };

    let price_pattern = b"\"p\":\"";
    let start = memchr::memmem::find(input, price_pattern)?;
    let start = start + price_pattern.len();
    let end = memchr::memchr(b'"', &input[start..])?;
    let price_str = &input[start..start + end];
    let price = unsafe { std::str::from_utf8_unchecked(price_str) }
        .parse()
        .unwrap();

    let is_buyer_maker_pattern = b"\"m\":";
    let start = memchr::memmem::find(input, is_buyer_maker_pattern)?;
    let start = start + is_buyer_maker_pattern.len();
    let end = memchr::memchr(b',', &input[start..])?;
    let is_buyer_maker_str = &input[start..start + end];
    let is_buyer_maker = unsafe { std::str::from_utf8_unchecked(is_buyer_maker_str) }
        .parse()
        .unwrap();

    Some(AggTrade {
        symbol,
        event_time,
        price,
        is_buyer_maker,
    })
}
