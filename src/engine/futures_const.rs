pub const FUTURES_IN_LEN: usize = 1024 * 64;
pub const FUTURES_OUT_LEN: usize = 1024 * 4;
pub const FUTURES_USDT_SYMBOL: &str = "TRUMPUSDT";
pub const FUTURES_USDC_SYMBOL: &str = "TRUMPUSDC";
#[cfg(feature = "hlc")]
pub const FUTURES_API_KEY: &str =
    "I6ZeHYptYtPBYGj1uQYx944Eb2Caoi20VnGIuKRKjih852Hg1bUjjy36UEyjaGLH";
#[cfg(feature = "hlc")]
pub const FUTURES_API_SECRET: &str = "-----B<PERSON>IN PRIVATE KEY-----\nMC4CAQAwBQYDK2VwBCIEIB01rxfEYLKZlDFw/ieLChOmI7/fxN4eeH8bZPqJvR74\n-----END PRIVATE KEY-----";
#[cfg(feature = "yfq")]
pub const FUTURES_API_KEY: &str =
    "FAN3BxdBVBXoncn8y0KqB5UZyOjO6olGaDN5AKoQlBbh2J1LitXuMNRjd0hOdKAQ";
#[cfg(feature = "yfq")]
pub const FUTURES_API_SECRET: &str = "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VwBCIEICDTwgbtKlorTCpgINA88/H80NDHiHv3KiRj6gH8CjlM\n-----END PRIVATE KEY-----";
#[cfg(feature = "hlc")]
pub const FUTURES_HMAC_API_KEY: &str =
    "3HXZr1PVaBeCp0KReDTDCKDPsdA56yMZKrFGhsUMEnOjsEJsAOUzyjhsDZSCx5J2";
#[cfg(feature = "hlc")]
pub const FUTURES_HMAC_API_SECRET: &str =
    "YobjCzXHi5OBXidaafg537zdDCotyhLVPbAsWfUhBrRjSehuPr0xWR40OpkhEt4r";
// yfq
#[cfg(feature = "yfq")]
pub const FUTURES_HMAC_API_KEY: &str =
    "MTQ71msvZEhboqgp37Im5R1XNIPUXl5RIKnHswLTdf1o2jNNIyJmNF7FtCmtmgO6";
#[cfg(feature = "yfq")]
pub const FUTURES_HMAC_API_SECRET: &str =
    "eLwP04WC7su5QBhk9BlGcfjpCApNtMAQJ8Ncmi2N3w4OK7NjCBnEFBcBvraBjpqt";
pub const FUTURES_ORDER_PLACE_OFFSET: f64 = 3.0;
pub const FUTURES_TAKE_PROFIT_OFFSET: f64 = 1.0;
pub const FUTURES_MAKER_MIN_LEVEL: usize = 5;
pub const FUTURES_QUANTITY_TICK_SIZE: f64 = 0.005;
pub const FUTURES_ORDER_QUANTITY: f64 = 1.0;
pub const FUTURES_PRICE_TICK_SIZE: f64 = 0.01;
