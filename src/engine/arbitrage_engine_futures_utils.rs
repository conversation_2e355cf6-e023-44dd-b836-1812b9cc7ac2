use crate::{
    encoding::bn_futures_order::OrderSide,
    engine::{
        futures_const::FUTURES_PRICE_TICK_SIZE,
        opportunity_simulator::{Opportunity, OpportunityStatus, OpportunityType, PredictResult},
    },
    utils::perf::system_now_in_us,
};

const PRICE_OFFSET_TICKS: f64 = 20.0;
const GAP_TICKS: f64 = 1.0;

pub fn generate_take_profit_and_stop_loss_opportunity(
    fill_price: f64,
    origin_side: OrderSide,
    origin_opportunity_id: u64,
) -> [Opportunity; 2] {
    let take_profit_id = system_now_in_us();
    let stop_loss_id = take_profit_id + 1;
    let (take_profit_price, take_profit_stop_price) = match origin_side {
        OrderSide::Buy => {
            let price = fill_price + PRICE_OFFSET_TICKS * FUTURES_PRICE_TICK_SIZE;
            let stop = price - GAP_TICKS * FUTURES_PRICE_TICK_SIZE;
            (price, stop)
        }
        OrderSide::Sell => {
            let price = fill_price - PRICE_OFFSET_TICKS * FUTURES_PRICE_TICK_SIZE;
            let stop = price + GAP_TICKS * FUTURES_PRICE_TICK_SIZE;
            (price, stop)
        }
    };
    let (stop_loss_price, stop_loss_stop_price) = match origin_side {
        OrderSide::Buy => {
            let stop = fill_price - PRICE_OFFSET_TICKS * FUTURES_PRICE_TICK_SIZE;
            let price = stop + GAP_TICKS * FUTURES_PRICE_TICK_SIZE; // price > stop
            (price, stop)
        }
        OrderSide::Sell => {
            let stop = fill_price + PRICE_OFFSET_TICKS * FUTURES_PRICE_TICK_SIZE;
            let price = stop - GAP_TICKS * FUTURES_PRICE_TICK_SIZE; // price < stop
            (price, stop)
        }
    };
    [
        Opportunity {
            id: take_profit_id,
            origin_order_id: origin_opportunity_id,
            price: take_profit_price,
            stop_price: take_profit_stop_price,
            side: origin_side.reverse(),
            status: OpportunityStatus::Pending,
            predict_result: PredictResult::None,
            opportunity_type: OpportunityType::TakeProfit,
        },
        Opportunity {
            id: stop_loss_id,
            origin_order_id: origin_opportunity_id,
            price: stop_loss_price,
            stop_price: stop_loss_stop_price,
            side: origin_side.reverse(),
            status: OpportunityStatus::Pending,
            predict_result: PredictResult::None,
            opportunity_type: OpportunityType::StopLoss,
        },
    ]
}
