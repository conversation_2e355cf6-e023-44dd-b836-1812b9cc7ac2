use crate::{
    encoding::{bn_futures_order::OrderSide, cross_exchange::model::HedgeStatus},
    engine::cross_exchange::cross_exchange_const::{CURRENCY_LEN, UnifiedCurrency},
};

pub struct HedgeManager {
    hedge_status: [HedgeStatus; CURRENCY_LEN],
}

impl HedgeManager {
    pub fn new() -> Self {
        Self {
            hedge_status: [HedgeStatus::default(); CURRENCY_LEN],
        }
    }

    pub fn get(&self, index: usize) -> &HedgeStatus {
        &self.hedge_status[index]
    }

    pub fn init_maker(&mut self, index: usize, price: f64, qty: f64, side: OrderSide) {
        let signed_qty = qty * side.sign();
        unsafe {
            let new_qty =
                self.hedge_status[index].maker_qty + signed_qty
            let new_price = if new_qty.abs() <= 0.00000001 {
                0.0
            } else {
                (price * signed_qty
                    +     self.hedge_status[index].maker_avg_price * self.hedge_status[index].maker_qty
                    )
                    / new_qty
            };
                self.hedge_status[index].maker_avg_price = new_price;
                self.hedge_status[index].maker_qty = new_qty;
            crate::info_unsafe!(
                r#"
Hedge status:
{:?}
bn price:   {:.6}, bn qty:   {:.5} bn pending:  {:.5}
gate price: {:.6}, gate qty: {:.5}
"#,
                UnifiedCurrency::from_usize(index).unwrap(),
                self.hedge_status[index].maker_avg_price,
                self.hedge_status[index].maker_qty,
                self.hedge_status[index].maker_pending,
                self.hedge_status[index].taker_avg_price,
                self.hedge_status[index].take_qty,
            );
        }
    }

    pub fn reduce_pending(&mut self, index: usize, qty: f64) {
        unsafe {
            let curreny = UnifiedCurrency::from_usize(index).unwrap();
            crate::info_unsafe!(
                "reduce pending: {:?} {} {}",
                curreny,
                qty,
                self.hedge_status[index].bn_pending
            );
            self.hedge_status[index].bn_pending -= qty;
        }
    }

    pub fn reduce_gate_pending(&mut self, index: usize, qty: f64) {
        unsafe {
            let curreny = UnifiedCurrency::from_usize(index).unwrap();
            crate::info_unsafe!(
                "reduce pending: {:?} {} {}",
                curreny,
                qty,
                self.hedge_status[index].gate_pending
            );
            self.hedge_status[index].gate_pending -= qty;
        }
    }

    pub fn update_when_bn_maker(
        &mut self,
        index: usize,
        price: f64,
        qty: f64,
        side: OrderSide,
        is_bn: bool,
    ) {
        let signed_qty = qty * side.sign();
        unsafe {
            let status = &mut self.hedge_status[index];
            let new_qty = if is_bn {
                status.bn_qty + signed_qty
            } else {
                status.gate_qty + signed_qty
            };
            let new_price = if new_qty.abs() <= 0.00000001 {
                0.0
            } else {
                (price * signed_qty
                    + if is_bn {
                        status.bn_avg_price * status.bn_qty
                    } else {
                        status.gate_avg_price * status.gate_qty
                    })
                    / new_qty
            };
            if is_bn {
                status.bn_avg_price = new_price;
                status.bn_qty = new_qty;
            } else {
                status.gate_avg_price = new_price;
                status.gate_qty = new_qty;
                crate::info_unsafe!(
                    "update pending: before: {}, delta: {}",
                    status.gate_pending,
                    signed_qty
                );
                if status.gate_pending.abs() < signed_qty.abs() {
                    crate::error_unsafe!(
                        "gate order filled ({}) is greater than pending ({})",
                        signed_qty,
                        status.gate_pending
                    );
                    status.gate_pending = 0.0;
                } else {
                    status.gate_pending -= signed_qty;
                }
                crate::info_unsafe!(
                    r#"
{:?}
BP: {:.6}, BQ: {:.5} BPD: {:.5}
GP: {:.6}, GQ: {:.5} GPD: {:.5}
"#,
                    UnifiedCurrency::from_usize(index).unwrap(),
                    self.hedge_status[index].bn_avg_price,
                    self.hedge_status[index].bn_qty,
                    self.hedge_status[index].bn_pending,
                    self.hedge_status[index].gate_avg_price,
                    self.hedge_status[index].gate_qty,
                    self.hedge_status[index].gate_pending,
                );
            }
        }
    }

    pub fn update(&mut self, index: usize, price: f64, qty: f64, side: OrderSide, is_bn: bool) {
        let signed_qty = qty * side.sign();
        unsafe {
            let new_qty = if is_bn {
                self.hedge_status[index].bn_qty + signed_qty
            } else {
                self.hedge_status[index].gate_qty + signed_qty
            };
            let new_price = if new_qty.abs() <= 0.00000001 {
                0.0
            } else {
                (price * signed_qty
                    + if is_bn {
                        self.hedge_status[index].bn_avg_price * self.hedge_status[index].bn_qty
                    } else {
                        self.hedge_status[index].gate_avg_price * self.hedge_status[index].gate_qty
                    })
                    / new_qty
            };
            if is_bn {
                self.hedge_status[index].bn_avg_price = new_price;
                self.hedge_status[index].bn_qty = new_qty;
                crate::info_unsafe!(
                    "update pending: before: {}, delta: {}",
                    self.hedge_status[index].bn_pending,
                    signed_qty
                );
                self.hedge_status[index].bn_pending -= signed_qty;
            } else {
                self.hedge_status[index].gate_avg_price = new_price;
                self.hedge_status[index].gate_qty = new_qty;
            }
            crate::info_unsafe!(
                r#"
{:?}
BP: {:.6}, BQ: {:.5} BPD: {:.5}
GP: {:.6}, GQ: {:.5}
"#,
                UnifiedCurrency::from_usize(index).unwrap(),
                self.hedge_status[index].bn_avg_price,
                self.hedge_status[index].bn_qty,
                self.hedge_status[index].bn_pending,
                self.hedge_status[index].gate_avg_price,
                self.hedge_status[index].gate_qty,
            );
        }
    }
}
