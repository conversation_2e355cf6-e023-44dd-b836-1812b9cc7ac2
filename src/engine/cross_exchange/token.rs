use mio::Token;

pub const GATE_BBO_TOKEN_1: Token = Token(1);
pub const GATE_BBO_TOKEN_2: Token = Token(2);
pub const BN_BBO_TOKEN_1: Token = Token(3);
pub const BN_BBO_TOKEN_2: Token = Token(4);
pub const BN_TRADE_TOKEN_1: Token = Token(5);
pub const GATE_FUNDING_RATE_TOKEN: Token = Token(6);
pub const BN_FUNDING_RATE_TOKEN: Token = Token(7);
pub const GATE_TRADE_TOKEN_1: Token = Token(8);
pub const GATE_ORDERBOOK_TOKEN_1: Token = Token(9);

pub const GATE_ORDER_TOKEN_1: Token = Token(31);
pub const GATE_ORDER_TOKEN_2: Token = Token(32);
pub const GATE_ORDER_TOKEN_3: Token = Token(33);
pub const GATE_ORDER_TOKEN_4: Token = Token(34);
pub const GATE_ORDER_TOKEN_5: Token = Token(35);
pub const GATE_ORDER_TOKEN_6: Token = Token(36);
pub const GATE_ORDER_TOKEN_7: Token = Token(37);
pub const GATE_ORDER_TOKEN_8: Token = Token(38);
pub const GATE_ORDER_TOKEN_9: Token = Token(39);
pub const GATE_ORDER_TOKEN_10: Token = Token(40);
pub const GATE_ORDER_SUB_TOKEN: Token = Token(13);
pub const GATE_POSITION_SUB_TOKEN: Token = Token(14);
pub const GATE_POSITION_SNAPSHOT_TOKEN: Token = Token(15);
pub const GATE_USER_TRADE_SUB_TOKEN: Token = Token(16);

pub const BN_ORDER_TOKEN: Token = Token(21);
pub const BN_USER_DATA_TOKEN: Token = Token(22);
pub const BN_POSITION_SNAPSHOT_TOKEN: Token = Token(23);
pub const BN_ORDERBOOK_SNAPSHOT_TOKEN_1: Token = Token(24);
pub const BN_ORDERBOOK_SNAPSHOT_TOKEN_2: Token = Token(25);

pub const OKX_BBO_TOKEN: Token = Token(91);

pub const STOP_WAKER_TOKEN: Token = Token(100);
