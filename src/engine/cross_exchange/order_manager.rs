use crate::{
    encoding::{
        bn_futures_order,
        cross_exchange::model::{MakerOrder, UnifiedOrderStatus},
        gate,
    },
    engine::cross_exchange::cross_exchange_const::{
        CANCEL_ALL_ORDER_STATUS, CURRENCY_LEN, UnifiedCurrency,
    },
    utils::perf::{circles_to_ns, now},
};

pub struct OrderManager {}

impl OrderManager {
    pub fn new() -> Self {
        Self {}
    }

    pub fn cancel_all_orders(&mut self) -> Vec<String> {
        let mut result = vec![];
        unsafe {
            for i in 0..CURRENCY_LEN {
                let currency = UnifiedCurrency::from_usize(i).unwrap();
                if CANCEL_ALL_ORDER_STATUS[i].1 {
                    continue;
                }
                CANCEL_ALL_ORDER_STATUS[i].0 = now();
                CANCEL_ALL_ORDER_STATUS[i].1 = true;
                let order = gate::cancel_all_orders(
                    CANCEL_ALL_ORDER_STATUS[i].0,
                    currency.to_gate_symbol(),
                );
                result.push(order);
            }
        }
        result
    }

    pub fn cancel_all_bn_order(
        &mut self,
        orders: &mut [MakerOrder; CURRENCY_LEN],
        close_orders: &mut [MakerOrder; CURRENCY_LEN],
    ) -> Vec<String> {
        let mut result = vec![];
        unsafe {
            for i in 0..CURRENCY_LEN {
                let order = &mut orders[i];
                let symbol = UnifiedCurrency::from_usize(i).unwrap().to_bn_symbol();
                if order.order_id != 0 {
                    order.status = UnifiedOrderStatus::Cancelling;
                    let order = bn_futures_order::cancel_order_with_symbol(order.order_id, &symbol);
                    crate::info_unsafe!("cancelling order: {}", order);
                    result.push(order);
                }
                let order = &mut close_orders[i];
                if order.order_id != 0 {
                    order.status = UnifiedOrderStatus::Cancelling;
                    let order = bn_futures_order::cancel_order_with_symbol(order.order_id, &symbol);
                    crate::info_unsafe!("cancelling order: {}", order);
                    result.push(order);
                }
            }
        }
        result
    }

    pub fn update_cancel_order(&mut self, symbol: &str) {
        unsafe {
            let currency = UnifiedCurrency::from_symbol(symbol).unwrap();
            CANCEL_ALL_ORDER_STATUS[currency as usize].1 = true;
        }
    }

    pub fn all_order_canceled(&self) -> bool {
        unsafe {
            for i in 0..CURRENCY_LEN {
                if !CANCEL_ALL_ORDER_STATUS[i].1 {
                    return false;
                }
            }
        }
        true
    }

    pub fn should_cancel_order(status: &UnifiedOrderStatus) -> bool {
        match status {
            UnifiedOrderStatus::Uninitialized
            | UnifiedOrderStatus::Amending
            | UnifiedOrderStatus::Opening
            | UnifiedOrderStatus::Cancelling => false,
            _ => true,
        }
    }

    pub fn should_check_timeout(status: &UnifiedOrderStatus) -> bool {
        match status {
            UnifiedOrderStatus::Opening
            | UnifiedOrderStatus::Amending
            | UnifiedOrderStatus::Cancelling => true,
            _ => false,
        }
    }

    pub fn check_and_set_timeout(order: &mut MakerOrder) {
        let now_ms = now();
        let elapse = circles_to_ns(now_ms - order.last_modify_time);
        if elapse < 10_000_000.0 {
            return;
        } else if elapse > 10_000_000_000.0 {
            crate::error!(
                "order not ack after {:.3} ms: req id: {} order id: {}",
                elapse / 1_000_000.0,
                order.req_id,
                order.order_id,
            );
            order.status = UnifiedOrderStatus::Uninitialized;
            order.req_id = 0;
        }
    }

    pub fn can_mark_open(order: &MakerOrder, req_id: u64) -> bool {
        match order.status {
            UnifiedOrderStatus::Opening if order.req_id == req_id => true,
            _ => false,
        }
    }

    pub fn can_mark_cancel(order: &MakerOrder, req_id: u64) -> bool {
        match order.status {
            UnifiedOrderStatus::Opening if order.req_id == req_id => true,
            UnifiedOrderStatus::Cancelling if order.req_id == req_id => true,
            UnifiedOrderStatus::Open if order.req_id == req_id => true,
            _ => false,
        }
    }

    pub fn can_mark_bn_cancel(order: &MakerOrder, order_id: u64) -> bool {
        match order.status {
            UnifiedOrderStatus::Opening if order.order_id == order_id => true,
            UnifiedOrderStatus::Cancelling if order.order_id == order_id => true,
            _ => false,
        }
    }

    pub fn mark_maker_order_opening(order: &mut MakerOrder, price: f64, req_id: u64, qty: f64) {
        order.price = price;
        order.status = UnifiedOrderStatus::Opening;
        order.req_id = req_id;
        order.canceling_by_timeout = false;
        order.last_modify_time = req_id;
        order.last_order_id = order.order_id;
        order.order_id = 0;
        order.filled_qty = 0.0;
        order.filled_qty_by_trade = 0.0;
        order.quantity = qty;
    }

    pub fn reset_order(order: &mut MakerOrder) {
        order.status = UnifiedOrderStatus::Uninitialized;
        order.req_id = 0;
    }
}

#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum OrderCancelReason {
    MakerFailed,
    NotFound,
    TooManyRequests,
    Other,
}
