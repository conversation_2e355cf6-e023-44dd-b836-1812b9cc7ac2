use crate::{
    encoding::bn_futures_order::OrderSide,
    engine::cross_exchange::cross_exchange_const::{
        BN_FUNDING_FEE, GATE_FUNDING_FEE, GATE_PRICE_LONG_OFFSET, GATE_PRICE_SHORT_OFFSET,
    },
};

pub fn decision_open_side(index: usize) -> Option<OrderSide> {
    unsafe {
        let bn_funding_fee = BN_FUNDING_FEE[index];
        let gate_funding_fee = GATE_FUNDING_FEE[index];
        if bn_funding_fee > 0.0 && gate_funding_fee > 0.0 {
            if gate_funding_fee > bn_funding_fee {
                // gate should short
                return Some(OrderSide::Buy);
            } else {
                return Some(OrderSide::Sell);
            }
        }
        if bn_funding_fee < 0.0 && gate_funding_fee < 0.0 {
            if gate_funding_fee < bn_funding_fee {
                // gate should long
                return Some(OrderSide::Sell);
            } else {
                return Some(OrderSide::Buy);
            }
        }
        if bn_funding_fee > 0.0 && gate_funding_fee < 0.0 {
            return Some(OrderSide::Sell);
        }
        if bn_funding_fee < 0.0 && gate_funding_fee > 0.0 {
            return Some(OrderSide::Buy);
        }
    }
    None
}

pub fn decision_gate_open_side(index: usize) -> Option<OrderSide> {
    unsafe {
        let bn_funding_fee = BN_FUNDING_FEE[index];
        let gate_funding_fee = GATE_FUNDING_FEE[index];
        if bn_funding_fee > 0.0 && gate_funding_fee > 0.0 {
            if gate_funding_fee > bn_funding_fee {
                return Some(OrderSide::Sell);
            } else {
                return Some(OrderSide::Buy);
            }
        }
        if bn_funding_fee < 0.0 && gate_funding_fee < 0.0 {
            if gate_funding_fee < bn_funding_fee {
                // gate should long
                return Some(OrderSide::Buy);
            } else {
                return Some(OrderSide::Sell);
            }
        }
        if bn_funding_fee > 0.0 && gate_funding_fee < 0.0 {
            return Some(OrderSide::Buy);
        }
        if bn_funding_fee < 0.0 && gate_funding_fee > 0.0 {
            return Some(OrderSide::Sell);
        }
    }
    None
}

pub fn cal_prices(
    index: usize,
    side: OrderSide,
    bid_price: f64,
    ask_price: f64,
    is_close: bool,
    near: f64,
    far: f64,
) -> Option<(f64, f64, f64)> {
    if side == OrderSide::Buy {
        let offset = if is_close {
            GATE_PRICE_SHORT_OFFSET[index] * 0.5
        } else {
            GATE_PRICE_SHORT_OFFSET[index]
        };
        let base_price = bid_price;
        let order_price = base_price * (1.0 + (offset * near));
        let lower_bound = base_price * (1.0 + offset * far);
        let upper_bound = base_price * (1.0 + offset);
        Some((order_price, lower_bound, upper_bound))
    } else {
        let offset = if is_close {
            GATE_PRICE_LONG_OFFSET[index] * 0.5
        } else {
            GATE_PRICE_LONG_OFFSET[index]
        };
        let base_price = ask_price;
        let order_price = base_price * (1.0 + (offset * near));
        let lower_bound = base_price * (1.0 + offset);
        let upper_bound = base_price * (1.0 + offset * far);
        Some((order_price, lower_bound, upper_bound))
    }
}
