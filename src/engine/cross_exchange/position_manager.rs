use crate::{
    encoding::cross_exchange::model::MakerOrder,
    engine::cross_exchange::cross_exchange_const::GATE_QUANTO_MULTIPLIER,
};

pub fn calculate_and_update_filled(
    left: Option<i64>,
    order: &mut MakerOrder,
    size: f64,
    index: usize,
    by_trade: bool,
) -> f64 {
    let left_qty = left.unwrap_or(0).abs() as f64 * GATE_QUANTO_MULTIPLIER[index];
    let size_qty = size.abs() * GATE_QUANTO_MULTIPLIER[index];
    let filled_qty = order.filled_qty.max(order.filled_qty_by_trade);
    let qty = (size_qty - left_qty - filled_qty).abs();
    crate::info!(
        "calculate_and_update_filled: qty: {} size_qty: {} left_qty: {} filled_qty: {}",
        qty,
        size_qty,
        left_qty,
        filled_qty
    );
    if by_trade {
        order.filled_qty_by_trade += qty;
    } else {
        order.filled_qty += qty;
    }
    if qty > size_qty {
        crate::error!("filled qty is larger than order qty");
        size_qty
    } else {
        qty
    }
}
