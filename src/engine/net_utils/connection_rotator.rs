use mio::Token;

pub struct ConnectionRotator<const N: usize> {
    connection_pool: [Option<Token>; N],
    connection_pool_index: usize,
}

impl<const N: usize> ConnectionRotator<N> {
    pub fn new() -> Self {
        Self {
            connection_pool: [None; N],
            connection_pool_index: 0,
        }
    }
    pub fn add_connection(&mut self, token: Token) {
        if self.connection_pool.contains(&Some(token)) {
            return;
        }
        for i in 0..N {
            if self.connection_pool[i].is_none() {
                self.connection_pool[i] = Some(token);
                return;
            }
        }
    }

    pub fn remove_connection(&mut self, token: Token) {
        for i in 0..N {
            if self.connection_pool[i] == Some(token) {
                self.connection_pool[i] = None;
                return;
            }
        }
    }

    pub fn next(&mut self) -> Option<Token> {
        for i in 0..N {
            if let Some(token) = self.connection_pool[i] {
                self.connection_pool_index = (i + 1) % N;
                return Some(token);
            }
        }
        None
    }
}
