use mio::Token;

use crate::{
    WebSocketHandle,
    configs::{GATE_IN_LEN, GATE_OUT_LEN},
    encoding::{bn_futures_order::generate_futures_user_data_ping_request, gate},
    utils::perf::system_now_in_secs,
};

#[derive(Debu<PERSON>, <PERSON><PERSON>, Co<PERSON>, PartialEq, Eq)]
pub enum ConnectionType {
    GateOrder,
    GateBbo,
    GateUserData,
    BinanceOrder,
    BinanceBbo,
    BinanceFuturesUserData,
}

pub struct ConnectionKeepAliveConfig {
    token: Token,
    last_ping_time_in_secs: u64,
    ping_interval_in_secs: u64,
    connection_type: ConnectionType,
}

pub struct ConnectionWatchDog {
    configs: Vec<ConnectionKeepAliveConfig>,
}

impl ConnectionWatchDog {
    pub fn new() -> Self {
        Self {
            configs: Vec::new(),
        }
    }

    pub fn add_connection(
        &mut self,
        token: Token,
        ping_interval_in_secs: u64,
        connection_type: ConnectionType,
    ) {
        self.configs.retain(|c| c.token != token);
        self.configs.push(ConnectionKeepAliveConfig {
            token,
            last_ping_time_in_secs: 0,
            ping_interval_in_secs,
            connection_type,
        });
    }

    pub fn remove_connection(&mut self, token: Token) {
        self.configs.retain(|c| c.token != token);
    }

    pub fn update(
        &mut self,
        handle: &mut WebSocketHandle<GATE_IN_LEN, GATE_OUT_LEN>,
    ) -> crate::Result<()> {
        let now = system_now_in_secs();
        for config in self.configs.iter_mut() {
            if now - config.last_ping_time_in_secs > config.ping_interval_in_secs {
                config.last_ping_time_in_secs = now;
                match config.connection_type {
                    ConnectionType::GateBbo
                    | ConnectionType::GateOrder
                    | ConnectionType::GateUserData => {
                        handle.send_message(config.token, gate::generate_ping_req())?;
                    }
                    ConnectionType::BinanceFuturesUserData => {
                        handle.send_message(
                            config.token,
                            generate_futures_user_data_ping_request(),
                        )?;
                    }
                    _ => {}
                }
            }
        }
        Ok(())
    }
}
