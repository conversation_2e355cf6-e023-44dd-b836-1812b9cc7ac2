use crate::utils::perf::system_now_in_ms;

pub struct RateLimiter {
    pub remain: u64,
    limit: u64,
    reset_ts_in_ms: u64,
}

impl RateLimiter {
    pub fn new() -> Self {
        Self {
            remain: 100,
            limit: 100,
            reset_ts_in_ms: 0,
        }
    }

    pub fn update(&mut self, remain: Option<u64>, limit: Option<u64>, reset_ts_in_ms: Option<u64>) {
        if let Some(remain) = remain {
            self.remain = remain;
        }
        if let Some(limit) = limit {
            self.limit = limit;
        }
        if let Some(reset_ts_in_ms) = reset_ts_in_ms {
            self.reset_ts_in_ms = reset_ts_in_ms;
        }
    }

    pub fn place_new_order(&mut self) -> bool {
        let ratio = self.remain as f64 / self.limit as f64;
        if ratio > 0.3 {
            self.remain += 1;
            return true;
        } else {
            let now = system_now_in_ms();
            if self.reset_ts_in_ms == 0 {
                self.reset_ts_in_ms = now;
                return false;
            } else if now - self.reset_ts_in_ms > 300 {
                self.reset_ts_in_ms = now;
                return true;
            }
        }
        false
    }

    pub fn use_large_price_range(&self) -> bool {
        let ratio = self.remain as f64 / self.limit as f64;
        ratio < 0.5
    }

    pub fn decrease(&mut self) {
        self.remain -= 1;
    }
}
