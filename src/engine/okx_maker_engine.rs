use std::collections::HashMap;

use crate::{
    encoding::{
        bn_futures_order::{OrderSide, generate_futures_market_order_request},
        book_ticker::FuturesBookTicker,
        cross_exchange::model::{MakerOrder, UnifiedBbo, UnifiedOrderStatus},
        futures_order_response::{OrderStatus, OrderTradeUpdate},
        gate::{self, GateBookTicker, UserTrade},
        okx,
        quantity::compute_min_hedge,
    },
    engine::{
        cross_exchange::{
            cross_exchange_const::{
                BN_BBO, BN_HEDGE_ORDERS, BN_HEDGE_TAKE_PROFIT_ORDERS, BN_LOT_SIZE, BN_MIN_NATIONAL,
                BN_MIN_QTY, CURRENCY_LEN, GATE_MIN_NATIONAL, GATE_PENDING_ORDERS,
                GATE_QUANTO_MULTIPLIER, GATE_TAKE_PROFIT_PENDING_ORDERS, HEDGE_STATUS,
                UnifiedCurrency,
            },
            hedge_manager::HedgeStatus,
            order_manager::{OrderCancelReason, OrderManager},
            order_side_price::{cal_prices, decision_gate_open_side},
            position_manager::calculate_and_update_filled,
        },
        rate_limiter::RateLimiter,
    },
    utils::perf::{circles_to_ns, now},
};

#[derive(PartialEq, Eq, PartialOrd, Ord, Clone, Copy, Debug)]
enum EngineStatus {
    Uninitialized,
    BnPositionInited,
    GatePositionInited,
    Running,
    Stopped,
}

pub struct ArbitrageGate {
    status: EngineStatus,
    position_limit: f64,
    open_notional_value: f64,
    order_manager: OrderManager,
    should_check_all_order_status: bool,
    bn_order_map: HashMap<u64, (usize, f64, OrderSide)>,
    price_range_far_offset_ratio: f64,
    price_range_far_offset_ratio_when_rate_limit: f64,
    price_near_offset_ratio_when_rate_limit: f64,
    price_range_near_offset_ratio: f64,
    open_signal_count: [u8; CURRENCY_LEN],
    close_signal_count: [u8; CURRENCY_LEN],
    signal_count_threshold: u8,
    rate_limiter: RateLimiter,
    okx_pending_orders: [MakerOrder; CURRENCY_LEN],
    okx_close_pending_orders: [MakerOrder; CURRENCY_LEN],
    bn_hedge_pending_orders: [MakerOrder; CURRENCY_LEN],
    bn_hedge_close_pending_orders: [MakerOrder; CURRENCY_LEN],
    bn_bbo: [UnifiedBbo; CURRENCY_LEN],
    okx_bbo: [UnifiedBbo; CURRENCY_LEN],
}

pub enum Exchange {
    Bn,
    Gate,
}

impl ArbitrageGate {
    pub fn new() -> Self {
        Self {
            status: EngineStatus::Uninitialized,
            position_limit: 150.0,
            open_notional_value: 30.0,
            order_manager: OrderManager::new(),
            should_check_all_order_status: false,
            bn_order_map: HashMap::new(),
            price_range_far_offset_ratio: 3.0,
            price_range_far_offset_ratio_when_rate_limit: 4.0,
            price_range_near_offset_ratio: 1.5,
            price_near_offset_ratio_when_rate_limit: 2.0,
            rate_limiter: RateLimiter::new(),
            open_signal_count: [0; CURRENCY_LEN],
            close_signal_count: [0; CURRENCY_LEN],
            signal_count_threshold: 10,
            okx_pending_orders: [MakerOrder::default(); CURRENCY_LEN],
            okx_close_pending_orders: [MakerOrder::default(); CURRENCY_LEN],
            bn_hedge_pending_orders: [MakerOrder::default(); CURRENCY_LEN],
            bn_hedge_close_pending_orders: [MakerOrder::default(); CURRENCY_LEN],
            okx_bbo: [UnifiedBbo::default(); CURRENCY_LEN],
            bn_bbo: [UnifiedBbo::default(); CURRENCY_LEN],
        }
    }

    pub fn stop(&mut self) {
        self.status = EngineStatus::Stopped;
    }

    pub fn stopped(&self) -> bool {
        self.status != EngineStatus::Running
    }

    pub fn set_should_check_all_order_status(&mut self, should_check: bool) {
        self.should_check_all_order_status = should_check;
    }

    pub fn reset_rate_limiter(
        &mut self,
        remain: Option<u64>,
        limit: Option<u64>,
        reset_ts_in_ms: Option<u64>,
    ) {
        self.rate_limiter.update(remain, limit, reset_ts_in_ms);
    }

    pub fn bn_position_inited(&mut self) {
        if self.status == EngineStatus::Uninitialized {
            self.status = EngineStatus::BnPositionInited;
        } else if self.status == EngineStatus::GatePositionInited {
            self.status = EngineStatus::Running;
        } else {
            crate::error!("bn position inited but not uninitialized");
        }
    }

    pub fn gate_position_inited(&mut self) {
        if self.status == EngineStatus::Uninitialized {
            self.status = EngineStatus::GatePositionInited;
        } else if self.status == EngineStatus::BnPositionInited {
            self.status = EngineStatus::Running;
        } else {
            crate::error!("gate position inited but not uninitialized");
        }
    }

    // #[perf_macro::measure]
    pub fn update_bn_bbo(&mut self, bbo: &FuturesBookTicker) -> Option<Vec<String>> {
        let currency = match UnifiedCurrency::from_symbol(&bbo.symbol) {
            Some(currency) => currency,
            None => return None,
        };
        let index = currency as usize;
        unsafe {
            let last = &BN_BBO[index];
            if bbo.update_id <= last.book_update_id {
                return None;
            }
            BN_BBO[index].transaction_time = bbo.transaction_time;
            BN_BBO[index].book_update_id = bbo.update_id;
            BN_BBO[index].bid_price = bbo.bid_price;
            BN_BBO[index].bid_qty = bbo.bid_qty;
            BN_BBO[index].ask_price = bbo.ask_price;
            BN_BBO[index].ask_qty = bbo.ask_qty;
        }
        let open_side = match decision_gate_open_side(index) {
            Some(side) => side,
            None => {
                return None;
            }
        };
        let mut result = vec![];
        if self.should_check_all_order_status {
            self.should_check_all_order_status = false;
            unsafe {
                for i in 0..CURRENCY_LEN {
                    let order = GATE_PENDING_ORDERS[i];
                    if order.order_id != 0 {
                        result.push(gate::check_order(order.req_id, order.order_id));
                    }
                }
                for i in 0..CURRENCY_LEN {
                    let order = GATE_TAKE_PROFIT_PENDING_ORDERS[i];
                    if order.order_id != 0 {
                        result.push(gate::check_order(order.req_id, order.order_id));
                    }
                }
            }
        }
        let close_side = open_side.reverse();
        let bid_amount = bbo.bid_qty.abs() * bbo.bid_price;
        let ask_amount = bbo.ask_qty.abs() * bbo.ask_price;
        let open_amount = if open_side == OrderSide::Buy {
            bid_amount
        } else {
            ask_amount
        };
        let close_amount = if close_side == OrderSide::Buy {
            bid_amount
        } else {
            ask_amount
        };
        if open_amount > self.open_notional_value * 3.0 && open_amount > close_amount * 2.0 {
            self.open_signal_count[index] += 1;
        } else {
            self.open_signal_count[index] = 0;
        }
        if let Some(order) = self.open_position(index, open_side) {
            result.push(order);
        }
        if close_amount > self.open_notional_value * 3.0 && close_amount > open_amount * 2.0 {
            self.close_signal_count[index] += 1;
        } else {
            self.close_signal_count[index] = 0;
        }
        if let Some(order) = self.close_position(index, close_side) {
            result.push(order);
        }
        Some(result)
    }

    pub fn cancel_all_orders(&mut self) -> Vec<String> {
        self.order_manager.cancel_all_orders()
    }

    pub fn update_cancel_order(&mut self, symbol: &str) {
        self.order_manager.update_cancel_order(symbol)
    }

    pub fn all_order_canceled(&self) -> bool {
        self.order_manager.all_order_canceled()
    }

    fn close_position(&mut self, index: usize, side: OrderSide) -> Option<String> {
        unsafe {
            let hedge_status = HEDGE_STATUS[index];
            let currency = UnifiedCurrency::from_usize(index).unwrap();
            let bn_bbo = BN_BBO[index];
            let ask_price = bn_bbo.ask_price;
            let (near_ratio, far_ratio) = self.price_range();
            let (order_price, lower_bound, upper_bound) = cal_prices(
                index,
                side,
                bn_bbo.bid_price,
                bn_bbo.ask_price,
                true,
                near_ratio,
                far_ratio,
            )?;
            let order = &mut GATE_TAKE_PROFIT_PENDING_ORDERS[index];
            let last_price = order.price;
            let status = order.status;
            if status == UnifiedOrderStatus::Uninitialized
                && hedge_status.gate_qty.abs() > 0.0
                && hedge_status.gate_qty * side.sign() < 0.0
                && !self.stopped()
                && self.close_signal_count[index] >= self.signal_count_threshold
                && self.rate_limiter.place_new_order()
            {
                let bn_lot_size = BN_LOT_SIZE[index];
                let bn_min_qty = BN_MIN_QTY[index];
                let bn_min_notional = BN_MIN_NATIONAL[index] + self.open_notional_value; // taker的盘口算开仓数量 30%
                let gate_min_notional = GATE_MIN_NATIONAL[index];
                let gate_multiplier = GATE_QUANTO_MULTIPLIER[index];
                let res = match compute_min_hedge(
                    order_price,
                    bn_lot_size,
                    bn_min_qty,
                    bn_min_notional,
                    1.0,
                    gate_min_notional,
                    gate_multiplier,
                ) {
                    Some(res) => res,
                    None => {
                        crate::error_unsafe!("compute_min_hedge failed");
                        return None;
                    }
                };
                let qty = (res.gate_contracts * GATE_QUANTO_MULTIPLIER[index])
                    .min(hedge_status.gate_qty.abs());
                let now_in_circle = now();
                OrderManager::mark_maker_order_opening(order, order_price, now_in_circle, qty);
                order.side = side;
                crate::info_unsafe!(
                    "closing position {:?} req_id {} bn price: {:.6} price: {:.6} qty {:.6}",
                    currency,
                    order.req_id,
                    ask_price,
                    order_price,
                    qty,
                );
                let order = gate::place_order(
                    order.req_id,
                    order_price,
                    order.quantity,
                    order.side,
                    "poc",
                    currency.to_gate_symbol(),
                    true,
                );
                return Some(order);
            }
            if !OrderManager::should_cancel_order(&status) {
                if !OrderManager::should_check_timeout(&status) {
                    return None;
                }
                OrderManager::check_and_set_timeout(order);
                return None;
            }

            if (last_price < lower_bound || last_price > upper_bound)
                || self.close_signal_count[index] == 0
            {
                self.rate_limiter.decrease();
                let now_in_circle = now();
                order.req_id = now_in_circle;
                order.status = UnifiedOrderStatus::Cancelling;
                order.last_modify_time = now_in_circle;
                crate::info_unsafe!(
                    r#"
Cancelling order:
req id: {}
order id: {}
order price: {:.6}
last price:{:.6}
side: {}
expect range: [{} - {}]
signal count: {}"#,
                    order.req_id,
                    order.order_id,
                    order_price,
                    last_price,
                    side.to_string(),
                    lower_bound,
                    upper_bound,
                    self.close_signal_count[index],
                );
                let order_req = gate::cancel_order(order.req_id, order.order_id);
                return Some(order_req);
            }
            None
        }
    }

    fn price_range(&self) -> (f64, f64) {
        if self.rate_limiter.use_large_price_range() {
            (
                self.price_near_offset_ratio_when_rate_limit,
                self.price_range_far_offset_ratio_when_rate_limit,
            )
        } else {
            (
                self.price_range_near_offset_ratio,
                self.price_range_far_offset_ratio,
            )
        }
    }

    fn open_position(&mut self, index: usize, side: OrderSide) -> Option<String> {
        unsafe {
            let hedge_status = HEDGE_STATUS[index];
            let amount = hedge_status.gate_avg_price * hedge_status.gate_qty * side.sign();
            let order = &mut GATE_PENDING_ORDERS[index];
            let bbo = BN_BBO[index];
            let req_id = now();
            let (near_ratio, far_ratio) = self.price_range();
            let currency = UnifiedCurrency::from_usize(index).unwrap();
            let last_price = order.price;
            let (order_price, lower_bound, upper_bound) = cal_prices(
                index,
                side,
                bbo.bid_price,
                bbo.ask_price,
                false,
                near_ratio,
                far_ratio,
            )?;
            if order.status == UnifiedOrderStatus::Uninitialized
                && !self.stopped()
                && self.open_signal_count[index] >= self.signal_count_threshold
                && amount < self.position_limit
                && self.rate_limiter.place_new_order()
            {
                let bn_lot_size = BN_LOT_SIZE[index];
                let bn_min_qty = BN_MIN_QTY[index];
                let bn_min_notional = BN_MIN_NATIONAL[index] + self.open_notional_value; // taker的盘口算开仓数量 30%
                let gate_min_notional = GATE_MIN_NATIONAL[index];
                let gate_multiplier = GATE_QUANTO_MULTIPLIER[index];
                let res = match compute_min_hedge(
                    order_price,
                    bn_lot_size,
                    bn_min_qty,
                    bn_min_notional,
                    1.0,
                    gate_min_notional,
                    gate_multiplier,
                ) {
                    Some(res) => res,
                    None => {
                        crate::error_unsafe!("compute_min_hedge failed");
                        return None;
                    }
                };
                let qty = res.gate_contracts * GATE_QUANTO_MULTIPLIER[index];
                let order = &mut GATE_PENDING_ORDERS[index];
                OrderManager::mark_maker_order_opening(order, order_price, req_id, qty);
                order.side = side;
                crate::info_unsafe!(
                    r#"
Placing order:
symbol: {:?}
request id: {}
price: {}
qty: {}
side: {}"#,
                    currency,
                    req_id,
                    order_price,
                    qty,
                    order.side.to_string(),
                );
                let order = gate::place_order(
                    req_id,
                    order_price,
                    order.quantity,
                    order.side,
                    "gtc",
                    currency.to_gate_symbol(),
                    false,
                );
                return Some(order);
            }

            if !OrderManager::should_cancel_order(&order.status) {
                if !OrderManager::should_check_timeout(&order.status) {
                    return None;
                }
                OrderManager::check_and_set_timeout(order);
                return None;
            }

            if (last_price > upper_bound || last_price < lower_bound)
                || self.open_signal_count[index] == 0
            {
                self.rate_limiter.decrease();
                GATE_PENDING_ORDERS[index].status = UnifiedOrderStatus::Cancelling;
                GATE_PENDING_ORDERS[index].req_id = req_id;
                GATE_PENDING_ORDERS[index].last_modify_time = now();
                crate::info_unsafe!(
                    r#"
Cancelling order:
request id: {}
order id: {}
symbol: {:?}
order price: {:.6}
last price: {:.6}
expect range: {:.6}-{:.6}
signal count: {}"#,
                    req_id,
                    order.order_id,
                    currency,
                    order.price,
                    last_price,
                    lower_bound,
                    upper_bound,
                    self.open_signal_count[index],
                );
                let order = gate::cancel_order(req_id, order.order_id);
                return Some(order);
            }
            None
        }
    }

    pub fn update_okx_bbo(&mut self, bbo: &okx::Bbo) -> Option<String> {
        if self.stopped() {
            return None;
        }
        let currency = match UnifiedCurrency::from_symbol(&bbo.symbol) {
            Some(currency) => currency,
            None => return None,
        };
        let index = currency as usize;
        let order = &mut self.okx_pending_orders[index];
        if order.status == UnifiedOrderStatus::Opening
            || order.status == UnifiedOrderStatus::Amending
            || order.status == UnifiedOrderStatus::Uninitialized
            || order.status == UnifiedOrderStatus::Cancelling
        {
            // pending order
            return None;
        }
        if (order.side == OrderSide::Buy && bbo.ask_price <= order.price)
            || (order.side == OrderSide::Sell && bbo.bid_price >= order.price)
        {
            crate::info!(
                r#"
Filled by bbo
order id: {}
request id: {}
symbol: {:?}
order.price: {}
order.side: {}
bbo.bid_price: {}
bbo.ask_price: {}"#,
                order.order_id,
                order.req_id,
                bbo.symbol,
                order.price,
                order.side.to_string(),
                bbo.bid_price,
                bbo.ask_price,
            );
            unsafe {
                order.status = UnifiedOrderStatus::Uninitialized;
                order.req_id = 0;
                let qty = order.quantity - order.filled_qty;
                HedgeStatus::update(index, order.price, qty.abs(), order.side, false);
                return self.hedge_bn(currency, false);
            }
        }
        let close_order = &mut self.okx_close_pending_orders[index];
        if close_order.status == UnifiedOrderStatus::Opening
            || close_order.status == UnifiedOrderStatus::Amending
            || close_order.status == UnifiedOrderStatus::Uninitialized
            || close_order.status == UnifiedOrderStatus::Cancelling
        {
            // pending order
            return None;
        }
        if (close_order.side == OrderSide::Sell && bbo.bid_price >= close_order.price)
            || (close_order.side == OrderSide::Buy && bbo.ask_price <= close_order.price)
        {
            crate::info!(
                r#"
Filled by bbo
order id: {}
req id: {}
symbol: {:?}
price: {}
side: {}
bbo.bid_price: {}
bbo.ask_price: {}"#,
                close_order.order_id,
                close_order.req_id,
                bbo.symbol,
                close_order.price,
                close_order.side.to_string(),
                bbo.bid_price,
                bbo.ask_price,
            );
            unsafe {
                let qty = close_order.quantity - close_order.filled_qty;
                HedgeStatus::update(index, close_order.price, qty.abs(), order.side, false);
                close_order.status = UnifiedOrderStatus::Uninitialized;
                close_order.req_id = 0;
                return self.hedge_bn(currency, true);
            }
        }
        None
    }

    pub fn cancel_bn_order_status_by_id(&mut self, order_id: u64) {
        if let Some(qty) = self.bn_order_map.remove(&order_id) {
            let reduce_num = if qty.2 == OrderSide::Sell {
                qty.1 * -1.0
            } else {
                qty.1
            };
            HedgeStatus::reduce_pending(qty.0, reduce_num);
        }
    }

    pub fn update_bn_order_status(&mut self, order_update: &OrderTradeUpdate) {
        if order_update.order_status != OrderStatus::Filled {
            return;
        }
        let currency = match UnifiedCurrency::from_symbol(&order_update.symbol) {
            Some(currency) => currency,
            None => {
                crate::error!("unknown symbol: {}", order_update.symbol);
                return;
            }
        };
        let index = currency as usize;
        unsafe {
            if BN_HEDGE_ORDERS[index].order_id == order_update.order_id {
                BN_HEDGE_ORDERS[index].order_id = 0;
            }
            if BN_HEDGE_TAKE_PROFIT_ORDERS[index].order_id == order_update.order_id {
                BN_HEDGE_TAKE_PROFIT_ORDERS[index].order_id = 0;
            }
        }
        crate::info!(
            r#"
Filled bn order:
symbol: {}
price: {}
side: {}
qty: {}"#,
            order_update.symbol,
            order_update.avg_price,
            order_update.order_side.to_string(),
            order_update.quantity,
        );
        self.bn_order_map.remove(&order_update.order_id);
        HedgeStatus::update(
            index,
            order_update.avg_price,
            order_update.quantity,
            order_update.order_side,
            true,
        );
    }

    pub fn cancel_gate_order(
        &mut self,
        symbol: &str,
        order_id: u64,
        req_id: u64,
        _size: f64,
        reason: OrderCancelReason,
    ) -> Option<(bool, String)> {
        let currency = match UnifiedCurrency::from_symbol(symbol) {
            Some(currency) => currency,
            None => {
                let mut target = 10000;
                let len = CURRENCY_LEN;
                for i in 0..len {
                    let p = unsafe { GATE_PENDING_ORDERS[i] };
                    if p.req_id == req_id {
                        target = i;
                        break;
                    }
                }
                if target != 10000 {
                    UnifiedCurrency::from_usize(target).unwrap()
                } else {
                    for i in 0..len {
                        let p = unsafe { GATE_TAKE_PROFIT_PENDING_ORDERS[i] };
                        if p.req_id == req_id {
                            target = i;
                            break;
                        }
                    }
                    if target != 10000 {
                        UnifiedCurrency::from_usize(target).unwrap()
                    } else {
                        crate::error!("unknown rsp {}", req_id);
                        return None;
                    }
                }
            }
        };
        let index = currency as usize;
        let (order, is_take_profit) = unsafe {
            if GATE_TAKE_PROFIT_PENDING_ORDERS[index].req_id == req_id {
                (&mut GATE_TAKE_PROFIT_PENDING_ORDERS[index], true)
            } else if GATE_PENDING_ORDERS[index].req_id == req_id {
                (&mut GATE_PENDING_ORDERS[index], false)
            } else {
                crate::error_unsafe!("unknown req id {}", req_id);
                return None;
            }
        };
        match reason {
            OrderCancelReason::MakerFailed | OrderCancelReason::TooManyRequests => {
                crate::info!("Cancelled order: {} {} {}", order_id, req_id, symbol);
                if OrderManager::can_mark_cancel(order, req_id) {
                    order.status = UnifiedOrderStatus::Uninitialized;
                    order.req_id = 0;
                }
                return None;
            }
            OrderCancelReason::NotFound => unsafe {
                if OrderManager::can_mark_cancel(order, req_id) {
                    order.status = UnifiedOrderStatus::Uninitialized;
                    order.req_id = 0;
                    let filled_qty = order.filled_qty.max(order.filled_qty_by_trade);
                    if order.quantity <= filled_qty {
                        crate::info_unsafe!(
                            "Qty is greater than order qty, symbol: {:?} order_id {} req_id {} order qty: {} filled_qty: {}, filled_by_trade_qty: {}",
                            currency,
                            order_id,
                            req_id,
                            order.quantity,
                            order.filled_qty,
                            order.filled_qty_by_trade,
                        );
                        return None;
                    }
                    let filled = order.quantity - filled_qty;
                    HedgeStatus::update(index, order.price, filled, order.side, false);
                    crate::error_unsafe!(
                        r#"
Filled by cancel request
order id: {}
req id: {}
symbol: {:?}
price: {}
side: {}
filled: {}"#,
                        order_id,
                        req_id,
                        currency,
                        order.price,
                        order.side.to_string(),
                        filled,
                    );
                    if let Some(hedge_order) = self.hedge_bn(currency, is_take_profit) {
                        return Some((true, hedge_order));
                    }
                }
            },
            OrderCancelReason::Other => unsafe {
                if OrderManager::can_mark_cancel(order, req_id) {
                    crate::info_unsafe!(
                        r#"
Canceled
order id: {}
req id: {}
symbol:{:?}
order price {}
order side: {}"#,
                        order.order_id,
                        req_id,
                        currency,
                        order.price,
                        order.side.to_string(),
                    );
                    order.status = UnifiedOrderStatus::Uninitialized;
                    order.req_id = 0;
                }
            },
        }
        None
    }

    pub fn update_checked_gate_order_status() {}

    pub fn update_gate_order_status(
        &mut self,
        status: UnifiedOrderStatus,
        symbol: &str,
        order_id: u64,
        req_id: u64,
        price: f64,
        size: f64,
        left: Option<i64>,
    ) -> Option<(bool, String)> {
        if self.stopped() {
            crate::info!("stopped");
            return None;
        }
        let currency = match UnifiedCurrency::from_symbol(symbol) {
            Some(currency) => currency,
            None => {
                let mut target = 10000;
                let len = CURRENCY_LEN;
                for i in 0..len {
                    let p = unsafe { GATE_PENDING_ORDERS[i] };
                    if p.req_id == req_id {
                        target = i;
                        break;
                    }
                }
                if target != 10000 {
                    UnifiedCurrency::from_usize(target).unwrap()
                } else {
                    crate::error!("unknown rsp {}", req_id);
                    return None;
                }
            }
        };
        let index = currency as usize;
        let side = if size > 0.0 {
            OrderSide::Buy
        } else {
            OrderSide::Sell
        };
        unsafe {
            let (order, is_take_profit) = if GATE_TAKE_PROFIT_PENDING_ORDERS[index].req_id == req_id
            {
                (&mut GATE_TAKE_PROFIT_PENDING_ORDERS[index], true)
            } else if GATE_PENDING_ORDERS[index].req_id == req_id {
                (&mut GATE_PENDING_ORDERS[index], false)
            } else {
                crate::error_unsafe!("unknown req id {}", req_id);
                return None;
            };
            match status {
                UnifiedOrderStatus::Open => {
                    if OrderManager::can_mark_open(order, req_id) {
                        order.status = UnifiedOrderStatus::Open;
                        order.order_id = order_id;
                        crate::info_unsafe!(
                            r#"
Opened order
order id: {}
req id: {}
symbol: {}
side: {}"#,
                            order_id,
                            req_id,
                            symbol,
                            side.to_string(),
                        );
                    }
                    None
                }
                UnifiedOrderStatus::PartialFilled => {
                    let status = order.status;
                    if status == UnifiedOrderStatus::Uninitialized {
                        crate::error_unsafe!("order already filled, {} {}", order_id, symbol);
                        return None;
                    }
                    if left.is_none() {
                        crate::error_unsafe!("left is none");
                        return None;
                    }
                    let qty = calculate_and_update_filled(left, order, size, index, false);
                    crate::info_unsafe!(
                        r#"
PartialFilled
order id: {}
req id: {}
symbol: {}
price: {}
side: {}
filled: {}"#,
                        order_id,
                        req_id,
                        symbol,
                        price,
                        side.to_string(),
                        qty,
                    );
                    HedgeStatus::update(index, price, qty, side, false);
                    if let Some(ho) = self.hedge_bn(currency, is_take_profit) {
                        return Some((true, ho));
                    }
                    return None;
                }
                UnifiedOrderStatus::Filled => {
                    let start = now();
                    if order_id == order.last_order_id {
                        crate::info_unsafe!("order id is last order id: {}", order_id);
                        return None;
                    }
                    let status = order.status;
                    if status == UnifiedOrderStatus::Uninitialized {
                        crate::error_unsafe!("order filled but not open, {} {}", order_id, symbol);
                        return None;
                    }
                    if status == UnifiedOrderStatus::Opening
                        || status == UnifiedOrderStatus::Amending
                    {
                        crate::warn_unsafe!("order filled before ack, {} {}", order_id, symbol);
                    }
                    let qty = calculate_and_update_filled(left, order, size, index, false);
                    crate::info_unsafe!(
                        r#"
Filled
order id: {}
req id: {}
symbol: {}
price: {}
side: {}
filled: {}"#,
                        order_id,
                        req_id,
                        symbol,
                        price,
                        side.to_string(),
                        qty,
                    );
                    HedgeStatus::update(index, price, qty.abs(), side, false);
                    OrderManager::reset_order(order);
                    if let Some(ho) = self.hedge_bn(currency, is_take_profit) {
                        crate::info_unsafe!(
                            "strategy latency: {:.1}",
                            circles_to_ns(now() - start)
                        );
                        return Some((true, ho));
                    }
                    return None;
                }
                UnifiedOrderStatus::Canceled | UnifiedOrderStatus::Expired => {
                    return self.cancel_gate_order(
                        symbol,
                        order_id,
                        req_id,
                        size,
                        OrderCancelReason::Other,
                    );
                }
                _ => None,
            }
        }
    }

    pub fn update_gate_user_trade(&mut self, trades: &Vec<UserTrade>) -> Option<(bool, String)> {
        let start = now();
        for trade in trades {
            let currency = match UnifiedCurrency::from_symbol(&trade.contract) {
                Some(currency) => currency,
                None => continue,
            };
            let index = currency as usize;
            let filled = (trade.size as f64 * GATE_QUANTO_MULTIPLIER[index]).abs();
            let is_take_profit = trade.size < 0;
            let price = match trade.price.parse::<f64>() {
                Ok(price) => price,
                Err(_) => continue,
            };
            unsafe {
                let order = if is_take_profit {
                    &mut GATE_TAKE_PROFIT_PENDING_ORDERS[index]
                } else {
                    &mut GATE_PENDING_ORDERS[index]
                };
                if order.order_id != trade.order_id {
                    continue;
                }
                if order.status == UnifiedOrderStatus::Uninitialized {
                    crate::info_unsafe!(
                        "order is uninitialized, {} {}",
                        trade.order_id,
                        trade.contract
                    );
                    continue;
                }
                crate::info_unsafe!(
                    r#"
Filled by trade:
order id: {}
symbol: {}
price: {}
side: {}
filled: {}"#,
                    trade.order_id,
                    trade.contract,
                    price,
                    order.side.to_string(),
                    filled,
                );
                let new_filled_qty_by_trade = order.filled_qty_by_trade + filled;
                order.filled_qty_by_trade = new_filled_qty_by_trade;
                if new_filled_qty_by_trade <= order.filled_qty {
                    crate::info_unsafe!(
                        "filled by trade is less than filled by order {} {}",
                        new_filled_qty_by_trade,
                        order.filled_qty
                    );
                    continue;
                }
                let side = if trade.size > 0 {
                    OrderSide::Buy
                } else {
                    OrderSide::Sell
                };
                HedgeStatus::update(index, price, filled, side, false);
                if let Some(ho) = self.hedge_bn(currency, is_take_profit) {
                    crate::info_unsafe!("strategy latency: {:.1}", circles_to_ns(now() - start));
                    return Some((true, ho));
                }
            }
        }
        None
    }

    fn hedge_bn(&mut self, currency: UnifiedCurrency, is_take_profit: bool) -> Option<String> {
        let index = currency as usize;
        let bn_hedge_diff = self.bn_hedge_diff(index);
        unsafe {
            let order = if is_take_profit {
                &mut BN_HEDGE_TAKE_PROFIT_ORDERS[index]
            } else {
                &mut BN_HEDGE_ORDERS[index]
            };
            let reduce_only = HEDGE_STATUS[index].bn_qty.abs() > HEDGE_STATUS[index].gate_qty.abs();
            order.qty = bn_hedge_diff.abs();
            order.order_id = now();
            HEDGE_STATUS[index].bn_pending -= bn_hedge_diff;
            order.side = if bn_hedge_diff > 0.0 {
                OrderSide::Sell
            } else {
                OrderSide::Buy
            };
            crate::info_unsafe!(
                "hedge bn TP: qty: {} order id: {} diff: {} current pending: {} side: {}",
                order.qty,
                order.order_id,
                bn_hedge_diff,
                HEDGE_STATUS[index].bn_pending,
                order.side.to_string()
            );

            let result = generate_futures_market_order_request(
                order.side,
                order.qty,
                currency.to_bn_symbol(),
                order.order_id,
                reduce_only,
            );
            self.bn_order_map
                .insert(order.order_id, (index, order.qty, order.side));
            return Some(result);
        }
    }

    fn bn_hedge_diff(&self, index: usize) -> f64 {
        unsafe {
            HEDGE_STATUS[index].bn_qty
                + HEDGE_STATUS[index].gate_qty
                + HEDGE_STATUS[index].bn_pending
        }
    }
}
