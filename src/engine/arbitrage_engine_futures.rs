/*
TODO:
1. 订阅1000档行情
2. 计算每30秒的avg trade vol
3. 挂载avg trade vol 内
 */

use std::collections::{BTreeMap, HashMap};

use crate::{
    Result, WebSocketHandle,
    encoding::{
        agg_trades::FuturesAggTrade,
        bn_futures_order::{
            OrderSide, generate_futures_cancel_order_request, generate_futures_order_request,
        },
        book_ticker::FuturesBookTicker,
        futures_order_response::OrderTradeUpdate,
        futures_orderbook::{FuturesOrderbookSnapshot, FuturesOrderbookUpdate},
    },
    engine::{
        futures_agg_trades_aggregator::FuturesAggTradeAggregator,
        futures_const::{
            FUTURES_IN_LEN, FUTURES_OUT_LEN, FUTURES_USDC_SYMBOL, FUTURES_USDT_SYMBOL,
        },
        futures_orderbook::{FuturesOrderBook, ImbalanceType},
        futures_position::FuturesPosition,
        opportunity_simulator::{
            Opportunity, OpportunitySimulator, OpportunityStatus, OpportunityType, PredictResult,
        },
        token::ORDER_TOKEN_1,
    },
    utils::perf::system_now_in_us,
};

pub struct ArbitrageEngineFutures {
    pub usdt_orderbook: FuturesOrderBook<20>,
    pub usdc_orderbook: FuturesOrderBook<1>,
    pub agg_traders: FuturesAggTradeAggregator<50>, // 使用const泛型，最大50个聚合数据
    pub last_opportunity: Option<Opportunity>,
    pub position: FuturesPosition,
    pub buy_order_ids: BTreeMap<u64, f64>,
    pub sell_order_ids: BTreeMap<u64, f64>,
    pub opportunity_simulator: OpportunitySimulator,
    pub take_profit_to_stop_loss_id: HashMap<u64, u64>,
    pub stop_loss_to_take_profit_id: HashMap<u64, u64>,
}

impl ArbitrageEngineFutures {
    pub fn new() -> Self {
        Self {
            usdc_orderbook: FuturesOrderBook::new(),
            usdt_orderbook: FuturesOrderBook::new(),
            agg_traders: FuturesAggTradeAggregator::new(), // 不再需要参数
            last_opportunity: None,
            position: FuturesPosition::new(),
            buy_order_ids: BTreeMap::new(),
            sell_order_ids: BTreeMap::new(),
            opportunity_simulator: OpportunitySimulator::new(),
            take_profit_to_stop_loss_id: HashMap::new(),
            stop_loss_to_take_profit_id: HashMap::new(),
        }
    }

    fn update_price(&mut self) {
        let best_bid = self.usdc_orderbook.best_bid().unwrap().price;
        let best_ask = self.usdc_orderbook.best_ask().unwrap().price;
        self.position.update_current_price(best_bid, best_ask);
    }

    pub fn update_orderbook_snapshot(&mut self, depth_snapshot: &FuturesOrderbookSnapshot) -> bool {
        if self.usdt_orderbook.apply_snapshot(depth_snapshot) {
            true
        } else {
            false
        }
    }

    pub fn update_orderbook_diff(&mut self, depth_diff: &FuturesOrderbookUpdate) -> bool {
        if self.usdt_orderbook.apply_diff(depth_diff) {
            true
        } else {
            false
        }
    }

    pub fn update_book_ticker(&mut self, book_ticker: &FuturesBookTicker) -> bool {
        match book_ticker.symbol.as_str() {
            FUTURES_USDT_SYMBOL => {
                if self.usdt_orderbook.apply_book_ticker(book_ticker) {
                    true
                } else {
                    false
                }
            }
            FUTURES_USDC_SYMBOL => {
                if self.usdc_orderbook.apply_book_ticker(book_ticker) {
                    self.update_price();
                    true
                } else {
                    false
                }
            }
            _ => {
                crate::error!("unknow symbol: {:?}", book_ticker.symbol);
                false
            }
        }
    }

    pub fn try_take_profit_or_stop_loss(
        &mut self,
        handle: &mut WebSocketHandle<FUTURES_IN_LEN, FUTURES_OUT_LEN>,
    ) -> Result<()> {
        match self.position.try_close_position() {
            Some((price, position)) => self.close_position(handle, price, position),
            None => Ok(()),
        }
    }

    pub fn add_agg_trades(&mut self, agg_trade: &FuturesAggTrade) {
        self.agg_traders.add_trades(agg_trade);
    }

    pub fn try_agg_trades_arbitrage(&mut self) -> Option<([f64; 1], OrderSide)> {
        return None;
        // if let Some(ref last_opportunity) = self.last_opportunity {
        //     if (system_now_in_us() - last_opportunity.id) < 1 {
        //         return None;
        //     }
        // }
        // if let Some(last_bar) = self.agg_traders.get_last_bar() {
        //     if last_bar.total_volume < 30_000.0 {
        //         return None;
        //     }
        //     if last_bar.buy_volume > last_bar.sell_volume * 1.5 && last_bar.open < last_bar.close {
        //         let last_trade = self.agg_traders.last_trade.as_ref().unwrap();
        //         let side = OrderSide::Buy;
        //         let price = [last_trade.price + FUTURES_PRICE_TICK_SIZE];
        //         let opportunity = Opportunity {
        //             id: system_now_in_us(),
        //             origin_order_id: 0,
        //             price: price[0],
        //             stop_price: 0.0,
        //             side,
        //             status: OpportunityStatus::Pending,
        //             predict_result: PredictResult::None,
        //             opportunity_type: OpportunityType::LargeTrade,
        //         };
        //         self.last_opportunity = Some(opportunity.clone());
        //         crate::info!(
        //             "Large trade oppurtunity: side: {} price: {}",
        //             side.to_string(),
        //             price[0]
        //         );
        //     }
        //     if last_bar.sell_volume > last_bar.buy_volume * 2.0 && last_bar.open > last_bar.close {
        //         let last_trade = self.agg_traders.last_trade.as_ref().unwrap();
        //         let side = OrderSide::Sell;
        //         let price = [last_trade.price - FUTURES_PRICE_TICK_SIZE];
        //         let opportunity = Opportunity {
        //             id: system_now_in_us(),
        //             origin_order_id: 0,
        //             price: price[0],
        //             stop_price: 0.0,
        //             side,
        //             status: OpportunityStatus::Pending,
        //             predict_result: PredictResult::None,
        //             opportunity_type: OpportunityType::LargeTrade,
        //         };
        //         self.last_opportunity = Some(opportunity.clone());
        //         crate::info!(
        //             "Large trade oppurtunity: side: {} price: {}",
        //             side.to_string(),
        //             price[0]
        //         );
        //         return Some((price, side));
        //     }
        // }
        // None
    }

    pub fn try_orderbook_arbitrage(&mut self) -> Option<([f64; 1], OrderSide)> {
        // if let Some(ref last_opportunity) = self.last_opportunity {
        //     if (system_now_in_us() - last_opportunity.id) < 1 {
        //         return None;
        //     }
        // }
        let (_, order_side) = match self.usdt_orderbook.is_imbalance() {
            ImbalanceType::BidMore(total_ask_qty) => (total_ask_qty, OrderSide::Buy),
            ImbalanceType::AskMore(total_bid_qty) => (total_bid_qty, OrderSide::Sell),
            ImbalanceType::Balanced => return None,
        };
        let order_price = match order_side {
            OrderSide::Buy => [self.usdc_orderbook.best_bid().unwrap().price],
            OrderSide::Sell => [self.usdc_orderbook.best_ask().unwrap().price],
        };
        if let Some(ref last_opportunity) = self.last_opportunity {
            if last_opportunity.price == order_price[0] && last_opportunity.side == order_side {
                return None;
            }
        }
        let opportunity = Opportunity {
            id: system_now_in_us(),
            origin_order_id: 0,
            price: order_price[0],
            stop_price: 0.0,
            side: order_side,
            status: OpportunityStatus::Pending,
            predict_result: PredictResult::None,
            opportunity_type: OpportunityType::OrderbookImbalance,
        };
        crate::debug!(
            "Orderbook imbalance oppurtunity: side: {} price: {}",
            order_side.to_string(),
            order_price[0]
        );
        self.last_opportunity = Some(opportunity.clone());
        Some((order_price, order_side))
    }

    pub fn place_order(
        &mut self,
        handle: &mut WebSocketHandle<FUTURES_IN_LEN, FUTURES_OUT_LEN>,
        prices: [f64; 1],
        side: OrderSide,
    ) -> Result<()> {
        self.cancel_order(handle, side)?;

        for price in prices {
            let order_id = self.last_opportunity.as_ref().unwrap().id;
            self.opportunity_simulator.increase_pending_count();
            handle.send_message(
                ORDER_TOKEN_1,
                generate_futures_order_request(price, side, FUTURES_USDC_SYMBOL, order_id),
            )?;
            if side == OrderSide::Buy {
                self.buy_order_ids.insert(order_id, price);
            } else {
                self.sell_order_ids.insert(order_id, price);
            }
        }
        Ok(())
    }

    pub fn close_position(
        &mut self,
        _handle: &mut WebSocketHandle<FUTURES_IN_LEN, FUTURES_OUT_LEN>,
        _price: f64,
        _position: f64,
    ) -> Result<()> {
        Ok(())
        // let side = if position > 0.0 {
        //     OrderSide::Sell
        // } else {
        //     OrderSide::Buy
        // };
        // let position = position.abs();
        // let order = generate_futures_reduce_only_request(price, side, position, FUTURES_SYMBOL);
        // handle.send_message(ORDER_TOKEN_1, order)
    }

    pub fn remove_order_by_id(
        &mut self,
        order_id: u64,
        handle: &mut WebSocketHandle<FUTURES_IN_LEN, FUTURES_OUT_LEN>,
    ) -> Result<()> {
        if self.buy_order_ids.contains_key(&order_id) {
            self.opportunity_simulator.increase_cancel_count();
            self.buy_order_ids.remove(&order_id);
        }
        if self.sell_order_ids.contains_key(&order_id) {
            self.opportunity_simulator.increase_cancel_count();
            self.sell_order_ids.remove(&order_id);
        }
        if let Some(stop_loss_id) = self.take_profit_to_stop_loss_id.remove(&order_id) {
            self.stop_loss_to_take_profit_id.remove(&stop_loss_id);
            self.opportunity_simulator.increase_fail_count();
            handle.send_message(
                ORDER_TOKEN_1,
                generate_futures_cancel_order_request(stop_loss_id),
            )?;
        }
        if let Some(take_profit_id) = self.stop_loss_to_take_profit_id.remove(&order_id) {
            self.take_profit_to_stop_loss_id.remove(&take_profit_id);
            self.opportunity_simulator.increase_fail_count();
            handle.send_message(
                ORDER_TOKEN_1,
                generate_futures_cancel_order_request(take_profit_id),
            )?;
        }
        Ok(())
    }

    pub fn cancel_order(
        &mut self,
        handle: &mut WebSocketHandle<FUTURES_IN_LEN, FUTURES_OUT_LEN>,
        side: OrderSide,
    ) -> Result<()> {
        if side == OrderSide::Buy {
            if self.sell_order_ids.len() > 0 {
                for (order_id, _) in self.sell_order_ids.iter() {
                    crate::debug!("can revere sell order: {}", order_id);
                    handle.send_message(
                        ORDER_TOKEN_1,
                        generate_futures_cancel_order_request(*order_id),
                    )?;
                }
            }
            if self.buy_order_ids.len() > 2 {
                for (order_id, _) in self.buy_order_ids.iter().take(2) {
                    crate::debug!("can old buy order: {}", order_id);
                    handle.send_message(
                        ORDER_TOKEN_1,
                        generate_futures_cancel_order_request(*order_id),
                    )?;
                }
            }
        } else if side == OrderSide::Sell {
            if self.buy_order_ids.len() > 0 {
                for (order_id, _) in self.buy_order_ids.iter() {
                    crate::debug!("cancel reverse buy order: {}", order_id);
                    handle.send_message(
                        ORDER_TOKEN_1,
                        generate_futures_cancel_order_request(*order_id),
                    )?;
                }
            }
            if self.sell_order_ids.len() > 2 {
                for (order_id, _) in self.sell_order_ids.iter().take(2) {
                    crate::debug!("cancel old sell order: {}", order_id);
                    handle.send_message(
                        ORDER_TOKEN_1,
                        generate_futures_cancel_order_request(*order_id),
                    )?;
                }
            }
        }
        Ok(())
    }

    pub fn cancel_all_orders(
        &mut self,
        handle: &mut WebSocketHandle<FUTURES_IN_LEN, FUTURES_OUT_LEN>,
    ) -> Result<()> {
        let now = system_now_in_us();
        for order_id in self.buy_order_ids.iter() {
            if now - order_id.0 < 100_000 {
                continue;
            }
            crate::debug!("cancel all orders: {} vs {}", now, order_id.0);
            handle.send_message(
                ORDER_TOKEN_1,
                generate_futures_cancel_order_request(*order_id.0),
            )?;
        }
        for order_id in self.sell_order_ids.iter() {
            if now - order_id.0 < 100_000 {
                continue;
            }
            crate::debug!("cancel all orders: {} vs {}", now, order_id.0);
            handle.send_message(
                ORDER_TOKEN_1,
                generate_futures_cancel_order_request(*order_id.0),
            )?;
        }
        Ok(())
    }

    pub fn update_trade(
        &mut self,
        trade: &OrderTradeUpdate,
        handle: &mut WebSocketHandle<FUTURES_IN_LEN, FUTURES_OUT_LEN>,
    ) -> Result<()> {
        self.position.add_trade(trade);
        // self.opportunity_simulator.add_trade(trade);
        if self
            .take_profit_to_stop_loss_id
            .contains_key(&trade.order_id)
        {
            let stop_loss_id = self
                .take_profit_to_stop_loss_id
                .remove(&trade.order_id)
                .unwrap();
            handle.send_message(
                ORDER_TOKEN_1,
                generate_futures_cancel_order_request(stop_loss_id),
            )?;
            self.opportunity_simulator.increase_success_count();
            self.stop_loss_to_take_profit_id.remove(&stop_loss_id);
            return Ok(());
        }
        if self
            .stop_loss_to_take_profit_id
            .contains_key(&trade.order_id)
        {
            let take_profit_id = self
                .stop_loss_to_take_profit_id
                .remove(&trade.order_id)
                .unwrap();
            handle.send_message(
                ORDER_TOKEN_1,
                generate_futures_cancel_order_request(take_profit_id),
            )?;
            self.opportunity_simulator.increase_fail_count();
            self.take_profit_to_stop_loss_id.remove(&take_profit_id);
            return Ok(());
        }
        if trade.order_side == OrderSide::Buy {
            self.buy_order_ids.remove(&trade.order_id);
        } else {
            self.sell_order_ids.remove(&trade.order_id);
        }
        self.opportunity_simulator.increase_filled_count();
        self.place_take_profit_and_stop_loss_maker_order(trade, handle)
    }

    pub fn place_take_profit_and_stop_loss_maker_order(
        &mut self,
        _trade: &OrderTradeUpdate,
        _handle: &mut WebSocketHandle<FUTURES_IN_LEN, FUTURES_OUT_LEN>,
    ) -> Result<()> {
        return Ok(());
        // let price = trade.price;
        // let order_side = trade.order_side;
        // let origin_opportunity_id = trade.order_id;
        // let opportunities = generate_take_profit_and_stop_loss_opportunity(
        //     price,
        //     order_side,
        //     origin_opportunity_id,
        // );
        // self.opportunity_simulator
        //     .add_take_profit_and_stop_loss_opportunity(&opportunities);
        // handle.send_message(
        //     ORDER_TOKEN_1,
        //     generate_take_profit_and_stop_loss_order_request(&opportunities[0], FUTURES_SYMBOL),
        // )?;
        // handle.send_message(
        //     ORDER_TOKEN_1,
        //     generate_take_profit_and_stop_loss_order_request(&opportunities[1], FUTURES_SYMBOL),
        // )?;
        // self.take_profit_to_stop_loss_id
        //     .insert(opportunities[0].id, opportunities[1].id);
        // self.stop_loss_to_take_profit_id
        //     .insert(opportunities[1].id, opportunities[0].id);
        // Ok(())
    }
}
