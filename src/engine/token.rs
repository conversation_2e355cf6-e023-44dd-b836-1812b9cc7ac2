use mio::Token;

pub const REST_TOKEN: Token = Token(0);

pub const SBE_BBO_1: Token = Token(10);
pub const SBE_BBO_2: Token = Token(11);
pub const SBE_BBO_3: Token = Token(12);
pub const SBE_BBO_4: Token = Token(13);
pub const SBE_BBO_5: Token = Token(14);
pub const SBE_BBO_6: Token = Token(15);

pub const ORDER_TOKEN_1: Token = Token(21);
pub const ORDER_TOKEN_2: Token = Token(22);
pub const ORDER_TOKEN_3: Token = Token(23);
pub const ORDER_TOKEN_4: Token = Token(24);

pub const TRADE_TOKEN_1: Token = Token(31);
pub const TRADE_TOKEN_2: Token = Token(32);
pub const TRADE_TOKEN_3: Token = Token(33);
pub const TRADE_TOKEN_4: Token = Token(34);

pub const SBE_DEPTH_DF_1: Token = Token(41);
pub const SBE_DEPTH_DF_2: Token = Token(42);
pub const SBE_DEPTH_DF_3: Token = Token(43);
pub const SBE_DEPTH_DF_4: Token = Token(44);

pub const WS_BBO_1: Token = Token(50);
pub const WS_BBO_2: Token = Token(51);
pub const WS_BBO_3: Token = Token(52);
pub const WS_BBO_4: Token = Token(53);

pub const SBE_DEPTH_SS_1: Token = Token(61);
pub const SBE_DEPTH_SS_2: Token = Token(62);
pub const SBE_DEPTH_SS_3: Token = Token(63);
pub const SBE_DEPTH_SS_4: Token = Token(64);

pub const WS_DEPTH_SS_1: Token = Token(70);
pub const WS_DEPTH_SS_2: Token = Token(71);

pub const WS_DEPTH_DF_T_1: Token = Token(81);
pub const WS_DEPTH_DF_T_2: Token = Token(82);
pub const WS_DEPTH_DF_T_3: Token = Token(83);
pub const WS_DEPTH_DF_T_4: Token = Token(84);

pub const WS_AGG_TRADE_T_1: Token = Token(91);
pub const WS_AGG_TRADE_T_2: Token = Token(92);

pub const USER_DATA_STREAM_1: Token = Token(98);

pub const HTTP_ORDERBOOK_SNAPSHOT_1: Token = Token(99);

pub const WAKER_TOKEN: Token = Token(100);
