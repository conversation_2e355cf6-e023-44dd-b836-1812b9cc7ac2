#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Generate Binance USDT-M Futures funding details for the past N hours:
- Fees (commission, commission rebate)
- Realized PnL (by symbol)
- Funding fees (by symbol)
- Transfer records (spot<->futures, via both income and SAPI transfer history)

Environment:
  BINANCE_API_KEY, BINANCE_API_SECRET

Usage:
  python scripts/binance_futures_funding_report.py --hours 6 [--format markdown|csv]

Implementation notes:
- Uses Futures Income History: GET /fapi/v1/income (base https://fapi.binance.com)
  Types include: COMMISSION, COMMISSION_REBATE, REALIZED_PNL, FUNDING_FEE, TRANSFER, ...
- For transfer direction details, also queries: GET /sapi/v1/futures/transfer
- Pagination: iterates startTime with cursor = last_time + 1 while hitting page limit.
"""
from __future__ import annotations
import os
import sys
import time
import hmac
import hashlib
import argparse
from typing import Dict, List, Tuple

import requests

BINANCE_KEY = os.getenv("BINANCE_API_KEY", "9HbITeDCxX614QhColLUiJimEa4MLhmCFYmRH6mhjCnY52L1HIOemqVNaXEhbFnX")
BINANCE_SEC = os.getenv("BINANCE_API_SECRET", "D0vcompTOn2qN8VfW6UeXEMXo3ubtpHBacsyhA9Vh7LVq0xrPedw5nypP4L5pSxo")
FAPI_BASE = "https://fapi.binance.com"
SAPI_BASE = "https://api.binance.com"

session = requests.Session()
session.headers.update({"Accept": "application/json", "User-Agent": "futures-funding-report/1.0"})


def _signed_get(base: str, path: str, params: dict | None = None):
    params = params.copy() if params else {}
    params["timestamp"] = int(time.time() * 1000)
    qs = "&".join(f"{k}={params[k]}" for k in params)
    sig = hmac.new(BINANCE_SEC.encode(), qs.encode(), hashlib.sha256).hexdigest()
    url = f"{base}{path}?{qs}&signature={sig}"
    headers = {"X-MBX-APIKEY": BINANCE_KEY}
    r = session.get(url, headers=headers, timeout=20)
    r.raise_for_status()
    return r.json()


# ---------- Income history (Futures) ----------


def get_income_history(start_ms: int, end_ms: int, income_type: str | None = None, limit: int = 1000) -> List[dict]:
    """Fetch all income records within [start_ms, end_ms]."""
    all_rows: List[dict] = []
    cursor = int(start_ms)
    while cursor <= end_ms:
        params = {"startTime": cursor, "endTime": end_ms, "limit": limit}
        if income_type:
            params["incomeType"] = income_type
        try:
            page = _signed_get(FAPI_BASE, "/fapi/v1/income", params) or []
        except Exception:
            page = []
        if not page:
            break
        all_rows.extend(page)
        # advance by last time
        try:
            max_t = max(int(float(x.get("time", 0))) for x in page)
        except Exception:
            max_t = cursor
        next_cursor = max_t + 1
        if next_cursor <= cursor:
            next_cursor = cursor + 1
        cursor = next_cursor
        # if page not full, likely done
        if len(page) < limit and cursor > max_t:
            if cursor > end_ms:
                break
    return all_rows


# ---------- SAPI futures transfer history (Spot<->Futures) ----------


def get_sapi_futures_transfers(
    start_ms: int, end_ms: int, asset: str | None = None, current: int = 1, size: int = 100
) -> List[dict]:
    """GET /sapi/v1/futures/transfer history. Paginates by 'current' pages."""
    rows: List[dict] = []
    page = int(current)
    while True:
        params = {"startTime": start_ms, "endTime": end_ms, "current": page, "size": size}
        if asset:
            params["asset"] = asset
        try:
            data = _signed_get(SAPI_BASE, "/sapi/v1/futures/transfer", params) or {}
        except Exception:
            break
        l = data.get("rows") or []
        if not l:
            break
        rows.extend(l)
        total = int(data.get("total", 0) or 0)
        if len(rows) >= total:
            break
        page += 1
    return rows


# ---------- Unrealized PnL (current) ----------


def get_unrealized_by_symbol() -> Dict[str, float]:
    """Fetch current unrealized PnL per symbol from position risk and aggregate sides.
    Endpoint: GET /fapi/v2/positionRisk (USER_DATA)
    """
    try:
        data = _signed_get(FAPI_BASE, "/fapi/v2/positionRisk", {}) or []
    except Exception:
        return {}
    upnl: Dict[str, float] = {}
    for p in data:
        try:
            sym = p.get("symbol") or ""
            if not sym:
                continue
            # Hedge mode returns multiple rows per symbol (BOTH/LONG/SHORT); sum them.
            raw = p.get("unRealizedProfit", p.get("unrealizedProfit", 0))
            val = float(raw or 0)
            if abs(val) < 1e-18:
                # keep zeros out to reduce noise
                continue
            upnl[sym] = upnl.get(sym, 0.0) + val
        except Exception:
            continue
    return upnl


# ---------- Formatting ----------


def fmt_markdown(summary: dict, transfers: List[Tuple[str, str, str, float, str]]) -> str:
    lines: List[str] = []
    # Fees
    lines.append("## 手续费汇总 (Futures)")
    lines.append("资产 | 手续费")
    lines.append(":--|--:")

    for asset, amt in sorted(summary["fees_by_asset"].items()):
        lines.append(f"{asset} | {amt:.8f}")
    lines.append("")
    # Commission rebate
    if summary["rebate_by_asset"]:
        lines.append("## 返佣汇总 (Commission Rebate)")
        lines.append("资产 | 返佣")
        lines.append(":--|--:")
        for asset, amt in sorted(summary["rebate_by_asset"].items()):
            lines.append(f"{asset} | {amt:.8f}")
        lines.append("")
    # Realized PnL
    lines.append("## 实现盈亏 (USDT 计价) - 按合约")
    lines.append("合约 | Realized PnL (USDT)")
    lines.append(":--|--:")
    total_pnl = 0.0
    for sym, v in sorted(summary["realized_by_symbol"].items()):
        lines.append(f"{sym} | {v:+.6f}")
        total_pnl += v
    lines.append(f"合计 | {total_pnl:+.6f}")
    lines.append("")
    # Funding fees
    if summary["funding_by_symbol"]:
        lines.append("## 资金费 (Funding) - 按合约")
        lines.append("合约 | Funding (USDT)")
        lines.append(":--|--:")
        for sym, v in sorted(summary["funding_by_symbol"].items()):
            lines.append(f"{sym} | {v:+.6f}")
        lines.append("")
    # Transfers
    # Unrealized PnL
    if summary.get("unrealized_by_symbol"):
        lines.append("## 未实现盈亏 (USDT 计价) - 按合约")
        lines.append("合约 | Unrealized PnL (USDT)")
        lines.append(":--|--:")
        total_upnl = 0.0
        for sym, v in sorted(summary["unrealized_by_symbol"].items()):
            lines.append(f"{sym} | {v:+.6f}")
            total_upnl += v
        lines.append(f"合计 | {total_upnl:+.6f}")
        lines.append("")

    lines.append("## 资金划转 (Spot<->Futures)")
    lines.append("时间 | 类型 | 资产 | 数量 | 方向/状态")
    lines.append(":--|:--|:--|--:|:--")
    for ts_str, typ, asset, amt, note in transfers:
        lines.append(f"{ts_str} | {typ} | {asset} | {amt:.8f} | {note}")
    return "\n".join(lines)


def fmt_csv(summary: dict, transfers: List[Tuple[str, str, str, float, str]]) -> str:
    lines: List[str] = []
    lines.append("[Fees]")
    lines.append("asset,fee")
    for asset, amt in sorted(summary["fees_by_asset"].items()):
        lines.append(f"{asset},{amt}")
    lines.append("")
    lines.append("[CommissionRebate]")
    lines.append("asset,rebate")
    for asset, amt in sorted(summary["rebate_by_asset"].items()):
        lines.append(f"{asset},{amt}")
    lines.append("")
    lines.append("[RealizedPnL]")
    lines.append("symbol,pnl_usdt")
    for sym, v in sorted(summary["realized_by_symbol"].items()):
        lines.append(f"{sym},{v}")
    lines.append("")
    lines.append("[Funding]")
    lines.append("symbol,funding_usdt")
    for sym, v in sorted(summary["funding_by_symbol"].items()):
        lines.append(f"{sym},{v}")
    lines.append("")
    # Unrealized PnL (CSV)
    if summary.get("unrealized_by_symbol"):
        lines.append("[UnrealizedPnL]")
        lines.append("symbol,upnl_usdt")
        for sym, v in sorted(summary["unrealized_by_symbol"].items()):
            lines.append(f"{sym},{v}")
        lines.append("")

    lines.append("[Transfers]")
    lines.append("time,type,asset,amount,note")
    for ts_str, typ, asset, amt, note in transfers:
        esc_note = str(note).replace("\\", "\\\\").replace('"', '\\"')
        lines.append(f'{ts_str},{typ},{asset},{amt},"{esc_note}"')
    return "\n".join(lines)


# ---------- Main ----------


def main():
    if not BINANCE_KEY or not BINANCE_SEC:
        print("请先设置环境变量 BINANCE_API_KEY 与 BINANCE_API_SECRET", file=sys.stderr)
        sys.exit(2)

    ap = argparse.ArgumentParser(description="Binance USDT-M 期货资金明细")
    ap.add_argument("--hours", type=int, default=6, help="过去N小时")
    ap.add_argument("--format", choices=["markdown", "csv"], default="markdown")
    args = ap.parse_args()

    now_ms = int(time.time() * 1000)
    start_ms = now_ms - max(1, args.hours) * 3600_000

    # Income aggregation
    incomes = get_income_history(start_ms, now_ms)

    fees_by_asset: Dict[str, float] = {}
    rebate_by_asset: Dict[str, float] = {}
    realized_by_symbol: Dict[str, float] = {}
    funding_by_symbol: Dict[str, float] = {}
    transfer_incomes: List[dict] = []

    for r in incomes or []:
        try:
            itype = (r.get("incomeType") or r.get("type") or "").upper()
            asset = r.get("asset") or "USDT"
            sym = r.get("symbol") or ""
            amt = float(r.get("income", 0) or 0)
            if itype == "COMMISSION":
                fees_by_asset[asset] = fees_by_asset.get(asset, 0.0) + amt
            elif itype == "COMMISSION_REBATE":
                rebate_by_asset[asset] = rebate_by_asset.get(asset, 0.0) + amt
            elif itype == "REALIZED_PNL":
                if sym:
                    realized_by_symbol[sym] = realized_by_symbol.get(sym, 0.0) + amt
            elif itype == "FUNDING_FEE":
                if sym:
                    funding_by_symbol[sym] = funding_by_symbol.get(sym, 0.0) + amt
            elif itype == "TRANSFER":
                transfer_incomes.append(r)
        except Exception:
            continue

    # Transfer history via SAPI for direction details
    sapi_transfers = get_sapi_futures_transfers(start_ms, now_ms)
    # Build map by tranId -> type
    tran_dir: Dict[str, str] = {}
    for t in sapi_transfers:
        try:
            tid = str(t.get("tranId") or t.get("tranIdLong") or "")
            typ = int(t.get("type", 0) or 0)
            # 1: spot->USDT-M futures, 2: USDT-M futures->spot, 3/4: COIN-M, etc.
            if typ == 1:
                tran_dir[tid] = "spot->UMFutures"
            elif typ == 2:
                tran_dir[tid] = "UMFutures->spot"
            elif typ == 3:
                tran_dir[tid] = "spot->CMFutures"
            elif typ == 4:
                tran_dir[tid] = "CMFutures->spot"
            else:
                tran_dir[tid] = f"type={typ}"
        except Exception:
            continue

    from datetime import datetime, timezone

    def _fmt_ts(ms: int) -> str:
        try:
            return datetime.fromtimestamp(ms / 1000.0, tz=timezone.utc).astimezone().strftime("%Y-%m-%d %H:%M:%S")
        except Exception:
            return str(ms)

    transfers: List[Tuple[str, str, str, float, str]] = []
    for r in transfer_incomes:
        try:
            ts = int(r.get("time", 0) or 0)
            asset = r.get("asset") or "USDT"
            amt = float(r.get("income", 0) or 0)
            tran_id = str(r.get("tranId") or r.get("info") or "")
            note = tran_dir.get(tran_id, tran_id)
            transfers.append((_fmt_ts(ts), "transfer", asset, amt, note))
        except Exception:
            continue

    upnl_by_symbol: Dict[str, float] = get_unrealized_by_symbol()
    summary = {
        "fees_by_asset": fees_by_asset,
        "rebate_by_asset": rebate_by_asset,
        "realized_by_symbol": realized_by_symbol,
        "funding_by_symbol": funding_by_symbol,
        "unrealized_by_symbol": upnl_by_symbol,
    }

    if args.format == "markdown":
        out = fmt_markdown(summary, sorted(transfers, key=lambda x: x[0]))
    else:
        out = fmt_csv(summary, sorted(transfers, key=lambda x: x[0]))

    print(out)


if __name__ == "__main__":
    main()
