#!/usr/bin/env python3
"""
Fetch Gate.io USDT-Settled Perpetual Futures public trades over a specified time range and save as CSV.

- Uses public endpoint: GET https://api.gateio.ws/api/v4/futures/usdt/trades
- Contract format: BASE_USDT (e.g., BTC_USDT); converts from Binance-like BTCUSDT automatically
- Time range in seconds: use query params `from` and `to`
- Robust windowing with adaptive split: if a window returns `limit` rows (possible truncation), auto-splits
- Deduplicates across window boundaries by trade `id`

Example:
  python scripts/fetch_gate_futures_trades.py \
      --symbol BTCUSDT \
      --start "2025-09-01T00:00:00Z" \
      --end   "2025-09-01T03:00:00Z" \
      --out   data/gate_data/BTC_USDT_trades_20250901_0000_0300.csv

Notes:
- Gate v4 docs indicate last_id is deprecated for futures trades; prefer time-based `from`/`to`.
- This script adaptively splits windows to avoid hitting per-request limits.
- Time arguments are assumed to be UTC unless a timezone offset is supplied.
"""

from __future__ import annotations

import argparse
import csv
import os
import sys
import time
from datetime import datetime, timezone
from typing import Dict, Iterable, List, Optional, Tuple

import requests

BASE_URL = "https://api.gateio.ws/api/v4"
TRADES_PATH = "/futures/usdt/trades"

# Defaults
LIMIT_DEFAULT = 1000  # Gate commonly allows up to 1000 on many public endpoints
CHUNK_SECONDS_DEFAULT = 300  # 5 minutes initial chunk; will auto-split further if needed


def parse_time_arg(value: str) -> int:
    """Parse a time argument into epoch milliseconds.

    Accepts:
    - ISO8601: "YYYY-MM-DDTHH:MM:SSZ", with offset, or "YYYY-MM-DD HH:MM:SS" (assumed UTC)
    - Integer epoch seconds or milliseconds (auto-detected)
    """
    v = value.strip()
    if v.isdigit():
        iv = int(v)
        return iv if iv >= 1_000_000_000_000 else iv * 1000
    if v.endswith("Z"):
        v = v[:-1] + "+00:00"
    try:
        dt = datetime.fromisoformat(v)
        if dt.tzinfo is None:
            dt = dt.replace(tzinfo=timezone.utc)
        return int(dt.timestamp() * 1000)
    except Exception:
        raise argparse.ArgumentTypeError(f"Unrecognized time format: {value}")


def format_iso_ms(ms: int) -> str:
    dt = datetime.fromtimestamp(ms / 1000, tz=timezone.utc)
    return dt.strftime("%Y-%m-%dT%H:%M:%S.%fZ")[:-3]


def mk_session(timeout: int = 20) -> requests.Session:
    s = requests.Session()
    s.headers.update(
        {
            "User-Agent": "libwebsocket-rs-scripts/1.0 (+https://gate.io)",
            "Accept": "application/json",
        }
    )
    s.request = _with_timeout(s.request, timeout)
    return s


def _with_timeout(request_func, timeout_default: int):
    def wrapper(method, url, **kwargs):
        if "timeout" not in kwargs:
            kwargs["timeout"] = timeout_default
        return request_func(method, url, **kwargs)

    return wrapper


def to_contract(symbol_or_contract: str) -> str:
    s = (symbol_or_contract or "").upper().replace("-", "_")
    if "_" in s:
        return s
    # Convert BTCUSDT -> BTC_USDT
    if s.endswith("USDT"):
        return s[:-4] + "_USDT"
    return s


def fetch_trades_once(
    session: requests.Session,
    contract: str,
    start_sec: int,
    end_sec: int,
    limit: int,
    retry: int = 5,
    backoff_base: float = 0.5,
) -> List[Dict]:
    url = BASE_URL + TRADES_PATH
    params = {
        "contract": contract,
        "from": start_sec,
        "to": end_sec,
        "limit": max(1, min(1000, int(limit))),
    }

    attempt = 0
    while True:
        attempt += 1
        try:
            resp = session.get(url, params=params)
            if resp.status_code == 429:
                ra = resp.headers.get("Retry-After")
                sleep_s = float(ra) if ra else min(30.0, backoff_base * (2 ** (attempt - 1)))
                time.sleep(sleep_s)
                continue
            if resp.status_code >= 500:
                time.sleep(min(30.0, backoff_base * (2 ** (attempt - 1))))
                if attempt <= retry:
                    continue
            resp.raise_for_status()
            data = resp.json()
            if not isinstance(data, list):
                raise RuntimeError(f"Unexpected response for futures trades: {data}")
            return data
        except requests.RequestException:
            if attempt > retry:
                raise
            time.sleep(min(30.0, backoff_base * (2 ** (attempt - 1))))


def fetch_trades_window_adaptive(
    session: requests.Session,
    contract: str,
    start_sec: int,
    end_sec: int,
    limit: int = LIMIT_DEFAULT,
    max_depth: int = 12,
) -> List[Dict]:
    """Fetch trades in [start_sec, end_sec] (seconds) with adaptive splitting to avoid truncation.

    If the returned row count == limit, we conservatively split the window and fetch both halves.
    """
    if end_sec <= start_sec:
        return []

    rows = fetch_trades_once(session, contract, start_sec, end_sec, limit)

    if len(rows) < limit or max_depth <= 0:
        return rows

    # Potential truncation: split window into two halves and merge recursively
    mid = start_sec + (end_sec - start_sec) // 2
    left = fetch_trades_window_adaptive(session, contract, start_sec, mid, limit, max_depth - 1)
    right = fetch_trades_window_adaptive(session, contract, mid, end_sec, limit, max_depth - 1)
    return left + right


def write_csv(path: str, rows: Iterable[Dict], write_header: bool) -> int:
    os.makedirs(os.path.dirname(path) or ".", exist_ok=True)
    # Gate futures trades typical fields: id, create_time_ms, price, size, contract, ...
    fieldnames = [
        "contract",
        "trade_id",
        "price",
        "size",
        "create_time_ms",
        "create_time_iso",
    ]
    count = 0
    with open(path, "a", newline="", encoding="utf-8") as f:
        writer = csv.DictWriter(f, fieldnames=fieldnames)
        if write_header:
            writer.writeheader()
        for it in rows:
            # Prefer create_time (seconds) for Gate futures; multiply to ms
            ct_ms = 0
            try:
                if it.get("create_time") is not None:
                    ct_ms = int(float(it.get("create_time")) * 1000.0)
                else:
                    raw_ct = it.get("create_time_ms")
                    ct_f = float(raw_ct) if raw_ct is not None else 0.0
                    # Heuristic: values < 1e12 are seconds; >= 1e12 are milliseconds
                    ct_ms = int(ct_f * 1000.0) if ct_f < 1_000_000_000_000 else int(ct_f)
            except Exception:
                ct_ms = 0

            print(f"create_time: {it.get('create_time')}, create_time_ms: {it.get('create_time_ms')}, ct_ms: {ct_ms}")

            writer.writerow(
                {
                    "contract": it.get("contract"),
                    "trade_id": it.get("id"),
                    "price": it.get("price"),
                    "size": it.get("size"),
                    "create_time_ms": ct_ms,
                    "create_time_iso": format_iso_ms(ct_ms) if ct_ms else "",
                }
            )
            count += 1
    return count


def default_out_path(contract: str, start_ms: int, end_ms: int) -> str:
    start_s = datetime.fromtimestamp(start_ms / 1000, tz=timezone.utc).strftime("%Y%m%d_%H%M%S")
    end_s = datetime.fromtimestamp(end_ms / 1000, tz=timezone.utc).strftime("%Y%m%d_%H%M%S")
    return os.path.join("data", "gate_data", f"{contract}_futures_trades_{start_s}_{end_s}.csv")


def main():
    parser = argparse.ArgumentParser(
        description="Fetch Gate.io USDT Perpetual public trades over a time range and save to CSV"
    )
    parser.add_argument("--symbol", required=True, help="Symbol or contract, e.g., BTCUSDT or BTC_USDT")
    parser.add_argument(
        "--start", required=True, type=parse_time_arg, help="Start time (ISO8601 or epoch s/ms), UTC assumed if no TZ"
    )
    parser.add_argument(
        "--end", required=True, type=parse_time_arg, help="End time (ISO8601 or epoch s/ms), UTC assumed if no TZ"
    )
    parser.add_argument(
        "--out",
        default=None,
        help="Output CSV path; default to data/gate_data/<contract>_futures_trades_<start>_<end>.csv",
    )
    parser.add_argument(
        "--chunk-seconds",
        type=int,
        default=CHUNK_SECONDS_DEFAULT,
        help="Initial chunk size in seconds; auto-splits on high volume",
    )
    parser.add_argument("--limit", type=int, default=LIMIT_DEFAULT, help="Per-request limit (max 1000)")
    parser.add_argument("--quiet", action="store_true", help="Less verbose output")

    args = parser.parse_args()

    print(f"Symbol: {args.symbol}")
    contract = to_contract(args.symbol)
    print(f"Contract: {contract}")
    start_ms = args.start
    end_ms = args.end
    if end_ms <= start_ms:
        print("end must be greater than start", file=sys.stderr)
        sys.exit(2)

    out_path = args.out or default_out_path(contract, start_ms, end_ms)
    os.makedirs(os.path.dirname(out_path) or ".", exist_ok=True)

    session = mk_session()

    total = 0
    wrote_header = not os.path.exists(out_path) or os.path.getsize(out_path) == 0

    if not args.quiet:
        print(f"Fetching {contract} futures trades from {format_iso_ms(start_ms)} to {format_iso_ms(end_ms)}")
        print(f"Output: {out_path}")

    seen_ids: set[int] = set()

    # Iterate by initial chunk seconds; within each window we adaptively split if needed
    start_sec = start_ms // 1000
    end_sec = end_ms // 1000

    cur = start_sec
    idx = 0
    while cur < end_sec:
        idx += 1
        nxt = min(cur + int(args.chunk_seconds), end_sec)
        if not args.quiet:
            print(f"Chunk {idx}: {format_iso_ms(cur*1000)} -> {format_iso_ms(nxt*1000)} ...", end=" ")
            sys.stdout.flush()
        try:
            rows = fetch_trades_window_adaptive(session, contract, cur, nxt, limit=args.limit)
            # Dedup by trade id across windows
            filtered: List[Dict] = []
            for r in rows:
                tid = r.get("id")
                try:
                    tid_i = int(tid)
                except Exception:
                    tid_i = None
                if tid_i is not None and tid_i in seen_ids:
                    continue
                if tid_i is not None:
                    seen_ids.add(tid_i)
                filtered.append(r)
            if filtered:
                wrote = write_csv(out_path, filtered, write_header=wrote_header)
                wrote_header = False
                total += wrote
            if not args.quiet:
                print(f"{len(filtered)} rows")
        except KeyboardInterrupt:
            print("\nInterrupted. Exiting...")
            break
        except Exception as e:
            print(f"\nError in chunk {idx}: {e}", file=sys.stderr)
            raise
        finally:
            cur = nxt

    if not args.quiet:
        print(f"Done. Wrote {total} rows to {out_path}")


if __name__ == "__main__":
    main()
