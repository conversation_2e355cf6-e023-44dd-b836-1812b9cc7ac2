#!/usr/bin/env python3
"""
筛选Binance和Gate都交易的永续合约，按交易量排序并计算price tick占比
"""

import requests
import json
import time
import csv
from typing import Dict, List, Tuple, Optional
import sys

# 代理配置 - 如果不需要代理，设置为None
PROXIES = {"http": "http://127.0.0.1:7890", "https": "http://127.0.0.1:7890"}
# PROXIES = None


def get_binance_futures_info() -> Dict[str, Dict]:
    """获取Binance永续合约信息"""
    url = "https://fapi.binance.com/fapi/v1/exchangeInfo"

    for attempt in range(3):
        try:
            print(f"正在获取Binance永续合约信息... (尝试 {attempt + 1}/3)")
            response = requests.get(url, proxies=PROXIES, timeout=30)
            response.raise_for_status()
            data = response.json()

            futures_info = {}
            for symbol_info in data["symbols"]:
                if (
                    symbol_info["status"] == "TRADING"
                    and symbol_info["contractType"] == "PERPETUAL"
                    and symbol_info["quoteAsset"] == "USDT"
                ):

                    symbol = symbol_info["symbol"]

                    # 提取价格过滤器信息
                    price_tick = None
                    lot_size = None
                    min_qty = None
                    min_notional = None

                    for filter_info in symbol_info["filters"]:
                        if filter_info["filterType"] == "PRICE_FILTER":
                            price_tick = float(filter_info["tickSize"])
                        elif filter_info["filterType"] == "LOT_SIZE":
                            lot_size = float(filter_info["stepSize"])
                            min_qty = float(filter_info["minQty"])
                        elif filter_info["filterType"] == "MIN_NOTIONAL":
                            min_notional = float(filter_info["notional"])

                    futures_info[symbol] = {
                        "baseAsset": symbol_info["baseAsset"],
                        "quoteAsset": symbol_info["quoteAsset"],
                        "priceTick": price_tick,
                        "lotSize": lot_size,
                        "minQty": min_qty,
                        "minNotional": min_notional,
                        "status": symbol_info["status"],
                    }

            print(f"获取到 {len(futures_info)} 个Binance永续合约")
            return futures_info

        except Exception as e:
            print(f"获取Binance永续合约信息失败 (尝试 {attempt + 1}/3): {e}")
            if attempt < 2:
                time.sleep(2)
            continue

    return {}


def get_gate_futures_contracts() -> Dict[str, Dict]:
    """获取Gate永续合约信息"""
    url = "https://api.gateio.ws/api/v4/futures/usdt/contracts"

    try:
        print("正在获取Gate永续合约信息...")
        response = requests.get(url, proxies=PROXIES, timeout=30)
        response.raise_for_status()
        data = response.json()

        contracts = {}
        for contract in data:
            if contract["type"] == "direct" and not contract["in_delisting"]:
                symbol = contract["name"]  # 例如: BTC_USDT

                # 转换为Binance格式的symbol (去掉下划线)
                binance_symbol = symbol.replace("_", "")

                # 计算Gate的min notional (最小名义价值)
                # Gate的min notional = order_size_min * mark_price (如果有mark_price的话)
                mark_price = float(contract.get("mark_price", 0)) if contract.get("mark_price") else 0
                order_size_min = int(contract.get("order_size_min", 1))
                quanto_multiplier = float(contract.get("quanto_multiplier", 1))
                gate_min_notional = mark_price * order_size_min * quanto_multiplier if mark_price > 0 else 0

                # Gate的一份合约对应的base asset数量
                # 通常等于quanto_multiplier，表示1张合约代表多少个base asset
                contract_size = quanto_multiplier  # 1张合约对应的base asset数量

                contracts[binance_symbol] = {
                    "name": symbol,
                    "underlying": contract.get("underlying", ""),
                    "quote_currency": contract.get("quote_currency", ""),
                    "settle_currency": contract.get("settle_currency", ""),
                    "leverage_min": float(contract.get("leverage_min", 1)),
                    "leverage_max": float(contract.get("leverage_max", 1)),
                    "mark_price": mark_price,
                    "index_price": float(contract.get("index_price", 0)) if contract.get("index_price") else 0,
                    "order_price_round": float(contract.get("order_price_round", 0.01)),
                    "order_size_min": order_size_min,
                    "order_size_max": int(contract.get("order_size_max", 1000000)),
                    "quanto_multiplier": quanto_multiplier,
                    # Gate的lot size和min notional信息
                    "lot_size": 1,  # Gate通常以整数张数交易，最小单位为1张
                    "min_notional": gate_min_notional,  # 计算得出的最小名义价值
                    "contract_size": contract_size,  # 1张合约对应的base asset数量
                }

        print(f"获取到 {len(contracts)} 个Gate永续合约")
        return contracts

    except Exception as e:
        print(f"获取Gate永续合约信息失败: {e}")
        return {}


def calculate_price_tick_ratio(price: float, tick_size: float) -> float:
    """计算price tick占比"""
    if price <= 0 or tick_size <= 0:
        return 0
    return (tick_size / price) * 100


def _ts_ms_now() -> int:
    return int(time.time() * 1000)


def get_binance_futures_klines_1m(symbol: str, start_ms: int, end_ms: int) -> List[Tuple[int, float]]:
    """获取Binance USDT-M 永续合约 1m K线收盘价，返回[(openTimeMs, closePrice), ...]
    注：/fapi/v1/klines 单次支持 up to 1500 根，24小时=1440，单次即可。
    """
    url = "https://fapi.binance.com/fapi/v1/klines"
    params = {
        "symbol": symbol,
        "interval": "1m",
        "startTime": int(start_ms),
        "endTime": int(end_ms),
        "limit": 1500,
    }
    for attempt in range(3):
        try:
            r = requests.get(url, params=params, proxies=PROXIES, timeout=30)
            r.raise_for_status()
            data = r.json() or []
            out: List[Tuple[int, float]] = []
            for k in data:
                # kline array: [openTime, open, high, low, close, volume, closeTime, ...]
                ts = int(k[0])
                close_px = float(k[4])
                out.append((ts, close_px))
            return out
        except Exception:
            if attempt < 2:
                time.sleep(1.0)
            else:
                return []
    return []


def get_gate_futures_candles_1m(contract: str, start_s: int, end_s: int) -> List[Tuple[int, float]]:
    """获取Gate USDT永续 1m K线收盘价，返回[(timeMs, closePrice), ...]
    文档：GET /futures/usdt/candlesticks?contract=BTC_USDT&interval=1m&from&to
    返回对象含 t(秒), c 收盘。
    """
    url = "https://api.gateio.ws/api/v4/futures/usdt/candlesticks"
    params = {
        "contract": contract,
        "interval": "1m",
        "from": int(start_s),
        "to": int(end_s),
    }
    for attempt in range(3):
        try:
            r = requests.get(url, params=params, proxies=PROXIES, timeout=30)
            r.raise_for_status()
            arr = r.json() or []
            out: List[Tuple[int, float]] = []
            for it in arr:
                # API 文档是对象数组，但某些环境可能返回列表形式 [t, v, c, h, l, o]
                try:
                    if isinstance(it, dict):
                        t_s = int(it.get("t", 0))
                        c_px = float(it.get("c", 0))
                    else:
                        # 尝试位置解析: [t, v, c, h, l, o] 或 [t, o, h, l, c, v]
                        t_s = int(float(it[0]))
                        # 尝试把 c 放到正确位置（优先 it[2] 再 it[4]）
                        try:
                            c_px = float(it[2])
                        except Exception:
                            c_px = float(it[4])
                    if t_s > 0 and c_px > 0:
                        out.append((t_s * 1000, c_px))
                except Exception:
                    continue
            # Gate 返回时间升序/降序不一定，统一排序
            out.sort(key=lambda x: x[0])
            return out
        except Exception:
            if attempt < 2:
                time.sleep(1.0)
            else:
                return []
    return []


def compute_minute_kline_diff_stats(symbol: str, gate_contract: str) -> Tuple[float, float, float, int]:
    """计算过去24小时 1m 收盘价差(Binance-Gate)的最大、最小、平均值。
    返回 (max_diff, min_diff, avg_diff, overlap_points)
    """
    end_ms = _ts_ms_now()
    start_ms = end_ms - 24 * 3600 * 1000
    bn = get_binance_futures_klines_1m(symbol, start_ms, end_ms)
    gt = get_gate_futures_candles_1m(gate_contract, start_ms // 1000, end_ms // 1000)
    if not bn or not gt:
        return (0.0, 0.0, 0.0, 0)

    # 对齐分钟，以 openTimeMs 为键
    bn_map = dict(bn)
    gt_map = dict(gt)
    diffs: List[float] = []
    # 仅使用共同时间戳
    for ts in sorted(set(bn_map.keys()) & set(gt_map.keys())):
        diffs.append((bn_map[ts] - gt_map[ts]) / bn_map[ts])

    if not diffs:
        return (0.0, 0.0, 0.0, 0)

    max_diff = max(diffs)
    min_diff = min(diffs)
    avg_diff = sum(diffs) / len(diffs)
    return (max_diff, min_diff, avg_diff, len(diffs))


def main():
    print("开始获取Binance和Gate永续合约数据...")

    binance_futures = get_binance_futures_info()

    gate_contracts = get_gate_futures_contracts()

    if not binance_futures or not gate_contracts:
        print("获取数据失败，退出程序")
        return

    common_symbols = set(binance_futures.keys()) & set(gate_contracts.keys())
    results = []

    for symbol in common_symbols:
        binance_info = binance_futures[symbol]
        gate_info = gate_contracts[symbol]

        results.append(
            {
                "symbol": symbol,
                "base_asset": binance_info["baseAsset"],
                "binance_price_tick": binance_info["priceTick"],
                "binance_lot_size": binance_info["lotSize"],
                "binance_min_qty": binance_info["minQty"],
                "binance_min_notional": binance_info["minNotional"],
                "gate_price_tick": gate_info["order_price_round"],
                "gate_lot_size": gate_info["lot_size"],
                "gate_order_size_min": gate_info["order_size_min"],
                "gate_min_notional": gate_info["min_notional"],
                "gate_contract_size": gate_info["contract_size"],
                "gate_quanto_multiplier": gate_info["quanto_multiplier"],
            }
        )

    # whitelist = ["SOL", "MYX", "WLD", "DOGE", "XRP", "SOMI", "TA", "WLFI", "OPEN", "ENA", "ADA"]
    whitelist = [
        "FIO",
        "WOO",
        "APT",
        "MEW",
        "ALT",
        "SOL",
        "ETH",
    ]
    top_80_results = [r for r in results if r["base_asset"] in whitelist]
    # top_80_results = results
    print(len(top_80_results))

    available_sym = []

    # 计算过去24小时分钟K线对比（仅对筛选出来的合约）
    print("\n正在计算过去24小时1分钟K线价差(Binance-Gate)...")
    for row in top_80_results:
        sym = row["symbol"]
        gate_contract = gate_contracts.get(sym, {}).get("name", f"{row['base_asset']}_USDT")
        if "SOL" in sym or "ETH" in sym:
            row["long_offset"] = 0.0003
            row["short_offset"] = -0.0003
        else:
            row["long_offset"] = 0.0005
            row["short_offset"] = -0.0005
        print(f"symbol: {sym}, gate_contract: {gate_contract}")
        available_sym.append(sym)

    top_80_results = [r for r in results if r["symbol"] in available_sym]

    csv_file = "binance_gate_futures_comparison.csv"
    json_file = "binance_gate_futures_comparison.json"

    # 写入CSV文件
    with open(csv_file, "w", newline="", encoding="utf-8") as csvfile:
        if top_80_results:
            fieldnames = top_80_results[0].keys()
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(top_80_results)

    # 写入JSON文件
    output_data = {
        "metadata": {
            "total_contracts_found": len(results),
            "top_contracts_saved": len(top_80_results),
            "generated_at": time.strftime("%Y-%m-%d %H:%M:%S"),
        },
        "contracts": top_80_results,
    }

    with open(json_file, "w", encoding="utf-8") as jsonfile:
        json.dump(output_data, jsonfile, indent=2, ensure_ascii=False)

    print(f"\n完整结果已保存到:")
    print(f"- CSV格式: {csv_file}")
    print(f"- JSON格式: {json_file}")


if __name__ == "__main__":
    main()
