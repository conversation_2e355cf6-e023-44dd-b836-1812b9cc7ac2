import requests
import time
from time import sleep

CORP_ID = "ww95e76226b475125a"
CORP_SECRET = "-5emOPIIwtPi4fRZkDrtS0DmYVwV03qfPWO0kktG-jw"
AGENT_ID = 1000003
TO_USER_1 = "HuLingChuan"
TO_USER_2 = "tonyyang"

_token_cache = {"value": None, "expire_at": 0}

import os, time, hmac, hashlib, json
import requests
from collections import defaultdict
from urllib.parse import quote_plus

BINANCE_KEY = os.getenv("BINANCE_API_KEY", "3HXZr1PVaBeCp0KReDTDCKDPsdA56yMZKrFGhsUMEnOjsEJsAOUzyjhsDZSCx5J2")
BINANCE_SEC = os.getenv("BINANCE_API_SECRET", "YobjCzXHi5OBXidaafg537zdDCotyhLVPbAsWfUhBrRjSehuPr0xWR40OpkhEt4r")
GATE_KEY = os.getenv("GATE_API_KEY", "efb5fb382886a7bb26a3302f22a7a4a7")
GATE_SEC = os.getenv("GATE_API_SECRET", "adc72b8896c9692114f35cf330a9ee8775d2a200b1b540eba5e33f112230eca6")

BINANCE_BASE = "https://fapi.binance.com"
GATE_BASE = "https://api.gateio.ws"
GATE_PREFIX = "/api/v4"

# 允许的偏差（以“基准币数量”为单位），考虑到量化与精度问题可适当放大
ABS_TOL = float(os.getenv("HEDGE_ABS_TOL", "1e-8"))

QUOTES = ("USDT", "USDC", "BUSD", "USD", "FDUSD", "USDD", "USDE")

session = requests.Session()
session.headers.update({"Accept": "application/json", "User-Agent": "hedge-checker/1.0"})


def norm_base_from_binance_symbol(sym: str) -> str:
    s = sym.upper()
    for q in QUOTES:
        if s.endswith(q):
            return s[: -len(q)]
    return s


def norm_base_from_gate_contract(contract: str) -> str:
    # e.g. "BTC_USDT" -> "BTC"
    return contract.split("_", 1)[0].upper()


# ---------- Binance: signed GET ----------
def binance_signed_get(path: str, params: dict = None):
    if params is None:
        params = {}
    params["timestamp"] = int(time.time() * 1000)
    qs = "&".join(f"{k}={params[k]}" for k in params)  # keep insertion order
    sig = hmac.new(BINANCE_SEC.encode(), qs.encode(), hashlib.sha256).hexdigest()
    url = f"{BINANCE_BASE}{path}?{qs}&signature={sig}"
    headers = {"X-MBX-APIKEY": BINANCE_KEY}
    r = session.get(url, headers=headers, timeout=15)
    r.raise_for_status()
    return r.json()


# ---------- Gate: sign per APIv4 doc ----------
def gate_sign(method: str, url_path: str, query_string: str = "", payload: str = ""):
    # Hex(SHA512(payload))
    m = hashlib.sha512()
    m.update((payload or "").encode("utf-8"))
    payload_hash = m.hexdigest()
    ts = str(int(time.time()))
    sign_str = f"{method}\n{url_path}\n{query_string}\n{payload_hash}\n{ts}"
    sign = hmac.new(GATE_SEC.encode("utf-8"), sign_str.encode("utf-8"), hashlib.sha512).hexdigest()
    return {"KEY": GATE_KEY, "Timestamp": ts, "SIGN": sign, "Content-Type": "application/json"}


def gate_private_get(path: str, query: dict | None = None):
    query = query or {}
    # build query string in exact order
    qstr = "&".join([f"{k}={query[k]}" for k in query])  # no url-encode for signing
    url_path = f"{GATE_PREFIX}{path}"
    headers = gate_sign("GET", url_path, qstr, "")
    url = f"{GATE_BASE}{url_path}" + (f"?{qstr}" if qstr else "")
    r = session.get(url, headers=headers, timeout=15)
    r.raise_for_status()
    return r.json()


def gate_public_get(path: str):
    url = f"{GATE_BASE}{GATE_PREFIX}{path}"
    r = session.get(url, timeout=15)
    r.raise_for_status()
    return r.json()


# ---------- Binance: signed POST ----------
def binance_signed_post(path: str, params: dict = None):
    if params is None:
        params = {}
    params["timestamp"] = int(time.time() * 1000)
    qs = "&".join(f"{k}={params[k]}" for k in params)  # keep insertion order
    sig = hmac.new(BINANCE_SEC.encode(), qs.encode(), hashlib.sha256).hexdigest()
    url = f"{BINANCE_BASE}{path}"
    headers = {"X-MBX-APIKEY": BINANCE_KEY}
    data = f"{qs}&signature={sig}"
    r = session.post(url, data=data, headers=headers, timeout=15)
    r.raise_for_status()
    return r.json()


# ---------- Gate: signed POST ----------
def gate_private_post(path: str, payload: dict = None):
    payload = payload or {}
    payload_str = json.dumps(payload) if payload else ""
    url_path = f"{GATE_PREFIX}{path}"
    headers = gate_sign("POST", url_path, "", payload_str)
    url = f"{GATE_BASE}{url_path}"
    r = session.post(url, data=payload_str, headers=headers, timeout=15)
    r.raise_for_status()
    return r.json()


def gate_private_post_qs(path: str, query: dict | None = None, payload: dict | None = None):
    """Signed POST with optional query-string and JSON payload."""
    query = query or {}
    payload = payload or {}
    qstr = "&".join([f"{k}={query[k]}" for k in query])
    payload_str = json.dumps(payload) if payload else ""
    url_path = f"{GATE_PREFIX}{path}"
    headers = gate_sign("POST", url_path, qstr, payload_str)
    url = f"{GATE_BASE}{url_path}" + (f"?{qstr}" if qstr else "")
    r = session.post(url, data=payload_str, headers=headers, timeout=15)
    r.raise_for_status()
    return r.json()


# ---------- Fetch & parse ----------
def get_binance_exposures():
    """
    Returns dict: {base_symbol: (qty_in_base_units (float), entry_price (float))}
    Only USDT-M futures via /fapi/v3/positionRisk
    """
    exposures = defaultdict(lambda: [0.0, 0.0])  # [qty, weighted_entry_price]
    data = binance_signed_get("/fapi/v3/positionRisk")
    for p in data:
        # fields: symbol, positionAmt, positionSide (BOTH/LONG/SHORT), entryPrice, etc.
        amt = float(p.get("positionAmt", "0"))
        if abs(amt) < 1e-18:
            continue
        side = p.get("positionSide", "BOTH")
        entry_price = float(p.get("entryPrice", "0"))

        signed_amt = amt
        if side == "SHORT":
            # In hedge mode, positionAmt is positive for each side; SHORT should be negative exposure
            signed_amt = -abs(amt)
        elif side == "LONG":
            signed_amt = +abs(amt)
        # BOTH: amt already signed (+ long / - short)

        base = norm_base_from_binance_symbol(p["symbol"])

        # Calculate weighted average entry price
        current_qty, current_weighted_price = exposures[base]
        total_qty = current_qty + signed_amt

        if abs(total_qty) > 1e-18:
            # Weighted average entry price calculation
            if abs(current_qty) > 1e-18 and abs(signed_amt) > 1e-18:
                # Both positions exist, calculate weighted average
                total_notional = current_qty * current_weighted_price + signed_amt * entry_price
                new_entry_price = total_notional / total_qty
            else:
                # Only one position exists
                new_entry_price = entry_price if abs(signed_amt) > 1e-18 else current_weighted_price
        else:
            new_entry_price = 0.0

        exposures[base] = [total_qty, new_entry_price]

    # Convert to final format: {base: (qty, entry_price)}
    return {base: (data[0], data[1]) for base, data in exposures.items() if abs(data[0]) > 1e-18}


# cache for Gate contract multipliers
_gate_mult_cache: dict[str, float] = {}


def get_gate_quanto_multiplier(contract: str) -> float:
    if contract in _gate_mult_cache:
        return _gate_mult_cache[contract]
    info = gate_public_get(f"/futures/usdt/contracts/{quote_plus(contract)}")
    # prefer "quanto_multiplier"
    qmul = info.get("quanto_multiplier")
    if qmul is None:
        # Some old docs may use "multiplier" naming; fallback
        qmul = info.get("multiplier", "1")
    mul = float(qmul)
    _gate_mult_cache[contract] = mul
    return mul


def get_gate_exposures():
    """
    Returns dict: {base_symbol: (qty_in_base_units (float), entry_price (float))}
    Uses /futures/usdt/positions?holding=true
    base_qty = size (signed contracts) * quanto_multiplier
    """
    exposures = defaultdict(lambda: [0.0, 0.0])  # [qty, weighted_entry_price]
    positions = gate_private_get("/futures/usdt/positions", {"holding": "true"})
    for pos in positions:
        contract = pos["contract"]
        size = float(pos.get("size", 0))
        if abs(size) < 1e-18:
            continue
        mul = get_gate_quanto_multiplier(contract)
        base_qty = size * mul  # signed
        entry_price = float(pos.get("entry_price", "0"))

        base = norm_base_from_gate_contract(contract)

        # Calculate weighted average entry price
        current_qty, current_weighted_price = exposures[base]
        total_qty = current_qty + base_qty

        if abs(total_qty) > 1e-18:
            # Weighted average entry price calculation
            if abs(current_qty) > 1e-18 and abs(base_qty) > 1e-18:
                # Both positions exist, calculate weighted average
                total_notional = current_qty * current_weighted_price + base_qty * entry_price
                new_entry_price = total_notional / total_qty
            else:
                # Only one position exists
                new_entry_price = entry_price if abs(base_qty) > 1e-18 else current_weighted_price
        else:
            new_entry_price = 0.0

        exposures[base] = [total_qty, new_entry_price]

    # Convert to final format: {base: (qty, entry_price)}
    return {base: (data[0], data[1]) for base, data in exposures.items() if abs(data[0]) > 1e-18}


# ---------- Open orders (Gate futures USDT) ----------


def get_gate_open_orders(limit: int = 99) -> list:
    """Return a list of open futures orders on Gate USDT-M.
    Tries the canonical endpoint with status=open. Falls back gracefully.
    """
    try:
        res = gate_private_get("/futures/usdt/orders", {"status": "open", "limit": str(limit)})
        print(f"res: {res}")
        return res or []
    except Exception as e:
        print(f"get_gate_open_orders failed: {e}")
        # some deployments may not accept limit; retry without
        try:
            return gate_private_get("/futures/usdt/orders", {"status": "open"}) or []
        except Exception as e:
            print(f"get_gate_open_orders failed: {e}")
            return []


def build_gate_open_orders_msg(limit: int = 99) -> str:
    orders = get_gate_open_orders(limit=limit)
    print(f"orders: {orders}")
    if not orders:
        return "当前无未成交订单"
    lines: list[str] = ["== Gate 未成交订单 =="]
    for o in orders:
        try:
            contract = o.get("contract") or o.get("symbol") or "?"
            contract = contract.replace("_", "")
            contract = contract.replace("USDT", "")
            size = o.get("size")
            price = o.get("price", "0")
            tif = o.get("tif") or o.get("time_in_force") or ""
            try:
                size_f = float(size if size is not None else 0)
            except Exception:
                size_f = 0.0
            try:
                price_f = float(price if price is not None else 0)
            except Exception:
                price_f = 0.0
            side = "BUY" if size_f > 0 else ("SELL" if size_f < 0 else "?")
            size_abs = abs(size_f)
            lines.append(f"{contract}\t{side}\t{size_abs:.0f}@{price_f}\t{tif}")
        except Exception:
            # robust formatting fallback
            try:
                lines.append(str(o))
            except Exception:
                lines.append(repr(o))
    return "\n".join(lines)


# ---------- Open orders (Binance futures USDT) ----------


def get_binance_open_orders() -> list:
    """Return a list of open futures orders on Binance USDT-M.
    Prefer all-symbol query; if it fails, fall back to per-symbol queries.
    """
    try:
        res = binance_signed_get("/fapi/v1/openOrders")
        if isinstance(res, list):
            return res
    except Exception:
        pass
    # fallback: iterate tradable USDT perpetual symbols
    orders: list = []
    try:
        symbols = get_binance_all_symbols()
        for sym in symbols:
            try:
                arr = binance_signed_get("/fapi/v1/openOrders", {"symbol": sym})
                if isinstance(arr, list) and arr:
                    orders.extend(arr)
            except Exception:
                continue
    except Exception:
        return []
    # de-duplicate by orderId
    seen = set()
    uniq = []
    for o in orders:
        oid = o.get("orderId")
        if oid in seen:
            continue
        seen.add(oid)
        uniq.append(o)
    return uniq


def build_bn_open_orders_msg() -> str:
    orders = get_binance_open_orders()
    if not orders:
        return "当前无未成交订单"
    lines: list[str] = ["== Binance 未成交订单 =="]
    for o in orders:
        try:
            symbol = o.get("symbol") or "?"
            base = norm_base_from_binance_symbol(symbol)
            side = (o.get("side") or "").upper()
            tif = o.get("timeInForce") or ""
            try:
                price_f = float(o.get("price", 0) or 0)
            except Exception:
                price_f = 0.0
            try:
                orig = float(o.get("origQty", 0) or 0)
                execd = float(o.get("executedQty", 0) or 0)
                qty = max(orig - execd, 0.0)
            except Exception:
                qty = float(o.get("origQty", 0) or 0)
            lines.append(f"{base}\t{side}\t{qty:.2f}@{price_f}")
        except Exception:
            try:
                lines.append(str(o))
            except Exception:
                lines.append(repr(o))
    return "\n".join(lines)


# ---------- 杠杆设置功能 ----------


def get_binance_all_symbols():
    """获取Binance所有USDT永续合约交易对"""
    try:
        data = session.get(f"{BINANCE_BASE}/fapi/v1/exchangeInfo", timeout=15).json()
        symbols = []
        for symbol_info in data.get("symbols", []):
            if (
                symbol_info.get("status") == "TRADING"
                and symbol_info.get("contractType") == "PERPETUAL"
                and symbol_info.get("quoteAsset") == "USDT"
            ):
                symbols.append(symbol_info["symbol"])
        return symbols
    except Exception as e:
        print(f"获取Binance交易对失败: {e}")
        return []


def get_gate_all_contracts():
    """获取Gate所有USDT永续合约"""
    try:
        data = gate_public_get("/futures/usdt/contracts")
        contracts = []
        for contract in data:
            if contract.get("type") == "direct":
                contracts.append(contract["name"])
        return contracts
    except Exception as e:
        print(f"获取Gate合约失败: {e}")
        return []


def set_binance_leverage(symbol: str, leverage: int):
    """设置Binance单个交易对的杠杆"""
    try:
        params = {"symbol": symbol, "leverage": leverage}
        result = binance_signed_post("/fapi/v1/leverage", params)
        return True, result
    except Exception as e:
        return False, str(e)


def set_gate_leverage(contract: str, leverage: int):
    """设置Gate单个合约的杠杆。
    依据当前仓位的保证金模式选择正确的字段：
    - 逐仓(iso) 使用 { "leverage": "5" }
    - 全仓(cross) 使用 { "cross_leverage_limit": "5" }
    若无法可靠判断，将尝试两种字段并回退。
    返回 (ok: bool, result_or_error: Any)
    """
    try:
        # 1) 校验合约存在
        try:
            _ = gate_public_get(f"/futures/usdt/contracts/{quote_plus(contract)}")
        except Exception:
            return False, f"Gate合约不存在或不可交易: {contract}"

        # 2) 查询当前仓位，判断保证金模式（cross/isolated）
        use_cross = None
        pos = None
        try:
            pos = gate_private_get(f"/futures/usdt/positions/{quote_plus(contract)}")
            # 常见判断字段：
            # - margin_mode: "cross" / "isolated"
            # - cross_mode: bool
            # - 若 cross 模式，通常返回 cross_leverage_limit；逐仓则返回 leverage
            mm = (pos.get("margin_mode") or "").lower() if isinstance(pos, dict) else ""
            if mm in ("cross", "isolated"):
                use_cross = mm == "cross"
            elif isinstance(pos, dict):
                if pos.get("cross_mode") is True:
                    use_cross = True
                elif pos.get("cross_mode") is False:
                    use_cross = False
                else:
                    # 通过字段存在性/数值推断
                    lev = pos.get("leverage")
                    cross_lev = pos.get("cross_leverage_limit")
                    try:
                        use_cross = float(cross_lev or 0) > 0 and float(lev or 0) == 0
                    except Exception:
                        use_cross = None
        except Exception:
            use_cross = None

        # 3) 根据判断调用接口；构造候选 payload 并依次尝试（保证始终包含 leverage）
        def _post(payload: dict):
            # 将 leverage 放在 query-string 中，兼容某些服务端仅从 query 读取的实现
            return gate_private_post_qs(
                f"/futures/usdt/positions/{contract}/leverage",
                query={"leverage": str(leverage)},
                payload=payload,
            )

        payloads: list[dict] = []
        if use_cross is True:
            # 优先：同时传 leverage 与 cross_leverage_limit，其次仅传 leverage
            payloads = [
                {"leverage": str(leverage), "cross_leverage_limit": str(leverage)},
                {"leverage": str(leverage)},
                {"cross_leverage_limit": str(leverage)},
            ]
        elif use_cross is False:
            payloads = [
                {"leverage": str(leverage)},
                {"leverage": str(leverage), "cross_leverage_limit": str(leverage)},
            ]
        else:
            # 未知模式：先逐仓，再两者都传，最后仅 cross 限额
            payloads = [
                {"leverage": str(leverage)},
                {"leverage": str(leverage), "cross_leverage_limit": str(leverage)},
                {"cross_leverage_limit": str(leverage)},
            ]

        last_err = None
        for p in payloads:
            try:
                result = _post(p)
                return True, result
            except requests.HTTPError as e:
                try:
                    last_err = e.response.text or str(e)
                except Exception:
                    last_err = str(e)
            except Exception as e:
                last_err = str(e)
        return False, (last_err or "Gate leverage update failed")

    except Exception as e:
        return False, str(e)


def set_all_leverage(leverage: int):
    """设置所有交易对的杠杆"""
    results = {"binance": {"success": [], "failed": []}, "gate": {"success": [], "failed": []}, "leverage": leverage}

    # 设置Binance杠杆
    print(f"开始设置Binance杠杆为 {leverage}x...")
    binance_symbols = get_binance_all_symbols()
    for symbol in binance_symbols:
        success, result = set_binance_leverage(symbol, leverage)
        if success:
            results["binance"]["success"].append(symbol)
        else:
            results["binance"]["failed"].append(f"{symbol}: {result}")

    # 设置Gate杠杆
    print(f"开始设置Gate杠杆为 {leverage}x...")
    gate_contracts = get_gate_all_contracts()
    for contract in gate_contracts:
        success, result = set_gate_leverage(contract, leverage)
        if success:
            results["gate"]["success"].append(contract)
        else:
            results["gate"]["failed"].append(f"{contract}: {result}")

    return results


# ---------- Accounts & Snapshots ----------


def get_binance_account_totals() -> tuple[float, float]:
    """Return (wallet_balance, total_unrealized_pnl) in USDT for Binance USDT-M futures."""
    try:
        data = binance_signed_get("/fapi/v2/account")
        wb = float(data.get("totalWalletBalance", 0) or 0)
        upnl = float(data.get("totalUnrealizedProfit", 0) or 0)
        return wb, upnl
    except Exception:
        return 0.0, 0.0


def get_gate_account_totals() -> tuple[float, float]:
    """Return (balance, total_unrealized_pnl) for Gate USDT futures account."""
    try:
        data = gate_private_get("/futures/usdt/accounts")
        # Gate fields naming variants: prefer total, balance and total_unrealized_pnl/unrealised_pnl
        bal = data.get("total") if isinstance(data, dict) else None
        if bal is None:
            bal = data.get("balance", 0) if isinstance(data, dict) else 0
        upnl = (
            (data.get("total_unrealized_pnl") if isinstance(data, dict) else None)
            or (data.get("total_unrealised_pnl") if isinstance(data, dict) else None)
            or (data.get("unrealized_pnl") if isinstance(data, dict) else None)
            or (data.get("unrealised_pnl") if isinstance(data, dict) else None)
            or 0
        )
        return float(bal or 0), float(upnl or 0)
    except Exception:
        return 0.0, 0.0


def make_snapshot(binance_exp: dict, gate_exp: dict):
    """Canonical snapshot of positions for change detection."""
    bases = sorted(set(binance_exp) | set(gate_exp))
    snap = []
    for b in bases:
        bq, bp = binance_exp.get(b, (0.0, 0.0))
        gq, gp = gate_exp.get(b, (0.0, 0.0))
        snap.append(
            (
                b,
                round(bq, 8),
                round(bp, 4),
                round(gq, 8),
                round(gp, 4),
            )
        )
    return tuple(snap)


from datetime import datetime
from zoneinfo import ZoneInfo


# ---------- Compare ----------
def compare_and_print(
    binance_exp: dict,
    gate_exp: dict,
    tol: float = ABS_TOL,
    bn_totals: tuple[float, float] | None = None,
    gt_totals: tuple[float, float] | None = None,
):
    bases = sorted(set(binance_exp) | set(gate_exp))
    lines: list[str] = []
    lines.append("==Bn&Gt PositionCheck==")

    # Account summary
    if bn_totals or gt_totals:
        bn_bal, bn_upnl = bn_totals or (0.0, 0.0)
        gt_bal, gt_upnl = gt_totals or (0.0, 0.0)
        sum_bal = bn_bal + gt_bal
        sum_upnl = bn_upnl + gt_upnl
        sum_line = f"Sum    {sum_bal:.2f} UPNL {sum_upnl:.2f}"
        lines.append(f"BN Bal {bn_bal:.2f}  UPNL {bn_upnl:.2f}")
        lines.append(f"Gt Bal {gt_bal:.2f}  UPNL {gt_upnl:.2f}")

    if not bases:
        lines.append("no positions")
        return "\n".join(lines)

    all_ok = True
    for b in bases:
        # Extract quantity and entry price for Binance
        if b in binance_exp:
            bq, bn_entry = binance_exp[b]
        else:
            bq, bn_entry = 0.0, 0.0

        # Extract quantity and entry price for Gate
        if b in gate_exp:
            gq, gate_entry = gate_exp[b]
        else:
            gq, gate_entry = 0.0, 0.0

        ok = abs(bq + gq) <= tol
        all_ok &= ok
        prefix = "✅" if ok else "❌"

        # Calculate price ratio (BN entry / Gate entry) as percentage
        if gq > 0 and bq < 0:
            price_ratio = (bn_entry / gate_entry - 1.0) * 100.0
            price_ratio_str = f"{price_ratio:+.4f}%"  # 百分比
        elif gq < 0 and bq > 0:
            price_ratio = (gate_entry / bn_entry - 1.0) * 100.0
            price_ratio_str = f"{price_ratio:+.4f}%"  # 百分比
        else:
            price_ratio_str = "N/A"

        # Format entry prices
        bn_entry_str = f"{bn_entry:.5f}" if abs(bn_entry) > 1e-18 else "N/A"
        gate_entry_str = f"{gate_entry:.5f}" if abs(gate_entry) > 1e-18 else "N/A"

        # Mobile-friendly multi-line summary per base
        lines.append(f"{prefix} {b} BN\t{bq:.1f}@{bn_entry_str} Gt\t{gq:.1f}@{gate_entry_str} D: {price_ratio_str}")

    now = datetime.now(ZoneInfo("Asia/Shanghai"))
    ts_str = now.strftime("%Y-%m-%d %H:%M")
    lines = [f"{'✅' if all_ok else '❌'} - {ts_str}\n" + sum_line] + lines
    return "\n".join(lines)


def check():
    for name, val in [
        ("BINANCE_API_KEY", BINANCE_KEY),
        ("BINANCE_API_SECRET", BINANCE_SEC),
        ("GATE_API_KEY", GATE_KEY),
        ("GATE_API_SECRET", GATE_SEC),
    ]:
        if not val:
            raise SystemExit(f"环境变量 {name} 未设置")

    try:
        binance_exp = get_binance_exposures()
    except Exception as e:
        raise SystemExit(f"获取 Binance 持仓失败: {e}")

    try:
        gate_exp = get_gate_exposures()
    except Exception as e:
        raise SystemExit(f"获取 Gate 持仓失败: {e}")

    return compare_and_print(binance_exp, gate_exp, ABS_TOL)


def get_token():
    if time.time() < _token_cache["expire_at"] - 60 and _token_cache["value"]:
        return _token_cache["value"]
    url = "https://qyapi.weixin.qq.com/cgi-bin/gettoken"
    resp = requests.get(url, params={"corpid": CORP_ID, "corpsecret": CORP_SECRET}, timeout=5)
    data = resp.json()
    if data.get("errcode") != 0:
        raise RuntimeError(f"gettoken failed: {data}")
    _token_cache["value"] = data["access_token"]
    _token_cache["expire_at"] = time.time() + data.get("expires_in", 7200)
    return _token_cache["value"]


def send_text(content: str):
    token = get_token()
    url = f"https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token={token}"
    payload = {
        "touser": TO_USER_2,
        "msgtype": "text",
        "agentid": AGENT_ID,
        "text": {"content": content},
        "duplicate_check_interval": 600,
    }
    r = requests.post(url, json=payload, timeout=5)
    print(r.json())
    payload = {
        "touser": TO_USER_1,
        "msgtype": "text",
        "agentid": AGENT_ID,
        "text": {"content": content},
        "duplicate_check_interval": 600,
    }
    r = requests.post(url, json=payload, timeout=5)
    print(r.json())
    return r.json()


_prev_snapshot = None


def get_snapshot():
    binance_exp = get_binance_exposures()
    gate_exp = get_gate_exposures()
    bn_totals = get_binance_account_totals()
    gt_totals = get_gate_account_totals()
    msg = compare_and_print(binance_exp, gate_exp, ABS_TOL, bn_totals, gt_totals)
    return msg


# ---------- Close detection & summary ----------
from typing import Dict, Tuple, List


def _snap_to_map(snap: tuple) -> Dict[str, Tuple[float, float, float, float]]:
    m = {}
    for b, bq, bp, gq, gp in snap:
        m[b] = (bq, bp, gq, gp)
    return m


def _base_to_bn_symbol(base: str) -> str:
    return f"{base}USDT"


def _base_to_gt_contract(base: str) -> str:
    return f"{base}_USDT"


def binance_user_trades(symbol: str, start_ms: int, end_ms: int) -> list:
    params = {"symbol": symbol, "startTime": start_ms, "endTime": end_ms, "limit": 1000}
    try:
        return binance_signed_get("/fapi/v1/userTrades", params)
    except Exception:
        return []


def gate_my_trades_timerange(contract: str, start_s: int, end_s: int, limit: int = 1000) -> list:
    # Try seconds window first; if empty, retry with milliseconds.
    try:
        data = gate_private_get(
            "/futures/usdt/my_trades_timerange",
            {"contract": contract, "from": start_s, "to": end_s, "limit": str(limit)},
        )
        if isinstance(data, list) and data:
            return data
    except Exception:
        pass
    try:
        return gate_private_get(
            "/futures/usdt/my_trades_timerange",
            {"contract": contract, "from": start_s * 1000, "to": end_s * 1000, "limit": str(limit)},
        )
    except Exception:
        return []


def binance_user_trades_all(symbol: str, start_ms: int, end_ms: int, limit: int = 1000, max_pages: int = 2000) -> list:
    """Fetch all Binance futures user trades within [start_ms, end_ms] by time-based pagination.
    Binance supports fromId but to keep compatibility, we advance by last trade time.
    """
    all_trades: list = []
    cursor = int(start_ms)
    pages = 0
    while cursor <= end_ms and pages < max_pages:
        params = {"symbol": symbol, "startTime": cursor, "endTime": end_ms, "limit": limit}
        try:
            page = binance_signed_get("/fapi/v1/userTrades", params) or []
        except Exception:
            page = []
        if not page:
            break
        # ensure list and advance cursor by max time + 1ms
        try:
            max_t = max(int(float(x.get("time", 0))) for x in page) if page else cursor
        except Exception:
            max_t = cursor
        all_trades.extend(page)
        pages += 1
        next_cursor = max_t + 1
        if next_cursor <= cursor:
            next_cursor = cursor + 1
        cursor = next_cursor
        # safety: if page smaller than limit, likely finished
        if len(page) < limit and cursor > max_t:
            # still move cursor to avoid tight loop and continue one more iteration in case of equal timestamps
            if cursor > end_ms:
                break
    return all_trades


def _gate_trade_ts_s(t: dict) -> int:
    """Extract trade timestamp in seconds for Gate trade record (create_time/time may be sec or ms)."""
    v = t.get("create_time", t.get("time"))
    try:
        ts = float(v)
        if ts > 1e12:
            ts = ts / 1000.0
        return int(ts)
    except Exception:
        return 0


def gate_my_trades_timerange_all(contract: str, start_s: int, end_s: int, limit: int = 1000) -> list:
    """Fetch all Gate trades within [start_s, end_s] using iterative time cursors.
    Handles >limit results by advancing cursor to last trade time + 1s until window end.
    """
    all_trades: list = []
    cursor = int(start_s)
    pages = 0
    while cursor <= end_s and pages < 20000:
        page = gate_my_trades_timerange(contract, cursor, end_s, limit=limit) or []
        if not page:
            break
        all_trades.extend(page)
        try:
            max_ts = max(_gate_trade_ts_s(x) for x in page)
        except Exception:
            max_ts = cursor
        next_cursor = max_ts + 1
        if next_cursor <= cursor:
            next_cursor = cursor + 1
        cursor = next_cursor
        pages += 1
        # If fewer than limit, likely done; continue one more loop to confirm
        if len(page) < limit and cursor > max_ts:
            if cursor > end_s:
                break
    return all_trades


def gate_position_close_history_contract_all(contract: str, start_s: int, end_s: int, limit: int = 1000) -> list:
    """Fetch Gate position_close for a single contract across [start,end] with time cursor.
    Falls back between seconds/ms is handled in gate_private_get wrapper we use elsewhere.
    """
    records: list = []
    cursor = int(start_s)
    tries = 0
    while cursor <= end_s and tries < 10000:
        to_s = end_s
        try:
            page = (
                gate_private_get(
                    "/futures/usdt/position_close",
                    {"contract": contract, "from": cursor, "to": to_s, "limit": str(limit)},
                )
                or []
            )
        except Exception:
            page = []
        if not page:
            # try ms
            try:
                page = (
                    gate_private_get(
                        "/futures/usdt/position_close",
                        {"contract": contract, "from": cursor * 1000, "to": to_s * 1000, "limit": str(limit)},
                    )
                    or []
                )
            except Exception:
                page = []
        if not page:
            break
        records.extend(page)
        # advance by max close timestamp on page
        try:
            max_ts = 0
            for r in page:
                ts = _gate_record_ts_s(r)
                if ts and ts > max_ts:
                    max_ts = ts
        except Exception:
            max_ts = cursor
        next_cursor = (max_ts or cursor) + 1
        if next_cursor <= cursor:
            next_cursor = cursor + 1
        cursor = next_cursor
        tries += 1
        if len(page) < limit and cursor > max_ts:
            if cursor > end_s:
                break
    return records


def gate_position_close_history(contract: str, start_s: int, end_s: int) -> list:
    """Optional helper: sum realized pnl/fee from Gate close history if available."""
    try:
        return gate_private_get(
            "/futures/usdt/position_close",
            {"contract": contract, "from": start_s, "to": end_s},
        )
    except Exception:
        return []


def summarize_close_for_base(
    base: str,
    prev_entry_bn: float,
    prev_entry_gt: float,
    prev_bq: float,
    prev_gq: float,
    window_start_ms: int,
    window_end_ms: int,
) -> dict:
    """Compute close avg prices, fees, and profit for a fully closed hedge on this base."""
    # Map instruments
    bn_symbol = _base_to_bn_symbol(base)
    gt_contract = _base_to_gt_contract(base)

    # Time windows
    start_ms = window_start_ms
    end_ms = window_end_ms
    start_s = start_ms // 1000
    end_s = end_ms // 1000

    # Binance trades (closing short -> BUY)
    bn_trades = binance_user_trades(bn_symbol, start_ms, end_ms)
    bn_buy_qty = 0.0
    bn_buy_notional = 0.0
    bn_commission = 0.0
    bn_realized = 0.0
    for t in bn_trades:
        try:
            if (t.get("side") or "").upper() != "BUY":
                continue
            qty = float(t.get("qty", 0) or 0)
            price = float(t.get("price", 0) or 0)
            bn_buy_qty += qty
            bn_buy_notional += qty * price
            bn_commission += float(t.get("commission", 0) or 0)
            bn_realized += float(t.get("realizedPnl", 0) or 0)
        except Exception:
            continue
    bn_close_price = (bn_buy_notional / bn_buy_qty) if bn_buy_qty > 0 else 0.0

    # Gate trades (closing long -> SELL)
    gt_trades = gate_my_trades_timerange(gt_contract, start_s, end_s)
    gt_sell_qty_base = 0.0
    gt_sell_notional = 0.0
    gt_fee = 0.0
    mul = 0.0
    try:
        mul = get_gate_quanto_multiplier(gt_contract)
    except Exception:
        mul = 0.0
    for t in gt_trades:
        try:
            # Some responses use 'side', some may encode via size sign. Prefer side.
            side = (t.get("side") or "").lower()
            size = float(t.get("size", 0) or 0)  # in contracts
            price = float(t.get("price", 0) or 0)
            fee = float(t.get("fee", 0) or 0)
            # Treat negative size as sell if side missing
            is_sell = side == "sell" or (side == "" and size < 0)
            if not is_sell:
                continue
            base_qty = abs(size) * (mul or 1.0)  # convert to base units
            gt_sell_qty_base += base_qty
            gt_sell_notional += base_qty * price
            gt_fee += float(fee or 0)
        except Exception:
            continue
    gt_close_price = (gt_sell_notional / gt_sell_qty_base) if gt_sell_qty_base > 0 else 0.0

    # Determine trade size in base units from previous snapshot (use smaller of two to be safe)
    size_base = min(abs(prev_bq), abs(prev_gq))

    # Spreads
    open_spread = (prev_entry_bn / prev_entry_gt - 1.0) * 100.0 if (prev_entry_bn > 0 and prev_entry_gt > 0) else 0.0
    close_spread = (
        (bn_close_price / gt_close_price - 1.0) * 100.0 if (bn_close_price > 0 and gt_close_price > 0) else 0.0
    )

    # Gross PnL via price diff approximation (exclude fees)
    gross_pnl = (gt_close_price - prev_entry_gt) * size_base + (prev_entry_bn - bn_close_price) * size_base

    # Fees
    total_fee = bn_commission + gt_fee

    # Net PnL
    net_pnl = gross_pnl - total_fee

    return {
        "base": base,
        "size_base": size_base,
        # open side (from prev snapshot)
        "bn_open_price": float(prev_entry_bn or 0),
        "gt_open_price": float(prev_entry_gt or 0),
        "open_spread_pct": open_spread,
        # close side (from trades)
        "close_spread_pct": close_spread,
        "bn_close_price": bn_close_price,
        "gt_close_price": gt_close_price,
        # fees & pnl
        "bn_commission": bn_commission,
        "gt_fee": gt_fee,
        "gross_pnl": gross_pnl,
        "net_pnl": net_pnl,
    }


def summarize_partial_close_for_base(
    base: str,
    prev_entry_bn: float,
    prev_entry_gt: float,
    prev_bq: float,
    prev_gq: float,
    curr_bq: float,
    curr_gq: float,
    window_start_ms: int,
    window_end_ms: int,
) -> dict | None:
    """
    针对“部分减仓”计算本次平仓均价、手续费、近似收益。
    仅统计窗口内(最近一次检测到现在)的平仓成交（BN: BUY 关空；GT: SELL 平多），
    并按关闭的数量 size_base 进行加权汇总。
    返回 dict（与 summarize_close_for_base 字段一致）或 None（未能确定有效成交）。
    """
    try:
        # 关仓数量（基于基础币数量）
        closed_bn = max(0.0, abs(prev_bq) - abs(curr_bq))
        closed_gt = max(0.0, abs(prev_gq) - abs(curr_gq))
        size_base = min(closed_bn, closed_gt)
        if size_base <= 1e-12:
            return None

        bn_symbol = _base_to_bn_symbol(base)
        gt_contract = _base_to_gt_contract(base)

        start_ms = int(window_start_ms)
        end_ms = int(window_end_ms)
        start_s = start_ms // 1000
        end_s = end_ms // 1000

        # Gate SELL trades -> close long
        mul = 1.0
        try:
            mul = get_gate_quanto_multiplier(gt_contract) or 1.0
        except Exception:
            mul = 1.0
        gt_trades_close = gate_my_trades_timerange(gt_contract, start_s, end_s) or []
        try:
            gt_trades_close = sorted(
                gt_trades_close,
                key=lambda x: float(x.get("create_time", x.get("time", 0)) or 0),
            )
        except Exception:
            pass
        remaining = size_base
        gt_close_notional = 0.0
        gt_close_fee = 0.0
        taken_base = 0.0
        for t in gt_trades_close:
            try:
                side = (t.get("side") or "").lower()
                size = float(t.get("size", 0) or 0)
                price = float(t.get("price", 0) or 0)
                fee = float(t.get("fee", 0) or 0)
                is_sell = side == "sell" or (side == "" and size < 0)
                if not is_sell or remaining <= 0:
                    continue
                base_qty_total = abs(size) * mul
                if base_qty_total <= 0:
                    continue
                take = min(remaining, base_qty_total)
                weight = take / base_qty_total
                gt_close_notional += take * price
                gt_close_fee += fee * weight  # 费率保留符号（maker 返利为负）
                remaining -= take
                taken_base += take
            except Exception:
                continue
        gt_close_price = (gt_close_notional / taken_base) if taken_base > 0 else 0.0

        # Binance BUY trades -> close short
        bn_trades_close = binance_user_trades(bn_symbol, start_ms, end_ms) or []
        try:
            bn_trades_close = sorted(bn_trades_close, key=lambda x: float(x.get("time", 0) or 0))
        except Exception:
            pass
        remaining = size_base
        bn_close_notional = 0.0
        bn_close_qty = 0.0
        bn_close_commission = 0.0
        for t in bn_trades_close:
            try:
                if (t.get("side") or "").upper() != "BUY" or remaining <= 0:
                    continue
                qty = float(t.get("qty", 0) or 0)
                price = float(t.get("price", 0) or 0)
                commission = float(t.get("commission", 0) or 0)
                if qty <= 0:
                    continue
                take = min(remaining, qty)
                weight = take / qty
                bn_close_qty += take
                bn_close_notional += take * price
                bn_close_commission += commission * weight
                remaining -= take
            except Exception:
                continue
        bn_close_price = (bn_close_notional / bn_close_qty) if bn_close_qty > 0 else 0.0

        # Spreads & PnL（以上次快照的开仓均价为基准近似）
        open_spread = (
            (prev_entry_bn / prev_entry_gt - 1.0) * 100.0 if (prev_entry_bn > 0 and prev_entry_gt > 0) else 0.0
        )
        close_spread = (
            (bn_close_price / gt_close_price - 1.0) * 100.0 if (bn_close_price > 0 and gt_close_price > 0) else 0.0
        )
        gross_pnl = (gt_close_price - prev_entry_gt) * size_base + (prev_entry_bn - bn_close_price) * size_base
        total_fee = bn_close_commission + gt_close_fee
        net_pnl = gross_pnl - total_fee

        return {
            "base": base,
            "size_base": size_base,
            # open side (from prev snapshot)
            "bn_open_price": float(prev_entry_bn or 0),
            "gt_open_price": float(prev_entry_gt or 0),
            "open_spread_pct": open_spread,
            # close side (from trades window)
            "close_spread_pct": close_spread,
            "bn_close_price": bn_close_price,
            "gt_close_price": gt_close_price,
            # fees & pnl
            "bn_commission": bn_close_commission,
            "gt_fee": gt_close_fee,
            "gross_pnl": gross_pnl,
            "net_pnl": net_pnl,
        }
    except Exception:
        return None


def detect_closures_and_build_msg(
    prev_snap: tuple, curr_snap: tuple, window_start_ms: int, window_end_ms: int
) -> str | None:
    prev = _snap_to_map(prev_snap) if prev_snap else {}
    curr = _snap_to_map(curr_snap) if curr_snap else {}

    closures: List[dict] = []
    partials: List[dict] = []
    eps = 1e-9

    for b, (pbq, pbp, pgq, pgp) in prev.items():
        # previously hedged (BN short -> pbq < 0, GT long -> pgq > 0)
        if not (pbq < -eps and pgq > eps):
            continue
        cbq, _, cgq, _ = curr.get(b, (0.0, 0.0, 0.0, 0.0))

        # 1) 全部平仓：两边都接近 0
        if abs(cbq) <= eps and abs(cgq) <= eps:
            summary = summarize_close_for_base(b, pbp, pgp, pbq, pgq, window_start_ms, window_end_ms)
            if summary:
                closures.append(summary)
            continue

        # 2) 部分减仓：两边仍保持对冲方向（BN<0, GT>0），且绝对值减少
        if (cbq < -eps and cgq > eps) and (abs(cbq) + eps < abs(pbq) or abs(cgq) + eps < abs(pgq)):
            psum = summarize_partial_close_for_base(b, pbp, pgp, pbq, pgq, cbq, cgq, window_start_ms, window_end_ms)
            if psum:
                partials.append(psum)

    if not closures and not partials:
        return None

    lines: list[str] = []
    if closures:
        lines.append("== 平仓提醒 ==")
        for s in closures:
            lines.append(
                f"{s['base']}: 数量 {s['size_base']:.6f}\n"
                f"开仓: BN 卖均价 {s['bn_open_price']:.6f}  GT 买均价 {s['gt_open_price']:.6f}  价差 {s['open_spread_pct']:+.3f}%\n"
                f"平仓: BN 买均价 {s['bn_close_price']:.6f}  GT 卖均价 {s['gt_close_price']:.6f}  价差 {s['close_spread_pct']:+.3f}%\n"
                f"手续费: BN {s['bn_commission']:.6f} + GT {s['gt_fee']:.6f} = {s['bn_commission']+s['gt_fee']:.6f} USDT\n"
                f"毛利: {s['gross_pnl']:+.6f} USDT  净利(扣费): {s['net_pnl']:+.6f} USDT\n"
            )
    if partials:
        lines.append("== 部分平仓提醒 ==")
        for s in partials:
            lines.append(
                f"{s['base']}: 本次减仓 {s['size_base']:.6f}\n"
                f"开仓参考: BN 卖均价 {s['bn_open_price']:.6f}  GT 买均价 {s['gt_open_price']:.6f}  价差 {s['open_spread_pct']:+.3f}%\n"
                f"本次平仓: BN 买均价 {s['bn_close_price']:.6f}  GT 卖均价 {s['gt_close_price']:.6f}  价差 {s['close_spread_pct']:+.3f}%\n"
                f"手续费: BN {s['bn_commission']:.6f} + GT {s['gt_fee']:.6f} = {s['bn_commission']+s['gt_fee']:.6f} USDT\n"
                f"净利(近似): {s['net_pnl']:+.6f} USDT\n"
            )
    return "\n".join(lines)


# tolerant ts parser for Gate records (sec/ms)
def _gate_record_ts_s(rec: dict) -> int | None:
    for k in ("time", "finish_time", "close_time", "ctime", "create_time"):
        v = rec.get(k)
        if v is None:
            continue
        try:
            t = float(v)
            if t > 1e12:
                t = t / 1000.0
            return int(t)
        except Exception:
            continue
    return None


def gate_position_close_recent(limit: int = 500) -> list:
    try:
        return gate_private_get("/futures/usdt/position_close", {"limit": str(limit)})
    except Exception:
        return []


# ---------- Recent closures summary (on-demand) ----------
import re


def gate_position_close_history_all(start_s: int, end_s: int, limit: int = 1000) -> list:
    """Fetch Gate position close history across all contracts within [start,end].
    Some environments require milliseconds. If seconds returns empty, retry with ms.
    """
    # try seconds
    try:
        data = gate_private_get(
            "/futures/usdt/position_close",
            {"from": start_s, "to": end_s, "limit": str(limit)},
        )
        if isinstance(data, list) and data:
            return data
    except Exception:
        pass
    # retry with milliseconds
    try:
        return gate_private_get(
            "/futures/usdt/position_close",
            {"from": start_s * 1000, "to": end_s * 1000, "limit": str(limit)},
        )
    except Exception:
        return []


def build_recent_closures_msg(window_minutes: int = 60) -> str:
    """
    Aggregate recent closures within the past window_minutes and build a summary message.
    Uses Gate position_close to get precise close times, then matches corresponding Binance trades.
    """
    now_ms = int(time.time() * 1000)
    start_ms = now_ms - max(1, window_minutes) * 60 * 1000
    start_s = start_ms // 1000
    end_ms = now_ms
    end_s = now_ms // 1000

    records = gate_position_close_history_all(start_s, end_s, limit=1000)
    # fallback: loop each contract if all-contract query not supported
    if not records:
        try:
            contracts = get_gate_all_contracts()
        except Exception:
            contracts = []
        for c in contracts:
            rs = gate_position_close_history(c, start_s, end_s)
            if rs:
                if not isinstance(records, list):
                    records = []
                records.extend(rs)
    if not records:
        # fallback: fetch recent without time range and filter locally
        recent = gate_position_close_recent(limit=500)
        if recent:
            filtered = []
            for r in recent:
                ts = _gate_record_ts_s(r)
                if ts is None:
                    continue
                if start_s <= ts <= end_s:
                    filtered.append(r)
            records = filtered
    if not records:
        return f"最近{window_minutes}分钟无平仓记录"

    # Process each Gate close record individually to get precise timing
    lines = [f"== 最近{window_minutes}分钟平仓 =="]
    for r in records:
        try:
            contract = r.get("contract") or r.get("symbol") or ""
            if not contract:
                continue
            base = norm_base_from_gate_contract(contract)

            # close time and size (base units)
            close_ts_s = _gate_record_ts_s(r) or end_s
            close_ms = close_ts_s * 1000
            # size in contracts -> base
            size_contracts = None
            for k in ("close_size", "accum_size", "size"):
                v = r.get(k)
                if v is not None:
                    try:
                        size_contracts = float(v or 0)
                        break
                    except Exception:
                        pass
            if not size_contracts:
                continue
            mul = 1.0
            try:
                mul = get_gate_quanto_multiplier(contract) or 1.0
            except Exception:
                mul = 1.0
            size_base = abs(size_contracts) * mul
            if size_base <= 0:
                continue

            # ---- Gate close avg price & fee (SELL to close long)
            # Always reconstruct from trades to match the exchange "Closed Vol" precisely.
            # Keep the record values only as a fallback if no trades are found in window.
            gt_close_price_record = 0.0
            for k in ("avg_close_price", "close_price", "price"):
                if r.get(k) is not None:
                    try:
                        gt_close_price_record = float(r.get(k) or 0)
                        break
                    except Exception:
                        pass
            gt_close_fee_record = 0.0
            for k in ("pnl_fee", "fee"):
                if r.get(k) is not None:
                    try:
                        gt_close_fee_record = float(r.get(k) or 0)
                        break
                    except Exception:
                        pass

            # narrow window around close time to fetch SELL trades and fill
            gt_trades_close = gate_my_trades_timerange(contract, max(0, close_ts_s - 1800), close_ts_s + 600)
            # prefer most recent trades first to match the actual close event
            try:
                gt_trades_close = sorted(
                    gt_trades_close,
                    key=lambda x: float(x.get("create_time", x.get("time", 0))),
                    reverse=True,
                )
            except Exception:
                pass
            remaining = size_base
            notional = 0.0
            fee_sum = 0.0
            for t in gt_trades_close:
                try:
                    side = (t.get("side") or "").lower()
                    size = float(t.get("size", 0) or 0)
                    price = float(t.get("price", 0) or 0)
                    fee = float(t.get("fee", 0) or 0)
                    is_sell = side == "sell" or (side == "" and size < 0)
                    if not is_sell or remaining <= 0:
                        continue
                    base_qty_total = abs(size) * mul
                    if base_qty_total <= 0:
                        continue
                    take = min(remaining, base_qty_total)
                    weight = take / base_qty_total
                    notional += take * price
                    # preserve sign so maker rebates (negative) reduce fees
                    fee_sum += fee * weight
                    remaining -= take
                except Exception:
                    continue
            if notional > 0 and (size_base - remaining) > 0:
                gt_close_price = notional / (size_base - remaining)
                gt_close_fee = fee_sum
            else:
                gt_close_price = gt_close_price_record
                gt_close_fee = gt_close_fee_record

            # ---- Binance close avg (BUY) & commission around close time
            bn_symbol = _base_to_bn_symbol(base)
            bn_trades_close = binance_user_trades(
                bn_symbol, max(start_ms, close_ms - 1800_000), min(end_ms, close_ms + 600_000)
            )
            try:
                bn_trades_close = sorted(bn_trades_close, key=lambda x: float(x.get("time", 0)))
            except Exception:
                pass
            remaining = size_base
            bn_close_notional = 0.0
            bn_close_qty = 0.0
            bn_close_commission = 0.0
            for t in bn_trades_close:
                try:
                    if (t.get("side") or "").upper() != "BUY" or remaining <= 0:
                        continue
                    qty = float(t.get("qty", 0) or 0)
                    price = float(t.get("price", 0) or 0)
                    commission = float(t.get("commission", 0) or 0)
                    if qty <= 0:
                        continue
                    take = min(remaining, qty)
                    weight = take / qty
                    bn_close_qty += take
                    bn_close_notional += take * price
                    bn_close_commission += commission * weight
                    remaining -= take
                except Exception:
                    continue
            bn_close_price = (bn_close_notional / bn_close_qty) if bn_close_qty > 0 else 0.0

            # ---- OPEN side: use most recent prior trades up to size_base
            # Gate BUY to open long (within 24h lookback before close)
            gt_open_trades = gate_my_trades_timerange(contract, max(0, close_ts_s - 24 * 3600), max(0, close_ts_s - 1))
            # sort by time desc to take the nearest buys
            try:
                gt_open_trades = sorted(
                    gt_open_trades, key=lambda x: float(x.get("create_time", x.get("time", 0))), reverse=True
                )
            except Exception:
                pass
            remaining = size_base
            gt_open_notional = 0.0
            gt_open_qty = 0.0
            gt_open_fee = 0.0
            for t in gt_open_trades:
                try:
                    side = (t.get("side") or "").lower()
                    size = float(t.get("size", 0) or 0)
                    price = float(t.get("price", 0) or 0)
                    fee = float(t.get("fee", 0) or 0)
                    is_buy = side == "buy" or (side == "" and size > 0)
                    if not is_buy or remaining <= 0:
                        continue
                    base_qty_total = abs(size) * mul
                    if base_qty_total <= 0:
                        continue
                    take = min(remaining, base_qty_total)
                    weight = take / base_qty_total
                    gt_open_qty += take
                    gt_open_notional += take * price
                    # preserve sign of fee/rebate
                    gt_open_fee += fee * weight
                    remaining -= take
                except Exception:
                    continue
            gt_open_price = (gt_open_notional / gt_open_qty) if gt_open_qty > 0 else 0.0

            # Binance SELL to open short (use prior SELLs before close)
            bn_open_trades = binance_user_trades(bn_symbol, max(0, close_ms - 24 * 3600_000), max(0, close_ms - 1))
            try:
                bn_open_trades = sorted(bn_open_trades, key=lambda x: float(x.get("time", 0)), reverse=True)
            except Exception:
                pass
            remaining = size_base
            bn_open_notional = 0.0
            bn_open_qty = 0.0
            bn_open_commission = 0.0
            for t in bn_open_trades:
                try:
                    if (t.get("side") or "").upper() != "SELL" or remaining <= 0:
                        continue
                    qty = float(t.get("qty", 0) or 0)
                    price = float(t.get("price", 0) or 0)
                    commission = float(t.get("commission", 0) or 0)
                    if qty <= 0:
                        continue
                    take = min(remaining, qty)
                    weight = take / qty
                    bn_open_qty += take
                    bn_open_notional += take * price
                    bn_open_commission += commission * weight
                    remaining -= take
                except Exception:
                    continue
            bn_open_price = (bn_open_notional / bn_open_qty) if bn_open_qty > 0 else 0.0

            # Spreads
            open_spread = (
                ((bn_open_price / gt_open_price - 1.0) * 100.0) if (bn_open_price > 0 and gt_open_price > 0) else 0.0
            )
            close_spread = (
                ((bn_close_price / gt_close_price - 1.0) * 100.0)
                if (bn_close_price > 0 and gt_close_price > 0)
                else 0.0
            )

            # PnL approx via prices - fees from trades
            gross_pnl = (gt_close_price - gt_open_price) * size_base + (bn_open_price - bn_close_price) * size_base
            fee_open = bn_open_commission + gt_open_fee
            fee_close = bn_close_commission + gt_close_fee
            fee_total = fee_open + fee_close
            net_pnl = gross_pnl - fee_total

            lines.append(
                f"{base}: 数量 {size_base:.6f}\n"
                f"开仓: BN 卖均价 {bn_open_price:.6f}  GT 买均价 {gt_open_price:.6f}  价差 {open_spread:+.3f}%\n"
                f"平仓: BN 买均价 {bn_close_price:.6f}  GT 卖均价 {gt_close_price:.6f}  价差 {close_spread:+.3f}%\n"
                f"手续费(开仓): BN {bn_open_commission:.6f} + GT {gt_open_fee:.6f} = {fee_open:.6f} USDT\n"
                f"手续费(平仓): BN {bn_close_commission:.6f} + GT {gt_close_fee:.6f} = {fee_close:.6f} USDT\n"
                f"手续费合计: {fee_total:.6f} USDT\n"
                f"净利(价格-手续费近似): {net_pnl:+.6f} USDT\n"
            )
        except Exception:
            continue

    return "\n".join(lines)


def build_trades_agg_msg(base: str, hours: int = 2) -> str:
    """Aggregate past N hours trades for a given base on both exchanges.
    Shows: trade count, volume (base units), notional (USDT), fees, and realized PnL.
    - Binance: from /fapi/v1/userTrades (uses realizedPnl, commission)
    - Gate:    from /futures/usdt/my_trades_timerange (fees),
               realized PnL aggregated from position_close within the same window
    """
    base = base.upper().strip()
    try:
        bn_symbol = _base_to_bn_symbol(base)
        gt_contract = _base_to_gt_contract(base)
    except Exception:
        return f"不支持的币种: {base}"

    now_ms = int(time.time() * 1000)
    start_ms = now_ms - max(1, hours) * 3600_000
    start_s = start_ms // 1000
    end_ms = now_ms
    end_s = now_ms // 1000

    # Binance aggregation (pagination)
    bn_trades = binance_user_trades_all(bn_symbol, start_ms, end_ms)
    bn_count = len(bn_trades or [])
    bn_vol = 0.0
    bn_notional = 0.0
    bn_fee = 0.0
    bn_pnl = 0.0
    for t in bn_trades or []:
        try:
            qty = float(t.get("qty", 0) or 0)
            price = float(t.get("price", 0) or 0)
            fee = float(t.get("commission", 0) or 0)
            pnl = float(t.get("realizedPnl", 0) or 0)
            bn_vol += abs(qty)
            bn_notional += abs(qty) * price
            bn_fee += fee
            bn_pnl += pnl
        except Exception:
            continue

    # Gate aggregation (trades, pagination)
    gt_trades = gate_my_trades_timerange_all(gt_contract, start_s, end_s)
    # quanto multiplier to convert contracts -> base units
    try:
        mul = get_gate_quanto_multiplier(gt_contract)
    except Exception:
        mul = 1.0
    gt_count = len(gt_trades or [])
    gt_vol = 0.0
    gt_notional = 0.0
    gt_fee = 0.0
    for t in gt_trades or []:
        try:
            size = float(t.get("size", 0) or 0)
            price = float(t.get("price", 0) or 0)
            fee = float(t.get("fee", 0) or 0)
            base_qty = abs(size) * (mul or 1.0)
            gt_vol += base_qty
            gt_notional += base_qty * price
            gt_fee += fee  # keep sign; maker rebate could be negative
        except Exception:
            continue

    # Gate realized PnL from position_close within window
    gt_pnl = 0.0
    try:
        closes = gate_position_close_history_contract_all(gt_contract, start_s, end_s)
    except Exception:
        closes = []
    if closes:
        for r in closes:
            try:
                if r.get("contract") and r.get("contract") != gt_contract:
                    continue
                pnl_pnl = float(r.get("pnl_pnl", 0) or 0)
                pnl_fee = float(r.get("pnl_fee", 0) or 0)
                pnl_fund = float(r.get("pnl_fund", 0) or 0)
                pnl = float(r.get("pnl", 0) or 0)
                if pnl_pnl != 0 or pnl_fee != 0 or pnl_fund != 0:
                    gt_pnl += pnl_pnl - pnl_fee - pnl_fund
                else:
                    gt_pnl += pnl
            except Exception:
                continue

    lines = [f"== {base} 过去{hours}小时成交汇总 =="]
    lines.append(
        f"BN: 笔数 {bn_count}  成交量 {bn_vol:.6f}  名义额 {bn_notional:.6f}  手续费 {bn_fee:.6f}  PnL {bn_pnl:+.6f} USDT"
    )
    lines.append(
        f"GT: 笔数 {gt_count}  成交量 {gt_vol:.6f}  名义额 {gt_notional:.6f}  手续费 {gt_fee:.6f}  PnL {gt_pnl:+.6f} USDT"
    )
    return "\n".join(lines)


# ---------- Funding/PNL/Fee summary (account-wide) ----------
from typing import Optional


def binance_income_get(income_type: str, start_ms: int, end_ms: int, limit: int = 1000) -> list:
    """Fetch Binance income records for a specific type within [start_ms, end_ms]."""
    params = {
        "incomeType": income_type,
        "startTime": int(start_ms),
        "endTime": int(end_ms),
        "limit": int(limit),
    }
    try:
        return binance_signed_get("/fapi/v1/income", params) or []
    except Exception:
        return []


def binance_income_timerange_all(
    income_type: str, start_ms: int, end_ms: int, limit: int = 1000, max_pages: int = 5000
) -> list:
    """Iteratively fetch all Binance income records of given type within [start_ms, end_ms]."""
    all_items: list = []
    cursor = int(start_ms)
    pages = 0
    while cursor <= end_ms and pages < max_pages:
        page = binance_income_get(income_type, cursor, end_ms, limit=limit) or []
        if not page:
            break
        all_items.extend(page)
        try:
            max_t = max(int(float(x.get("time", 0))) for x in page)
        except Exception:
            max_t = cursor
        next_cursor = max_t + 1
        if next_cursor <= cursor:
            next_cursor = cursor + 1
        cursor = next_cursor
        pages += 1
        if len(page) < limit and cursor > max_t:
            if cursor > end_ms:
                break
    return all_items


def _safe_float(x, default: float = 0.0) -> float:
    try:
        return float(x or 0)
    except Exception:
        return default


def gate_account_book_range(from_s: int, to_s: int, limit: int = 1000, offset: int = 0) -> list:
    """Fetch Gate futures account book entries in [from_s, to_s]. Try seconds then milliseconds."""
    try:
        data = gate_private_get(
            "/futures/usdt/account_book", {"from": int(from_s), "to": int(to_s), "limit": limit, "offset": offset}
        )
        if isinstance(data, list) and data:
            return data
    except Exception:
        pass
    try:
        return gate_private_get(
            "/futures/usdt/account_book", {"from": int(from_s) * 1000, "to": int(to_s) * 1000, "limit": str(limit)}
        )
    except Exception:
        return []


def gate_account_book_range_all(from_s: int, to_s: int, limit: int = 1000, max_pages: int = 20000) -> list:
    """Iteratively fetch Gate account book across time using cursor on time field.

    Gate account_book time resolution is seconds. Use integer-second cursor to avoid
    repeated pages when the underlying request coerces to integer seconds.
    """
    all_items: list = []
    offset = 0
    print("gt from_s:", from_s, " to_s:", to_s)
    while True:
        print("limit:", limit, " offset:", offset)
        page = gate_account_book_range(from_s, to_s, limit=limit, offset=offset) or []
        if not page:
            break
        all_items.extend(page)
        offset += len(page)
        time.sleep(0.1)
    return all_items


def build_funding_details_msg(hours: int = 24) -> str:
    """Build account-wide funding details for past N hours for Binance and Gate.
    Report components separately: PnL (price), Funding fee, Commission.
    """
    now_ms = int(time.time() * 1000)
    start_ms = now_ms - max(1, hours) * 3600_000
    end_ms = now_ms
    start_s = start_ms // 1000
    end_s = end_ms // 1000

    # Binance via income endpoint
    bn_pnl_items = binance_income_timerange_all("REALIZED_PNL", start_ms, end_ms)
    bn_fund_items = binance_income_timerange_all("FUNDING_FEE", start_ms, end_ms)
    bn_comm_items = binance_income_timerange_all("COMMISSION", start_ms, end_ms)

    bn_pnl = sum(_safe_float(x.get("income", 0)) for x in bn_pnl_items)
    bn_funding = sum(_safe_float(x.get("income", 0)) for x in bn_fund_items)
    bn_commission = sum(_safe_float(x.get("income", 0)) for x in bn_comm_items)

    # Gate via account_book (preferred)
    gt_pnl = 0.0
    gt_funding = 0.0
    gt_commission = 0.0

    ab = gate_account_book_range_all(start_s, end_s, limit=1000)
    if ab:
        for r in ab:
            t = (r.get("type") or r.get("entry_type") or r.get("reason") or "").lower()
            txt = (r.get("text") or r.get("desc") or "").lower()
            change = _safe_float(r.get("change", r.get("amount", 0)))
            # Heuristics: classify types
            if ("fund" in t) or ("funding" in txt):
                gt_funding += change
            elif ("fee" in t) or ("commission" in t) or ("fee" in txt):
                gt_commission += change
            elif ("pnl" in t) or ("profit" in t) or ("close" in t):
                gt_pnl += change
            else:
                # ignore other account changes like transfers, point, interest, etc.
                pass
    else:
        # Fallback: approximate from position_close (pnl breakdown) within window
        try:
            closes = gate_position_close_history_all(start_s, end_s, limit=1000)
        except Exception:
            closes = []
        for r in closes or []:
            try:
                pnl_pnl = _safe_float(r.get("pnl_pnl", 0))
                pnl_fee = _safe_float(r.get("pnl_fee", 0))
                pnl_fund = _safe_float(r.get("pnl_fund", 0))
                if pnl_pnl != 0 or pnl_fee != 0 or pnl_fund != 0:
                    gt_pnl += pnl_pnl
                    gt_commission += -pnl_fee
                    gt_funding += -pnl_fund
                else:
                    # if only net pnl is available, we cannot split; attribute to pnl
                    gt_pnl += _safe_float(r.get("pnl", 0))
            except Exception:
                continue

    total_bn = bn_pnl + bn_funding + bn_commission
    total_gt = gt_pnl + gt_funding + gt_commission

    lines = [f"== 过去{hours}小时资金明细 =="]
    lines.append(
        f"Binance: PnL {bn_pnl:+.6f}  Funding {bn_funding:+.6f}  Commission {bn_commission:+.6f}  合计 {total_bn:+.6f} USDT"
    )
    lines.append(
        f"Gate:    PnL {gt_pnl:+.6f}  Funding {gt_funding:+.6f}  Commission {gt_commission:+.6f}  合计 {total_gt:+.6f} USDT"
    )
    lines.append(f"总计:   {total_bn + total_gt:+.6f} USDT")
    return "\n".join(lines)


def build_funding_details_by_currency_msg(hours: int = 24) -> str:
    """Per-currency funding breakdown for past N hours on Binance and Gate.
    Components per base: PnL (price), Funding fee, Commission.
    """
    now_ms = int(time.time() * 1000)
    start_ms = now_ms - max(1, hours) * 3600_000
    end_ms = now_ms
    start_s = start_ms // 1000
    end_s = end_ms // 1000

    # --- Binance: group income by symbol -> base ---
    bn_by_base: dict[str, dict[str, float]] = defaultdict(lambda: {"pnl": 0.0, "funding": 0.0, "commission": 0.0})
    try:
        bn_pnl_items = binance_income_timerange_all("REALIZED_PNL", start_ms, end_ms)
        bn_fund_items = binance_income_timerange_all("FUNDING_FEE", start_ms, end_ms)
        bn_comm_items = binance_income_timerange_all("COMMISSION", start_ms, end_ms)
    except Exception:
        bn_pnl_items, bn_fund_items, bn_comm_items = [], [], []

    def _bn_base(sym: str | None) -> str:
        s = (sym or "").upper()
        if s.endswith("USDT"):
            return s[:-4]
        return s

    for x in bn_pnl_items or []:
        base = _bn_base(x.get("symbol"))
        bn_by_base[base]["pnl"] += _safe_float(x.get("income", 0))
    for x in bn_fund_items or []:
        base = _bn_base(x.get("symbol"))
        bn_by_base[base]["funding"] += _safe_float(x.get("income", 0))
    for x in bn_comm_items or []:
        base = _bn_base(x.get("symbol"))
        bn_by_base[base]["commission"] += _safe_float(x.get("income", 0))

    # --- Gate: account_book grouped by base (preferred) ---
    gt_by_base: dict[str, dict[str, float]] = defaultdict(lambda: {"pnl": 0.0, "funding": 0.0, "commission": 0.0})

    def _gate_record_base(r: dict) -> str:
        contract = (r.get("contract") or r.get("symbol") or "").upper()
        if contract:
            if "_" in contract:
                return contract.split("_", 1)[0]
            if contract.endswith("USDT"):
                return contract[:-4]
        txtu = (r.get("text") or r.get("desc") or r.get("reason") or "").upper()
        m = re.search(r"([A-Z0-9]+)_USDT", txtu)
        if not m:
            m = re.search(r"([A-Z0-9]+)USDT", txtu)
        return m.group(1) if m else ""

    ab = gate_account_book_range_all(start_s, end_s, limit=1000)
    if ab:
        for r in ab:
            try:
                base = _gate_record_base(r)
                if not base:
                    continue
                t = (r.get("type") or r.get("entry_type") or r.get("reason") or "").lower()
                txt = (r.get("text") or r.get("desc") or "").lower()
                change = _safe_float(r.get("change", r.get("amount", 0)))
                if ("fund" in t) or ("funding" in txt):
                    gt_by_base[base]["funding"] += change
                elif ("fee" in t) or ("commission" in t) or ("fee" in txt):
                    gt_by_base[base]["commission"] += change
                elif ("pnl" in t) or ("profit" in t) or ("close" in t):
                    gt_by_base[base]["pnl"] += change
                else:
                    # ignore unrelated entries like transfers/interest/point/rebalance etc.
                    continue
            except Exception:
                continue
    else:
        # Fallback: use position_close breakdown if account_book is unavailable
        try:
            closes = gate_position_close_history_all(start_s, end_s, limit=1000)
        except Exception:
            closes = []
        for r in closes or []:
            try:
                contract = (r.get("contract") or "").upper()
                if not contract:
                    continue
                base = contract.split("_", 1)[0]
                pnl_pnl = _safe_float(r.get("pnl_pnl", 0))
                pnl_fee = _safe_float(r.get("pnl_fee", 0))
                pnl_fund = _safe_float(r.get("pnl_fund", 0))
                if pnl_pnl != 0 or pnl_fee != 0 or pnl_fund != 0:
                    gt_by_base[base]["pnl"] += pnl_pnl
                    gt_by_base[base]["commission"] += -pnl_fee
                    gt_by_base[base]["funding"] += -pnl_fund
                else:
                    gt_by_base[base]["pnl"] += _safe_float(r.get("pnl", 0))
            except Exception:
                continue

    # --- Build message (combined across exchanges) ---
    combined: dict[str, dict[str, float]] = defaultdict(lambda: {"pnl": 0.0, "funding": 0.0, "commission": 0.0})
    for base, v in bn_by_base.items():
        if not base:
            continue
        combined[base]["pnl"] += v["pnl"]
        combined[base]["funding"] += v["funding"]
        combined[base]["commission"] += v["commission"]
    for base, v in gt_by_base.items():
        if not base:
            continue
        combined[base]["pnl"] += v["pnl"]
        combined[base]["funding"] += v["funding"]
        combined[base]["commission"] += v["commission"]

    lines: list[str] = [f"== 过去{hours}小时分币种资金明细（合并两所） =="]
    tot_pnl = 0.0
    tot_funding = 0.0
    tot_commission = 0.0
    for base in sorted(k for k in combined.keys() if k):
        v = combined[base]
        tot_pnl += v["pnl"]
        tot_funding += v["funding"]
        tot_commission += v["commission"]
        lines.append(f"{base}: PnL {v['pnl']:+.6f}  Funding {v['funding']:+.6f}  Commission {v['commission']:+.6f}")
    lines.append(f"总计: PnL {tot_pnl:+.6f}  Funding {tot_funding:+.6f}  Commission {tot_commission:+.6f} USDT")

    return "\n".join(lines)


if __name__ == "__main__":
    last_check_ms = int(time.time() * 1000) - 120_000
    while True:
        loop_start_ms = int(time.time() * 1000)
        try:
            # Fetch exposures for both exchanges
            try:
                binance_exp = get_binance_exposures()
            except Exception as e:
                binance_exp = {}
            try:
                gate_exp = get_gate_exposures()
            except Exception as e:
                gate_exp = {}

            # Build snapshot and check for changes
            snapshot = make_snapshot(binance_exp, gate_exp)
            changed = snapshot != _prev_snapshot

            if changed:
                # First, detect closures within this check window
                try:
                    close_msg = detect_closures_and_build_msg(_prev_snapshot, snapshot, last_check_ms, loop_start_ms)
                    if close_msg:
                        send_text(close_msg)
                except Exception:
                    pass

                # Then, send the usual snapshot summary
                bn_totals = get_binance_account_totals()
                gt_totals = get_gate_account_totals()
                msg = compare_and_print(binance_exp, gate_exp, ABS_TOL, bn_totals, gt_totals)
                send_text(msg)
                _prev_snapshot = snapshot
        except Exception:
            # Swallow any unexpected error to keep the loop running
            pass
        finally:
            last_check_ms = loop_start_ms
        sleep(30)


# ---------- Flatten unhedged exposures (close-only) ----------
import math

_bn_exch_info_cache: dict | None = None


def _bn_symbol_qty_step(symbol: str) -> float:
    global _bn_exch_info_cache
    try:
        if _bn_exch_info_cache is None:
            data = session.get(f"{BINANCE_BASE}/fapi/v1/exchangeInfo", timeout=10)
            data.raise_for_status()
            _bn_exch_info_cache = {s.get("symbol"): s for s in (data.json().get("symbols") or [])}
        s = (_bn_exch_info_cache or {}).get(symbol)
        if not s:
            return 0.0001
        step = 0.0001
        for f in s.get("filters", []):
            if f.get("filterType") in ("LOT_SIZE", "MARKET_LOT_SIZE"):
                ss = f.get("stepSize")
                try:
                    step = float(ss)
                except Exception:
                    pass
                break
        return step or 0.0001
    except Exception:
        return 0.0001


_gate_contract_cache: dict[str, dict] = {}


def _gate_contract_info(contract: str) -> dict:
    if contract in _gate_contract_cache:
        return _gate_contract_cache[contract]
    try:
        info = gate_public_get(f"/futures/usdt/contracts/{quote_plus(contract)}")
        if isinstance(info, dict):
            _gate_contract_cache[contract] = info
            return info
    except Exception:
        pass
    return {}


def _gate_size_increment(contract: str) -> float:
    try:
        info = _gate_contract_info(contract)
        for k in ("order_size_increment", "size_increment", "order_size"):
            v = info.get(k)
            if v is not None:
                try:
                    return float(v)
                except Exception:
                    continue
    except Exception:
        pass
    return 1.0


def _round_down(q: float, step: float) -> float:
    try:
        if step <= 0:
            return q
        return math.floor(q / step + 1e-12) * step
    except Exception:
        return q


def _base_to_gate_contract(base: str) -> str:
    return f"{base.upper()}_USDT"


def _bn_symbol_known(symbol: str) -> bool:
    global _bn_exch_info_cache
    try:
        if _bn_exch_info_cache is None:
            data = session.get(f"{BINANCE_BASE}/fapi/v1/exchangeInfo", timeout=10)
            data.raise_for_status()
            _bn_exch_info_cache = {s.get("symbol"): s for s in (data.json().get("symbols") or [])}
        return symbol in (_bn_exch_info_cache or {})
    except Exception:
        return True  # don't block if cannot fetch


_bn_dual_side_cache: bool | None = None


def _bn_is_dual_side() -> bool:
    """Return whether Binance futures account is in hedge (dual-side) mode.
    Default to False (one-way) on error to avoid sending positionSide inappropriately.
    """
    global _bn_dual_side_cache
    if _bn_dual_side_cache is not None:
        return _bn_dual_side_cache
    try:
        data = binance_signed_get("/fapi/v1/positionSide/dual")
        v = data.get("dualSidePosition") if isinstance(data, dict) else None
        if isinstance(v, bool):
            _bn_dual_side_cache = v
        elif isinstance(v, str):
            _bn_dual_side_cache = v.lower() == "true"
        else:
            _bn_dual_side_cache = False
    except Exception:
        _bn_dual_side_cache = False
    return _bn_dual_side_cache


def place_binance_reduce_only_market(symbol: str, qty_base: float, position_side: str) -> dict:
    if qty_base <= 0:
        return {"skipped": "zero_qty"}
    if not _bn_symbol_known(symbol):
        return {"skipped": "unknown_symbol"}
    step = _bn_symbol_qty_step(symbol)
    q = _round_down(qty_base, step)
    if q <= 0:
        return {"skipped": "qty_round_zero"}
    side = "BUY" if position_side.upper() == "SHORT" else "SELL"
    params = {
        "symbol": symbol,
        "side": side,
        "type": "MARKET",
        "quantity": f"{q:.8f}",
        "reduceOnly": "true",
        "newOrderRespType": "RESULT",
    }

    try:
        return binance_signed_post("/fapi/v1/order", params)
    except requests.HTTPError as e:
        try:
            return {"error": e.response.text}
        except Exception:
            return {"error": str(e)}
    except Exception as e:
        return {"error": str(e)}


def _gate_contract_known(contract: str) -> bool:
    info = _gate_contract_info(contract)
    return bool(info)


def place_gate_reduce_only_market(contract: str, qty_base: float, side: str) -> dict:
    # Gate needs size in contracts (signed, BUY>0, SELL<0)
    if qty_base <= 0:
        return {"skipped": "zero_qty"}
    if not _gate_contract_known(contract):
        return {"skipped": "unknown_contract"}
    try:
        mul = get_gate_quanto_multiplier(contract) or 1.0
    except Exception:
        mul = 1.0
    size_contracts = qty_base / max(mul, 1e-12)
    inc = _gate_size_increment(contract)
    size_contracts = _round_down(abs(size_contracts), inc)
    if size_contracts <= 0:
        return {"skipped": "qty_round_zero"}
    signed_size = size_contracts if side.upper() == "BUY" else -size_contracts
    # Gate 文档要求 size 为整数（合约张数），避免小数导致 400
    try:
        signed_size_int = int(signed_size)
    except Exception:
        signed_size_int = int(math.floor(signed_size))
    if signed_size_int == 0:
        return {"skipped": "qty_round_zero"}
    payload = {
        "contract": contract,
        "size": signed_size_int,
        "price": "0",
        "tif": "ioc",
        "reduce_only": True,
        # Gate 要求 text 以 `t-` 开头
        "text": f"t-reduce-{int(time.time()*1000)}",
    }
    try:
        return gate_private_post("/futures/usdt/orders", payload)
    except requests.HTTPError as e:
        try:
            return {"error": e.response.text}
        except Exception:
            return {"error": str(e)}
    except Exception as e:
        return {"error": str(e)}


def flatten_unhedged_positions() -> str:
    """Close only the net exposure with reduce-only market orders, operating on the larger side.
    - If BN and GT have opposite signs and nearly equal sizes -> skip (fully hedged).
    - If opposite signs but not equal: only reduce the net residual on the larger side (preserve overlap).
    - If same sign or only one side has exposure: operate only on the larger side (reduce it toward zero).
    Returns a human-readable summary of actions and results.
    """
    try:
        bn = get_binance_exposures()  # {base: (qty, entry)}
        gt = get_gate_exposures()  # {base: (qty, entry)}
        bases = sorted(set(bn) | set(gt))
        lines: list[str] = ["== 平掉未对冲仓位 (Reduce-Only, Net-Only) =="]
        any_action = False
        eps = ABS_TOL if isinstance(ABS_TOL, (int, float)) else 1e-8
        for base in bases:
            q_bn = float((bn.get(base) or (0.0, 0.0))[0])
            q_gt = float((gt.get(base) or (0.0, 0.0))[0])
            if abs(q_bn) <= eps and abs(q_gt) <= eps:
                continue
            # Opposite signs case
            print(f"q_bn: {q_bn} q_gt: {q_gt}")
            if q_bn * q_gt < 0:
                # Already fully hedged?
                if abs(q_bn + q_gt) <= eps:
                    continue
                net = q_bn + q_gt  # sign indicates which side is larger
                symbol = _base_to_bn_symbol(base)
                contract = _base_to_gate_contract(base)
                actions = []
                if net > 0 and abs(q_gt) > abs(q_bn):
                    # GT long residual dominates -> sell GT reduce-only amount = net
                    res_gt = place_gate_reduce_only_market(contract, abs(net), "SELL")
                    actions.append(("GT", "SELL", abs(net), res_gt))
                elif net > 0 and abs(q_bn) > abs(q_gt):
                    res_bn = place_binance_reduce_only_market(symbol, abs(net), "LONG")
                    actions.append(("BN", "SHORT", abs(net), res_bn))
                elif net < 0 and abs(q_gt) > abs(q_bn):
                    res_gt = place_gate_reduce_only_market(contract, abs(net), "BUY")
                    actions.append(("GT", "BUY", abs(net), res_gt))
                else:
                    # BN short residual dominates -> buy BN reduce-only with positionSide SHORT amount = |net|
                    res_bn = place_binance_reduce_only_market(symbol, abs(net), "SHORT")
                    actions.append(("BN", "LONG", abs(net), res_bn))
            else:
                # Same sign or only one side present: operate only on the larger side
                symbol = _base_to_bn_symbol(base)
                contract = _base_to_gate_contract(base)
                actions = []
                if abs(q_bn) >= abs(q_gt) and abs(q_bn) > eps:
                    ps = "SHORT" if q_bn < 0 else "LONG"
                    res_bn = place_binance_reduce_only_market(symbol, abs(q_bn), ps)
                    actions.append(("BN", ps, abs(q_bn), res_bn))
                elif abs(q_gt) > eps:
                    side_gt = "SELL" if q_gt > 0 else "BUY"
                    res_gt = place_gate_reduce_only_market(contract, abs(q_gt), side_gt)
                    actions.append(("GT", side_gt, abs(q_gt), res_gt))
            if actions:
                any_action = True
                lines.append(f"{base}:")
                for ex, side, qty, res in actions:
                    if isinstance(res, dict) and res.get("error"):
                        status = "ERR: " + str(res.get("error"))
                    elif isinstance(res, dict) and res.get("skipped"):
                        status = "SKIP: " + str(res.get("skipped"))
                    else:
                        status = "OK"
                    lines.append(f"  {ex} reduce {side} {qty:.8f} -> {status}")
        if not any_action:
            lines.append("无未对冲仓位或数量过小，无需平仓")
        return "\n".join(lines)
    except Exception as e:
        return f"平掉未对冲仓位失败: {e}"


def flatten_symbol_all_positions(base: str) -> str:
    """按指定币种在两所(币安/Gate)全部 reduce-only 市价平掉。
    - BN: q<0 认为是 SHORT -> BUY reduce-only；q>0 认为是 LONG -> SELL reduce-only
    - GT: q>0 为多 -> SELL reduce-only；q<0 为空 -> BUY reduce-only
    """
    try:
        base_u = (base or "").upper().strip()
        if not base_u.isalpha() or not (2 <= len(base_u) <= 12):
            return f"❌ 币种无效: {base}"
        bn = get_binance_exposures()
        gt = get_gate_exposures()
        q_bn = float((bn.get(base_u) or (0.0, 0.0))[0])
        q_gt = float((gt.get(base_u) or (0.0, 0.0))[0])
        eps = ABS_TOL if isinstance(ABS_TOL, (int, float)) else 1e-8
        symbol = _base_to_bn_symbol(base_u)
        contract = _base_to_gt_contract(base_u)
        lines: list[str] = [f"== 指定币种平仓 {base_u} (Reduce-Only 市价) =="]
        any_action = False
        # Binance
        if abs(q_bn) > eps:
            ps = "SHORT" if q_bn < 0 else "LONG"
            res_bn = place_binance_reduce_only_market(symbol, abs(q_bn), ps)
            any_action = True
            status = (
                ("ERR: " + str(res_bn.get("error")))
                if (isinstance(res_bn, dict) and res_bn.get("error"))
                else (
                    ("SKIP: " + str(res_bn.get("skipped")))
                    if (isinstance(res_bn, dict) and res_bn.get("skipped"))
                    else "OK"
                )
            )
            lines.append(f"BN reduce {ps} {abs(q_bn):.8f} -> {status}")
        else:
            lines.append("BN 无持仓或数量过小，跳过")
        # Gate
        if abs(q_gt) > eps:
            side_gt = "SELL" if q_gt > 0 else "BUY"
            res_gt = place_gate_reduce_only_market(contract, abs(q_gt), side_gt)
            any_action = True
            status = (
                ("ERR: " + str(res_gt.get("error")))
                if (isinstance(res_gt, dict) and res_gt.get("error"))
                else (
                    ("SKIP: " + str(res_gt.get("skipped")))
                    if (isinstance(res_gt, dict) and res_gt.get("skipped"))
                    else "OK"
                )
            )
            lines.append(f"GT reduce {side_gt} {abs(q_gt):.8f} -> {status}")
        else:
            lines.append("GT 无持仓或数量过小，跳过")
        if not any_action:
            lines.append("两所均无需平仓")
        return "\n".join(lines)
    except Exception as e:
        return f"平仓 {base} 失败: {e}"
