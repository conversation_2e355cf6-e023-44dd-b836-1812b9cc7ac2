use bn_maker_rs::{init_tracing, EnvConfig, WeComClient};
use std::io::{self, Read};
use tracing::{error, info};

#[tokio::main]
async fn main() {
    init_tracing().await;

    // Read content from CLI arg or stdin
    let args: Vec<String> = std::env::args().collect();
    let content = if args.len() > 1 {
        args[1..].join(" ")
    } else {
        let mut buf = String::new();
        io::stdin().read_to_string(&mut buf).ok();
        buf
    };

    if content.trim().is_empty() {
        eprintln!("Usage: bn_maker_notify [message...]  OR  echo 'msg' | bn_maker_notify");
        std::process::exit(2);
    }

    let cfg = match EnvConfig::from_env() {
        Ok(c) => c,
        Err(e) => {
            error!("config error: {e}");
            eprintln!("Missing env. Required: WECOM_CORP_ID, WECOM_CORP_SECRET, WECOM_AGENT_ID");
            std::process::exit(1);
        }
    };

    let client = WeComClient::new(cfg);

    // default to_user: @all, can override via WECOM_TO_USER
    let to_user = std::env::var("WECOM_TO_USER").unwrap_or_else(|_| "@all".to_string());

    match client.send_text(&to_user, content.trim()).await {
        Ok(()) => info!("sent"),
        Err(e) => {
            error!("send failed: {e}");
            std::process::exit(1);
        }
    }
}

