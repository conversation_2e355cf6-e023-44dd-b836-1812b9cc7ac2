use std::collections::HashMap;
use std::net::SocketAddr;
use std::sync::Arc;

use axum::{
    Json, Router,
    extract::{Query, State},
    routing::{get, post},
};
use parking_lot::RwLock;
use serde::{Deserialize, Serialize};
use tracing::{error, info, warn};

use bn_maker_rs::init_tracing;

use aes::Aes256;
use base64::{Engine as _, engine::general_purpose};
use cbc::cipher::{BlockDecryptMut, BlockEncryptMut, KeyIvInit, block_padding::Pkcs7};
use cbc::{Decryptor, Encryptor};
use hex::ToHex;
use quick_xml::Reader;
use quick_xml::events::Event;
use rand::{Rng, distributions::Alphanumeric};
use sha1::{Digest, Sha1};

#[derive(Default, Debug)]
struct Store {
    desired: String,
    last_report: serde_json::Value,
}

#[derive(<PERSON>lone)]
struct CryptoState {
    token: String,
    corp_id: String,
    key: [u8; 32],
    iv: [u8; 16],
}

#[derive(Clone)]
struct AppState {
    store: Arc<RwLock<Store>>,
    crypto: Arc<CryptoState>,
}

#[derive(Deserialize)]
struct DesiredReq {
    agent: Option<String>,
}
#[derive(Serialize)]
struct DesiredResp {
    target_state: String,
}
#[derive(Deserialize)]
struct ReportReq {
    agent: Option<String>,
    #[serde(flatten)]
    rest: serde_json::Value,
}
#[derive(Serialize)]
struct OkResp {
    ok: bool,
}

fn env_default(key: &str, default: &str) -> String {
    std::env::var(key).unwrap_or_else(|_| default.to_string())
}

fn load_crypto() -> CryptoState {
    let token = env_default("WECOM_TOKEN", "");
    let corp_id = env_default("WECOM_CORP_ID", "");
    let aes_key_b64 = env_default("WECOM_AES_KEY", "");
    let mut s = aes_key_b64.clone();
    while s.len() % 4 != 0 {
        s.push('=');
    }
    let key_bytes = general_purpose::STANDARD
        .decode(s.as_bytes())
        .unwrap_or_default();
    let mut key = [0u8; 32];
    let mut iv = [0u8; 16];
    if key_bytes.len() >= 32 {
        key.copy_from_slice(&key_bytes[0..32]);
    }
    iv.copy_from_slice(&key[0..16]);
    CryptoState {
        token,
        corp_id,
        key,
        iv,
    }
}

fn sha1_sig(token: &str, ts: &str, nonce: &str, enc: &str) -> String {
    let mut v = [token, ts, nonce, enc];
    v.sort();
    let joined = v.concat();
    let mut h = Sha1::new();
    h.update(joined.as_bytes());
    h.finalize().encode_hex_lower()
}

fn xml_text(xml: &str, tag: &str) -> Option<String> {
    let mut r = Reader::from_str(xml);
    r.trim_text(true);
    let mut buf = Vec::new();
    let mut cur = String::new();
    loop {
        match r.read_event_into(&mut buf) {
            Ok(Event::Start(e)) => cur = String::from_utf8_lossy(e.name().as_ref()).to_string(),
            Ok(Event::Text(t)) if cur == tag => return Some(t.unescape().ok()?.to_string()),
            Ok(Event::Eof) => break,
            Err(_) => break,
            _ => {}
        }
    }
    None
}

fn decrypt_wecom(crypto: &CryptoState, enc_b64: &str) -> Option<String> {
    let cipher = general_purpose::STANDARD.decode(enc_b64).ok()?;
    let dec = Decryptor::<Aes256>::new_from_slices(&crypto.key, &crypto.iv).ok()?;
    let mut data = cipher.clone();
    let plain = dec.decrypt_padded_vec_mut::<Pkcs7>(&mut data).ok()?;
    if plain.len() < 20 {
        return None;
    }
    let xml_len = u32::from_be_bytes(plain[16..20].try_into().ok()?) as usize;
    if 20 + xml_len > plain.len() {
        return None;
    }
    let xml = &plain[20..20 + xml_len];
    let corp = &plain[20 + xml_len..];
    if corp != crypto.corp_id.as_bytes() {
        return None;
    }
    String::from_utf8(xml.to_vec()).ok()
}

fn encrypt_wecom(crypto: &CryptoState, xml: &str) -> String {
    let rand16: String = rand::thread_rng()
        .sample_iter(&Alphanumeric)
        .take(16)
        .map(char::from)
        .collect();
    let mut bytes = Vec::new();
    bytes.extend_from_slice(rand16.as_bytes());
    let xml_b = xml.as_bytes();
    bytes.extend_from_slice(&(xml_b.len() as u32).to_be_bytes());
    bytes.extend_from_slice(xml_b);
    bytes.extend_from_slice(crypto.corp_id.as_bytes());
    let enc = Encryptor::<Aes256>::new_from_slices(&crypto.key, &crypto.iv).unwrap();
    let ciphertext = enc.encrypt_padded_vec_mut::<Pkcs7>(&bytes);
    general_purpose::STANDARD.encode(ciphertext)
}

async fn api_desired(State(app): State<AppState>, Json(_): Json<DesiredReq>) -> Json<DesiredResp> {
    let s = app.store.read();
    Json(DesiredResp {
        target_state: s.desired.clone(),
    })
}

async fn api_report(State(app): State<AppState>, Json(body): Json<ReportReq>) -> Json<OkResp> {
    let agent = body.agent.clone().unwrap_or_else(|| "unknown".into());
    {
        let mut s = app.store.write();
        s.last_report = body.rest.clone();
        info!("report agent={}", agent);
    }
    Json(OkResp { ok: true })
}

async fn wecom_verify(
    State(app): State<AppState>,
    Query(q): Query<HashMap<String, String>>,
) -> (axum::http::StatusCode, String) {
    let sig = q.get("msg_signature").cloned().unwrap_or_default();
    let ts = q.get("timestamp").cloned().unwrap_or_default();
    let nonce = q.get("nonce").cloned().unwrap_or_default();
    let echo = q.get("echostr").cloned().unwrap_or_default();
    let calc = sha1_sig(&app.crypto.token, &ts, &nonce, &echo);
    if calc != sig {
        return (axum::http::StatusCode::FORBIDDEN, String::new());
    }
    if let Some(plain) = decrypt_wecom(&app.crypto, &echo) {
        (axum::http::StatusCode::OK, plain)
    } else {
        (axum::http::StatusCode::FORBIDDEN, String::new())
    }
}

async fn wecom_recv(
    State(app): State<AppState>,
    Query(q): Query<HashMap<String, String>>,
    body: String,
) -> (axum::http::StatusCode, String) {
    let sig = q.get("msg_signature").cloned().unwrap_or_default();
    let ts = q
        .get("timestamp")
        .cloned()
        .unwrap_or_else(|| format!("{}", chrono::Utc::now().timestamp()));
    let nonce = q.get("nonce").cloned().unwrap_or_else(|| "nonce".into());

    // extract Encrypt from XML body
    let enc = xml_text(&body, "Encrypt").unwrap_or_default();
    let calc = sha1_sig(&app.crypto.token, &ts, &nonce, &enc);
    if calc != sig {
        return (axum::http::StatusCode::FORBIDDEN, String::new());
    }

    let Some(plain) = decrypt_wecom(&app.crypto, &enc) else {
        return (axum::http::StatusCode::FORBIDDEN, String::new());
    };

    // parse fields
    let to_user = xml_text(&plain, "ToUserName").unwrap_or_default();
    let from_user = xml_text(&plain, "FromUserName").unwrap_or_default();
    let _msg_type = xml_text(&plain, "MsgType").unwrap_or_else(|| "text".into());
    let _content = xml_text(&plain, "Content").unwrap_or_default();

    // TODO: route to command processor; for now fixed reply
    let reply_content = "服务已就绪 (Rust)";
    let reply_plain = format!(
        "<xml><ToUserName><![CDATA[{to}]]></ToUserName><FromUserName><![CDATA[{from_}]]></FromUserName><CreateTime>{ts}</CreateTime><MsgType><![CDATA[text]]></MsgType><Content><![CDATA[{c}]]></Content></xml>",
        to = from_user,
        from_ = to_user,
        ts = ts,
        c = reply_content
    );

    let enc_reply = encrypt_wecom(&app.crypto, &reply_plain);
    let sig_reply = sha1_sig(&app.crypto.token, &ts, &nonce, &enc_reply);
    let resp_xml = format!(
        "<xml><Encrypt><![CDATA[{e}]]></Encrypt><MsgSignature><![CDATA[{s}]]></MsgSignature><TimeStamp>{ts}</TimeStamp><Nonce><![CDATA[{n}]]></Nonce></xml>",
        e = enc_reply,
        s = sig_reply,
        ts = ts,
        n = nonce
    );
    (axum::http::StatusCode::OK, resp_xml)
}

#[tokio::main]
async fn main() {
    init_tracing().await;

    let app_state = AppState {
        store: Arc::new(RwLock::new(Store {
            desired: "stopped".into(),
            last_report: serde_json::json!({}),
        })),
        crypto: Arc::new(load_crypto()),
    };

    if app_state.crypto.token.is_empty() || app_state.crypto.corp_id.is_empty() {
        warn!("WECOM_TOKEN or WECOM_CORP_ID not set. Verify/recv will likely fail.");
    }

    let app = Router::new()
        .route("/api/desired/bn_maker", post(api_desired))
        .route("/api/report/bn_maker", post(api_report))
        .route(
            "/wecom/callback/bn_maker",
            get(wecom_verify).post(wecom_recv),
        )
        .with_state(app_state);

    let addr: SocketAddr = std::env::var("WECOM_SERVER_ADDR")
        .unwrap_or_else(|_| "0.0.0.0:5001".into())
        .parse()
        .expect("invalid addr");
    info!("starting bn_maker_wecom_server on {}", addr);
    axum::Server::bind(&addr)
        .serve(app.into_make_service())
        .await
        .unwrap_or_else(|e| error!("server error: {}", e));
}
