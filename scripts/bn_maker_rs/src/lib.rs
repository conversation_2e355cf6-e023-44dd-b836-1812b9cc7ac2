use std::time::{Duration, Instant};

use reqwest::Client;
use serde::{Deserialize, Serialize};
use thiserror::Error;
use tracing::{debug, info, warn};

#[derive(Error, Debug)]
pub enum Error {
    #[error("http error: {0}")]
    Http(#[from] reqwest::Error),
    #[error("serde error: {0}")]
    Serde(#[from] serde_json::Error),
    #[error("config error: {0}")]
    Config(String),
    #[error("other: {0}")]
    Other(String),
}

pub type Result<T> = std::result::Result<T, Error>;

// ----------------------
// Config via env vars
// ----------------------
#[derive(Clone, Debug)]
pub struct EnvConfig {
    pub wecom_corp_id: String,
    pub wecom_corp_secret: String,
    pub wecom_agent_id: String,
}

impl EnvConfig {
    pub fn from_env() -> Result<Self> {
        let wecom_corp_id = std::env::var("WECOM_CORP_ID").map_err(|_| Error::Config("WECOM_CORP_ID missing".into()))?;
        let wecom_corp_secret = std::env::var("WECOM_CORP_SECRET").map_err(|_| Error::Config("WECOM_CORP_SECRET missing".into()))?;
        let wecom_agent_id = std::env::var("WECOM_AGENT_ID").map_err(|_| Error::Config("WECOM_AGENT_ID missing".into()))?;
        Ok(Self { wecom_corp_id, wecom_corp_secret, wecom_agent_id })
    }
}

// ----------------------
// WeCom client (access_token cache + send text)
// ----------------------
#[derive(Clone)]
pub struct WeComClient {
    http: Client,
    corp_id: String,
    corp_secret: String,
    agent_id: String,
    token_cache: tokio::sync::RwLock<Option<(String, Instant)>>,
}

#[derive(Deserialize)]
struct TokenResp {
    access_token: String,
    expires_in: u64,
    #[allow(dead_code)] errcode: Option<i64>,
    #[allow(dead_code)] errmsg: Option<String>,
}

#[derive(Serialize)]
struct SendTextReq<'a> {
    touser: &'a str,
    agentid: &'a str,
    msgtype: &'a str,
    text: SendTextContent<'a>,
    duplicate_check_interval: u32,
}

#[derive(Serialize)]
struct SendTextContent<'a> {
    content: &'a str,
}

#[derive(Deserialize)]
struct SendResp {
    errcode: i64,
    errmsg: String,
}

impl WeComClient {
    pub fn new(cfg: EnvConfig) -> Self {
        Self {
            http: Client::builder().tcp_keepalive(Duration::from_secs(30)).build().unwrap(),
            corp_id: cfg.wecom_corp_id,
            corp_secret: cfg.wecom_corp_secret,
            agent_id: cfg.wecom_agent_id,
            token_cache: tokio::sync::RwLock::new(None),
        }
    }

    async fn access_token(&self) -> Result<String> {
        if let Some((token, exp)) = self.token_cache.read().await.clone() {
            if Instant::now() < exp {
                return Ok(token);
            }
        }
        let url = format!(
            "https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid={}&corpsecret={}",
            self.corp_id, self.corp_secret
        );
        let resp = self.http.get(&url).send().await?.error_for_status()?;
        let tr: TokenResp = resp.json().await?;
        let ttl = tr.expires_in.saturating_sub(60);
        let exp = Instant::now() + Duration::from_secs(ttl);
        {
            let mut w = self.token_cache.write().await;
            *w = Some((tr.access_token.clone(), exp));
        }
        Ok(tr.access_token)
    }

    pub async fn send_text(&self, to_user: &str, content: &str) -> Result<()> {
        let token = self.access_token().await?;
        let url = format!("https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token={}", token);
        let body = SendTextReq {
            touser: to_user,
            agentid: &self.agent_id,
            msgtype: "text",
            text: SendTextContent { content },
            duplicate_check_interval: 1800,
        };
        let r = self.http.post(url).json(&body).send().await?.error_for_status()?;
        let sr: SendResp = r.json().await?;
        if sr.errcode != 0 {
            warn!("wecom send_text err: {} {}", sr.errcode, sr.errmsg);
            return Err(Error::Other(format!("wecom send_text failed: {} {}", sr.errcode, sr.errmsg)));
        }
        info!("wecom message sent");
        Ok(())
    }
}

// ----------------------
// Minimal health helper
// ----------------------
pub async fn init_tracing() {
    let _ = tracing_subscriber::fmt()
        .with_env_filter(tracing_subscriber::EnvFilter::from_default_env())
        .with_target(false)
        .try_init();
    debug!("tracing initialized");
}

