[package]
name = "bn_maker_rs"
version = "0.1.0"
edition = "2021"

[dependencies]
# Async runtime and HTTP
tokio = { version = "1", features = ["rt-multi-thread", "macros", "time"] }
reqwest = { version = "0.11", features = [
    "json",
    "brotli",
    "gzip",
    "zstd",
    "rustls-tls",
] }
axum = { version = "0.7", features = ["json"] }

# Serde & utilities
serde = { version = "1", features = ["derive"] }
serde_json = "1"
thiserror = "1"
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter", "fmt"] }
chrono = { version = "0.4", features = ["clock"] }
parking_lot = "0.12"
base64 = "0.21"
sha1 = "0.10"
sha2 = "0.10"
hmac = "0.12"
urlencoding = "2"
quick-xml = { version = "0.31", features = ["serialize"] }
hex = "0.4"
aes = "0.8"
cbc = { version = "0.1", features = ["alloc"] }
rand = "0.8"

[lib]
name = "bn_maker_rs"
path = "src/lib.rs"

[[bin]]
name = "bn_maker_notify"
path = "src/bin/bn_maker_notify.rs"

[[bin]]
name = "bn_maker_wecom_server"
path = "src/bin/bn_maker_wecom_server.rs"
