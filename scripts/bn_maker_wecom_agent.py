#!/usr/bin/env python3
"""
WeCon Agent: Poll desired state from wecon.py and manage the trading binary (gate).

- Poll interval: 1s (configurable via POLL_INTERVAL)
- Four states: stopped, stopping, starting, started
- Start: spawn binary; after a grace period with liveness -> started
- Stop: send SIGINT (Ctrl-C). If not exited within STOP_TIMEOUT, send SIGTERM. Never send SIG<PERSON><PERSON><PERSON>.
- Report current state back to WeCon after transitions and on heartbeat.

Configuration (env vars):
  WECON_GET_URL     - URL to POST for desired state (default: http://127.0.0.1:8000/api/desired)
  WECON_REPORT_URL  - URL to POST for reporting state (default: http://127.0.0.1:8000/api/report)
  AGENT_ID          - Identifier of this agent (default: hostname)
  TRADER_BIN        - Path to trading binary (default: ./target/release/gate)
  TRADER_ARGS       - Optional extra args for the binary (string, shell-like, default: "")
  TRADER_CWD        - Optional working directory for the binary (default: None)
  POLL_INTERVAL     - Poll interval seconds (default: 1.0)
  START_GRACE       - Seconds to consider process healthy after start (default: 3)
  STOP_TIMEOUT      - Seconds to wait after SIGINT before escalating to SIGTERM (default: 10)
  EXTRA_HEADERS_JSON- Optional JSON string for extra HTTP headers (e.g., auth)
  REPORT_EVERY      - Heartbeat report cadence in seconds (default: 5)

Server contracts (flexible):
- Desired-state endpoint response is JSON and may contain one of these keys:
  {"target_state":"started|stopped"} or {"desired_state":"start|stop"} or {"state":true|false}
  The agent normalizes values to "started" or "stopped".
- Report endpoint expects JSON like:
  {"agent":"...","state":"started|stopped|starting|stopping","pid":1234,"uptime":1.23,"timestamp":"..."}
"""
from __future__ import annotations

import json
import logging
import os
import shlex
import signal
import subprocess
import sys
import time
from dataclasses import dataclass
from datetime import datetime, timezone
from typing import Any, Dict, Optional
from urllib import request, error


STATE_STOPPED = "stopped"
STATE_STOPPING = "stopping"
STATE_STARTING = "starting"
STATE_STARTED = "started"


@dataclass
class Config:
    get_url: str = os.getenv("WECON_GET_URL", "http://54.238.66.59:80/api/desired/bn_maker")
    report_url: str = os.getenv("WECON_REPORT_URL", "http://54.238.66.59:80/api/report/bn_maker")
    agent_id: str = os.getenv("AGENT_ID", "bn_gate")
    binary: str = os.getenv("TRADER_BIN", "/home/<USER>/bn")
    args: str = os.getenv("TRADER_ARGS", "")
    cwd: Optional[str] = os.getenv("TRADER_CWD")
    poll_interval: float = float(os.getenv("POLL_INTERVAL", "1.0"))
    start_grace: float = float(os.getenv("START_GRACE", "3"))
    stop_timeout: float = float(os.getenv("STOP_TIMEOUT", "10"))
    report_every: float = float(os.getenv("REPORT_EVERY", "5"))
    extra_headers_json: Optional[str] = os.getenv("EXTRA_HEADERS_JSON")

    @property
    def extra_headers(self) -> Dict[str, str]:
        if not self.extra_headers_json:
            return {}
        try:
            val = json.loads(self.extra_headers_json)
            if isinstance(val, dict):
                return {str(k): str(v) for k, v in val.items()}
        except Exception:
            pass
        return {}


def utc_now_iso() -> str:
    return datetime.now(timezone.utc).isoformat()


def setup_logging() -> None:
    level = os.getenv("LOG_LEVEL", "INFO").upper()
    logging.basicConfig(
        level=getattr(logging, level, logging.INFO),
        format="%(asctime)s %(levelname)s %(message)s",
    )


class TraderProcess:
    def __init__(self, cfg: Config) -> None:
        self.cfg = cfg
        self.proc: Optional[subprocess.Popen] = None
        self.started_at: Optional[float] = None

    def is_running(self) -> bool:
        return self.proc is not None and self.proc.poll() is None

    def uptime(self) -> Optional[float]:
        if self.started_at is None or not self.is_running():
            return None
        return max(0.0, time.time() - self.started_at)

    def start(self) -> bool:
        if self.is_running():
            logging.info("Process already running (pid=%s)", self.proc.pid)
            return True
        cmd = [self.cfg.binary] + shlex.split(self.cfg.args)
        logging.info("Starting trader: %s (cwd=%s)", " ".join(shlex.quote(c) for c in cmd), self.cfg.cwd)
        try:
            # Start in its own process group so we can signal the whole group
            self.proc = subprocess.Popen(
                cmd,
                cwd=self.cfg.cwd or None,
                stdout=None,
                stderr=None,
                stdin=None,
                preexec_fn=os.setsid if hasattr(os, "setsid") else None,
            )
            self.started_at = time.time()
        except FileNotFoundError:
            logging.exception("Binary not found: %s", self.cfg.binary)
            self.proc = None
            self.started_at = None
            return False
        except Exception:
            logging.exception("Failed to start trader")
            self.proc = None
            self.started_at = None
            return False

        # Grace period: ensure it stays up for start_grace seconds
        deadline = time.time() + self.cfg.start_grace
        while time.time() < deadline:
            if not self.is_running():
                rc = self.proc.returncode if self.proc else None
                logging.error("Trader exited during start grace period (rc=%s)", rc)
                self.proc = None
                self.started_at = None
                return False
            time.sleep(0.2)
        logging.info("Trader healthy after grace period (pid=%s)", self.proc.pid)
        return True

    def _signal_group(self, sig: int) -> None:
        if not self.proc:
            return
        try:
            if hasattr(os, "getpgid"):
                pgid = os.getpgid(self.proc.pid)
                os.killpg(pgid, sig)
            else:
                self.proc.send_signal(sig)
        except ProcessLookupError:
            pass
        except Exception:
            logging.exception("Error signaling process")

    def stop(self, timeout: float) -> bool:
        if not self.is_running():
            logging.info("Process not running; nothing to stop")
            return True
        logging.info("Stopping trader with SIGINT (pid=%s)", self.proc.pid)
        self._signal_group(signal.SIGINT)
        try:
            self.proc.wait(timeout=timeout)
            logging.info("Trader exited after SIGINT (rc=%s)", self.proc.returncode)
            return True
        except subprocess.TimeoutExpired:
            logging.warning("Trader did not exit in %.1fs after SIGINT; sending SIGTERM", timeout)
            self._signal_group(signal.SIGTERM)
            try:
                self.proc.wait(timeout=max(1.0, timeout / 2))
                logging.info("Trader exited after SIGTERM (rc=%s)")
                return True
            except subprocess.TimeoutExpired:
                logging.error("Trader still running after SIGTERM; leaving it to exit on its own (no SIGKILL)")
                return False
        finally:
            if self.proc and self.proc.poll() is not None:
                self.proc = None
                self.started_at = None


class Agent:
    def __init__(self, cfg: Config) -> None:
        self.cfg = cfg
        self.trader = TraderProcess(cfg)
        self.state: str = STATE_STOPPED
        self._last_report_at: float = 0.0

    def _post_json(self, url: str, payload: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        data = json.dumps(payload).encode("utf-8")
        headers = {"Content-Type": "application/json", **self.cfg.extra_headers}
        req = request.Request(url, data=data, headers=headers, method="POST")
        try:
            with request.urlopen(req, timeout=5) as resp:
                body = resp.read()
                if not body:
                    return None
                return json.loads(body.decode("utf-8"))
        except error.HTTPError as e:
            logging.error("HTTP %s on %s: %s", e.code, url, e.read().decode(errors="ignore"))
        except Exception:
            logging.exception("POST %s failed", url)
        return None

    def fetch_desired_state(self) -> Optional[str]:
        payload = {"agent": self.cfg.agent_id}
        resp = self._post_json(self.cfg.get_url, payload)
        if not resp:
            return None
        # Try common keys
        for key in ("target_state", "desired_state", "state"):
            if key in resp:
                val = resp[key]
                break
        else:
            logging.warning("Desired state response missing known keys: %s", resp)
            return None
        # Normalize
        if isinstance(val, bool):
            return STATE_STARTED if val else STATE_STOPPED
        sval = str(val).strip().lower()
        mapping = {
            "start": STATE_STARTED,
            "started": STATE_STARTED,
            "on": STATE_STARTED,
            "1": STATE_STARTED,
            "true": STATE_STARTED,
            "stop": STATE_STOPPED,
            "stopped": STATE_STOPPED,
            "off": STATE_STOPPED,
            "0": STATE_STOPPED,
            "false": STATE_STOPPED,
        }
        if sval in mapping:
            return mapping[sval]
        logging.warning("Unrecognized desired value: %r", val)
        return None

    def current_state(self) -> str:
        if self.trader.is_running():
            # If running and past grace, state is started; otherwise starting
            if self.trader.started_at and (time.time() - self.trader.started_at) >= self.cfg.start_grace:
                return STATE_STARTED
            return STATE_STARTING
        return STATE_STOPPED

    def report(self, reason: str = "heartbeat") -> None:
        state = self.current_state()
        payload: Dict[str, Any] = {
            "agent": self.cfg.agent_id,
            "state": state,
            "pid": self.trader.proc.pid if self.trader.proc and self.trader.is_running() else None,
            "uptime": self.trader.uptime(),
            "timestamp": utc_now_iso(),
            "reason": reason,
        }
        self._post_json(self.cfg.report_url, payload)
        self._last_report_at = time.time()

    def maybe_report_heartbeat(self) -> None:
        if (time.time() - self._last_report_at) >= self.cfg.report_every:
            self.report(reason="heartbeat")

    def run_once(self) -> None:
        desired = self.fetch_desired_state()
        current = self.current_state()

        if desired is None:
            logging.debug("Desired state unknown; maintaining current (%s)", current)
            self.maybe_report_heartbeat()
            return

        if desired == STATE_STARTED:
            if current == STATE_STOPPED:
                logging.info("Transition: stopped -> starting")
                self.state = STATE_STARTING
                ok = self.trader.start()
                self.report(reason="start" if ok else "start_failed")
            else:
                self.maybe_report_heartbeat()
        elif desired == STATE_STOPPED:
            if self.trader.is_running():
                logging.info("Transition: started/starting -> stopping")
                self.state = STATE_STOPPING
                ok = self.trader.stop(timeout=self.cfg.stop_timeout)
                # Report immediately when exit observed; else heartbeat will continue
                self.report(reason="stop" if ok else "stopping")
            else:
                self.maybe_report_heartbeat()
        else:
            logging.debug("Desired=%s not recognized; noop", desired)
            self.maybe_report_heartbeat()

    def run_forever(self) -> None:
        logging.info(
            "Agent %s started. Polling %s; reporting to %s", self.cfg.agent_id, self.cfg.get_url, self.cfg.report_url
        )

        def _handle(sig, frame):  # noqa: ARG001
            logging.info("Agent received signal %s; forwarding to trader and exiting", sig)
            # Best-effort graceful shutdown of child
            if self.trader.is_running():
                self.trader.stop(timeout=self.cfg.stop_timeout)
            sys.exit(0)

        signal.signal(signal.SIGINT, _handle)
        signal.signal(signal.SIGTERM, _handle)

        while True:
            start = time.time()
            try:
                self.run_once()
            except Exception:
                logging.exception("Unexpected error in main loop")
            # Sleep the remainder, maintaining ~1s cadence
            elapsed = time.time() - start
            time.sleep(max(0.0, self.cfg.poll_interval - elapsed))


def main() -> int:
    setup_logging()
    cfg = Config()
    # Early sanity log
    logging.info("Binary: %s args: %s cwd: %s", cfg.binary, cfg.args, cfg.cwd)

    agent = Agent(cfg)
    agent.run_forever()
    return 0


if __name__ == "__main__":
    raise SystemExit(main())
