#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Generate Binance Spot funding details for the past N hours:
- Trade fees (by commission asset)
- Realized PnL (FIFO per symbol; USDT-quoted spot pairs)
- Transfer records (universal transfers, deposits, withdrawals)

Environment:
  BINANCE_API_KEY, BINANCE_API_SECRET

Usage:
  python scripts/binance_spot_funding_report.py --hours 6 [--format markdown|csv] [--symbols BTC,ETH,...]

Notes:
- PnL is realized on SELL trades only and computed using FIFO cost basis from historical BUY fills.
  To get accurate cost basis, the script may fetch earlier trades before the window until enough
  inventory is found. A safety cap is applied to avoid excessive requests.
- Symbols auto-discovery: by default, we detect non-zero spot balances and build <ASSET>USDT symbols
  that exist on the exchange. You can override with --symbols.
"""
from __future__ import annotations
import os
import sys
import time
import hmac
import hashlib
import argparse
from typing import Dict, List, Tuple, Optional

import requests

BINANCE_KEY = os.getenv("BINANCE_API_KEY", "9HbITeDCxX614QhColLUiJimEa4MLhmCFYmRH6mhjCnY52L1HIOemqVNaXEhbFnX")
BINANCE_SEC = os.getenv("BINANCE_API_SECRET", "D0vcompTOn2qN8VfW6UeXEMXo3ubtpHBacsyhA9Vh7LVq0xrPedw5nypP4L5pSxo")
BINANCE_BASE = "https://api.binance.com"

session = requests.Session()
session.headers.update({"Accept": "application/json", "User-Agent": "spot-funding-report/1.0"})


def _signed_get(path: str, params: dict | None = None):
    params = params.copy() if params else {}
    params["timestamp"] = int(time.time() * 1000)
    qs = "&".join(f"{k}={params[k]}" for k in params)  # keep insertion order
    sig = hmac.new(BINANCE_SEC.encode(), qs.encode(), hashlib.sha256).hexdigest()
    url = f"{BINANCE_BASE}{path}?{qs}&signature={sig}"
    headers = {"X-MBX-APIKEY": BINANCE_KEY}
    r = session.get(url, headers=headers, timeout=20)
    r.raise_for_status()
    return r.json()


def _public_get(path: str, params: dict | None = None):
    params = params or {}
    url = f"{BINANCE_BASE}{path}"
    r = session.get(url, params=params, timeout=20)
    r.raise_for_status()
    return r.json()


# ---------- Helpers ----------


def exchange_info() -> dict:
    return _public_get("/api/v3/exchangeInfo")


def symbol_exists(symbol: str, ex_info: dict) -> bool:
    try:
        for s in ex_info.get("symbols", []):
            if s.get("symbol") == symbol and s.get("status") == "TRADING" and s.get("isSpotTradingAllowed"):
                return True
    except Exception:
        pass
    return False


def get_account_balances() -> Dict[str, float]:
    data = _signed_get("/api/v3/account")
    balances: Dict[str, float] = {}
    for b in data.get("balances", []):
        try:
            free = float(b.get("free", 0) or 0)
            locked = float(b.get("locked", 0) or 0)
            amt = free + locked
            if amt > 0:
                balances[b.get("asset")] = amt
        except Exception:
            continue
    return balances


def autodiscover_usdt_symbols(ex_info: dict) -> List[str]:
    """Build <ASSET>USDT from non-zero balances when such symbol exists for spot trading."""
    bals = get_account_balances()
    symbols: List[str] = []
    for asset in sorted(bals.keys()):
        if asset in ("USDT", "BUSD", "FDUSD", "USDC", "USD"):
            continue
        sym = f"{asset}USDT"
        if symbol_exists(sym, ex_info):
            symbols.append(sym)
    return symbols


# ---------- Trades & Fees ----------


def get_spot_trades(symbol: str, start_ms: int, end_ms: int, limit: int = 1000) -> List[dict]:
    """Fetch all my spot trades for a symbol within [start_ms, end_ms]."""
    all_trades: List[dict] = []
    cursor = int(start_ms)
    pages = 0
    while cursor <= end_ms and pages < 5000:
        params = {"symbol": symbol, "startTime": cursor, "endTime": end_ms, "limit": limit}
        try:
            page = _signed_get("/api/v3/myTrades", params) or []
        except Exception:
            page = []
        if not page:
            break
        all_trades.extend(page)
        try:
            max_t = max(int(float(x.get("time", 0))) for x in page) if page else cursor
        except Exception:
            max_t = cursor
        next_cursor = max_t + 1
        if next_cursor <= cursor:
            next_cursor = cursor + 1
        cursor = next_cursor
        pages += 1
        if len(page) < limit and cursor > max_t:
            if cursor > end_ms:
                break
    return all_trades


def get_trades_before(
    symbol: str, end_ms: int, need_qty: float, step_hours: int = 168, max_lookbacks: int = 30
) -> List[dict]:
    """Fetch BUY trades before end_ms until accumulated buy qty >= need_qty or cap reached."""
    collected: List[dict] = []
    remaining = max(0.0, need_qty)
    lookbacks = 0
    cursor_end = end_ms
    while remaining > 1e-18 and lookbacks < max_lookbacks:
        start = max(0, cursor_end - step_hours * 3600_000)
        trades = get_spot_trades(symbol, start, cursor_end - 1)
        # reverse chronological to take nearest buys first
        trades.sort(key=lambda x: float(x.get("time", 0)), reverse=True)
        for t in trades:
            if (t.get("isBuyer") is True) or (str(t.get("isBuyer")).lower() == "true"):
                qty = abs(float(t.get("qty", 0) or 0))
                if qty <= 0:
                    continue
                collected.append(t)
                remaining -= qty
                if remaining <= 1e-18:
                    break
        cursor_end = start
        lookbacks += 1
        if start == 0:
            break
    # return in chronological order
    collected.sort(key=lambda x: float(x.get("time", 0)))
    return collected


def fifo_realized_pnl(symbol: str, window_trades: List[dict], start_ms: int) -> Tuple[float, Dict[str, float]]:
    """Compute realized PnL for SELL trades in window using FIFO from prior BUYs.
    Returns: (realized_pnl_in_quote, fees_by_asset(dict))
    Assumes symbol is quoted in USDT (or a quote with stable value like USDx)."""
    # Separate BUY/SELL in window
    buys_in_window: List[Tuple[float, float]] = []  # [(qty, price)]
    sells_in_window: List[Tuple[float, float]] = []
    fees: Dict[str, float] = {}
    for t in sorted(window_trades, key=lambda x: float(x.get("time", 0))):
        try:
            qty = float(t.get("qty", 0) or 0)
            price = float(t.get("price", 0) or 0)
            commission = float(t.get("commission", 0) or 0)
            commission_asset = t.get("commissionAsset") or ""
            fees[commission_asset] = fees.get(commission_asset, 0.0) + commission
            if (t.get("isBuyer") is True) or (str(t.get("isBuyer")).lower() == "true"):
                buys_in_window.append((qty, price))
            else:
                sells_in_window.append((abs(qty), price))
        except Exception:
            continue

    # Need prior BUY inventory for SELLs at window start
    sell_qty_need = sum(q for q, _ in sells_in_window)
    prior_buys = get_trades_before(symbol, start_ms, sell_qty_need)

    # Build FIFO queue: prior buys (chronological) + buys in window
    fifo: List[Tuple[float, float]] = []
    for t in prior_buys:
        try:
            qty = float(t.get("qty", 0) or 0)
            price = float(t.get("price", 0) or 0)
            fifo.append((qty, price))
        except Exception:
            continue
    fifo.extend(buys_in_window)

    realized = 0.0
    # Process sells
    for sell_qty, sell_price in sells_in_window:
        remaining = sell_qty
        while remaining > 1e-18 and fifo:
            buy_qty, buy_price = fifo[0]
            take = min(remaining, buy_qty)
            realized += (sell_price - buy_price) * take
            new_qty = buy_qty - take
            if new_qty <= 1e-18:
                fifo.pop(0)
            else:
                fifo[0] = (new_qty, buy_price)
            remaining -= take
        # If remaining > 0, we lacked historical buys -> cannot price; ignore leftover
    return realized, fees


# ---------- Transfers ----------


def get_universal_transfers(start_ms: int, end_ms: int) -> List[dict]:
    # GET /sapi/v1/asset/transfer?startTime&endTime
    try:
        data = _signed_get("/sapi/v1/asset/transfer", {"startTime": start_ms, "endTime": end_ms})
        rows = data.get("rows", []) if isinstance(data, dict) else []
        return rows
    except Exception:
        return []


def get_deposits(start_ms: int, end_ms: int) -> List[dict]:
    try:
        return _signed_get("/sapi/v1/capital/deposit/hisrec", {"startTime": start_ms, "endTime": end_ms}) or []
    except Exception:
        return []


def get_withdrawals(start_ms: int, end_ms: int) -> List[dict]:
    try:
        return _signed_get("/sapi/v1/capital/withdraw/history", {"startTime": start_ms, "endTime": end_ms}) or []
    except Exception:
        return []


# ---------- Formatting ----------


def fmt_markdown(
    fees_by_asset: Dict[str, float],
    pnl_by_symbol: Dict[str, float],
    transfers: List[Tuple[str, str, str, float, str]],
) -> str:
    lines: List[str] = []
    lines.append("## 手续费汇总 (按资产)")
    lines.append("资产 | 手续费")
    lines.append(":--|--:")
    for asset, amt in sorted(fees_by_asset.items()):
        lines.append(f"{asset} | {amt:.8f}")
    lines.append("")

    lines.append("## 实现盈亏 (USDT 计价) - 按交易对")
    lines.append("交易对 | Realized PnL (USDT)")
    lines.append(":--|--:")
    total_pnl = 0.0
    for sym, v in sorted(pnl_by_symbol.items()):
        lines.append(f"{sym} | {v:+.6f}")
        total_pnl += v
    lines.append(f"合计 | {total_pnl:+.6f}")
    lines.append("")

    lines.append("## 资金划转/充值/提现 记录")
    lines.append("时间 | 类型 | 资产 | 数量 | 备注/状态")
    lines.append(":--|:--|:--|--:|:--")
    for ts_str, typ, asset, amt, note in transfers:
        lines.append(f"{ts_str} | {typ} | {asset} | {amt:.8f} | {note}")
    return "\n".join(lines)


def fmt_csv(
    fees_by_asset: Dict[str, float],
    pnl_by_symbol: Dict[str, float],
    transfers: List[Tuple[str, str, str, float, str]],
) -> str:
    lines: List[str] = []
    lines.append("[Fees]")
    lines.append("asset,fee")
    for asset, amt in sorted(fees_by_asset.items()):
        lines.append(f"{asset},{amt}")
    lines.append("")

    lines.append("[RealizedPnL]")
    lines.append("symbol,pnl_usdt")
    for sym, v in sorted(pnl_by_symbol.items()):
        lines.append(f"{sym},{v}")
    lines.append("")

    lines.append("[Transfers]")
    lines.append("time,type,asset,amount,note")
    for ts_str, typ, asset, amt, note in transfers:
        # basic CSV escaping for commas in note
        lines.append(f"{ts_str},{typ},{asset},{amt},\"{str(note).replace('\\', '\\\\').replace('"', '\\"')}\"")
    return "\n".join(lines)


# ---------- Main ----------


def main():
    if not BINANCE_KEY or not BINANCE_SEC:
        print("请先设置环境变量 BINANCE_API_KEY 与 BINANCE_API_SECRET", file=sys.stderr)
        sys.exit(2)

    ap = argparse.ArgumentParser(description="Binance 现货资金明细")
    ap.add_argument("--hours", type=int, default=6, help="过去N小时")
    ap.add_argument("--format", choices=["markdown", "csv"], default="markdown")
    ap.add_argument(
        "--symbols",
        type=str,
        default="",
        help="逗号分隔的币种（现货对），如 BTC,ETH。默认为从余额自动发现 *USDT 交易对",
    )
    args = ap.parse_args()

    now_ms = int(time.time() * 1000)
    start_ms = now_ms - max(1, args.hours) * 3600_000

    # Discover symbols
    ex_info = exchange_info()
    if args.symbols.strip():
        bases = [x.strip().upper() for x in args.symbols.split(",") if x.strip()]
        symbols = [f"{b}USDT" for b in bases if symbol_exists(f"{b}USDT", ex_info)]
    else:
        symbols = autodiscover_usdt_symbols(ex_info)

    # Aggregate
    fees_all: Dict[str, float] = {}
    pnl_by_symbol: Dict[str, float] = {}

    for sym in symbols:
        trades = get_spot_trades(sym, start_ms, now_ms)
        if not trades:
            continue
        pnl, fees = fifo_realized_pnl(sym, trades, start_ms)
        pnl_by_symbol[sym] = pnl_by_symbol.get(sym, 0.0) + pnl
        for a, v in fees.items():
            fees_all[a] = fees_all.get(a, 0.0) + v

    # Transfers
    transfers: List[Tuple[str, str, str, float, str]] = []

    def _fmt_ts(ms: int) -> str:
        try:
            from datetime import datetime, timezone

            return datetime.fromtimestamp(ms / 1000.0, tz=timezone.utc).astimezone().strftime("%Y-%m-%d %H:%M:%S")
        except Exception:
            return str(ms)

    # Universal transfers
    for r in get_universal_transfers(start_ms, now_ms):
        try:
            ts = int(r.get("timestamp", 0) or 0)
            transfers.append(
                (
                    _fmt_ts(ts),
                    f"transfer:{r.get('type','')}",
                    r.get("asset", ""),
                    float(r.get("amount", 0) or 0),
                    r.get("status", ""),
                )
            )
        except Exception:
            continue

    # Deposits
    for r in get_deposits(start_ms, now_ms):
        try:
            ts = int(r.get("insertTime", 0) or 0)
            coin = r.get("coin", "")
            amount = float(r.get("amount", 0) or 0)
            status = r.get("status", "")
            txid = r.get("txId") or r.get("txIdHash") or ""
            note = f"status={status} tx={txid}"
            transfers.append((_fmt_ts(ts), "deposit", coin, amount, note))
        except Exception:
            continue

    # Withdrawals
    for r in get_withdrawals(start_ms, now_ms):
        try:
            ts = int(r.get("applyTime", 0) or r.get("applyTimeLong", 0) or 0)
            coin = r.get("coin", "")
            amount = float(r.get("amount", 0) or 0)
            status = r.get("status", "")
            txid = r.get("txId", "")
            note = f"status={status} tx={txid}"
            transfers.append((_fmt_ts(ts), "withdraw", coin, amount, note))
        except Exception:
            continue

    # Output
    if args.format == "markdown":
        out = fmt_markdown(fees_all, pnl_by_symbol, sorted(transfers, key=lambda x: x[0]))
    else:
        out = fmt_csv(fees_all, pnl_by_symbol, sorted(transfers, key=lambda x: x[0]))

    print(out)


if __name__ == "__main__":
    main()
