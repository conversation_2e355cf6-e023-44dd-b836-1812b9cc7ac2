#!/usr/bin/env python3
"""
Fetch Binance USDT-Margined Futures aggregate trades over a specified time range and save as CSV.

- Uses public endpoint: https://fapi.binance.com/fapi/v1/aggTrades
- Handles Binance's 1-hour time window restriction by chunking the request range into <= 1h windows
- Paginates within each window when there are > 1000 agg trades (uses fromId to continue)
- Robust retry/backoff and duplicate filtering across window boundaries

Example:
  python scripts/fetch_binance_futures_trades.py \
      --symbol BTCUSDT \
      --start "2025-09-01T00:00:00Z" \
      --end   "2025-09-01T03:00:00Z" \
      --out   data/binance_data/BTCUSDT_trades_20250901_0000_0300.csv

Notes:
- Endpoint returns aggregate trades (aggTrades), not raw trade-by-trade fills. For exact raw trades, Binance's futures API
  requires authenticated endpoints and different limits. If you need raw trades instead of aggTrades, tell me and I can extend this.
- Time arguments are assumed to be UTC unless a timezone offset is supplied.
"""

from __future__ import annotations

import argparse
import csv
import os
import sys
import time
from datetime import datetime, timedelta, timezone
from typing import Dict, Iterable, List, Optional, Tuple

import requests

FAPI_BASE = "https://fapi.binance.com"
AGG_TRADES_PATH = "/fapi/v1/aggTrades"

# Binance requires that if both startTime and endTime are provided, the range must be < 1 hour.
# We'll use a chunk a bit less than 1 hour to be safe.
ONE_HOUR_MS = 60 * 60 * 1000
CHUNK_MS_DEFAULT = 60 * 60 * 1000 - 1_000  # 59m59s


def parse_time_arg(value: str) -> int:
    """Parse a time argument into epoch milliseconds.

    Accepts:
    - ISO8601: "YYYY-MM-DDTHH:MM:SSZ", or with offset, or "YYYY-MM-DD HH:MM:SS" (assumed UTC)
    - Integer epoch seconds or milliseconds (auto-detected)
    """
    v = value.strip()
    # All digits? interpret as epoch seconds or ms
    if v.isdigit():
        iv = int(v)
        # Heuristic: >= 1e12 is milliseconds
        if iv >= 1_000_000_000_000:
            return iv
        # else seconds -> ms
        return iv * 1000

    # Normalize Z suffix to +00:00 for fromisoformat
    if v.endswith("Z"):
        v = v[:-1] + "+00:00"
    try:
        dt = datetime.fromisoformat(v)
        if dt.tzinfo is None:
            # Assume UTC if no tz specified
            dt = dt.replace(tzinfo=timezone.utc)
        return int(dt.timestamp() * 1000)
    except Exception:
        raise argparse.ArgumentTypeError(f"Unrecognized time format: {value}")


def format_iso_ms(ms: int) -> str:
    dt = datetime.fromtimestamp(ms / 1000, tz=timezone.utc)
    # Keep milliseconds in string
    return dt.strftime("%Y-%m-%dT%H:%M:%S.%fZ")[:-3]


def chunk_ranges(start_ms: int, end_ms: int, chunk_ms: int = CHUNK_MS_DEFAULT) -> Iterable[Tuple[int, int]]:
    cur = start_ms
    while cur < end_ms:
        nxt = min(cur + chunk_ms, end_ms)
        yield cur, nxt
        cur = nxt


def mk_session(timeout: int = 20) -> requests.Session:
    s = requests.Session()
    s.headers.update({
        "User-Agent": "libwebsocket-rs-scripts/1.0 (+https://binance.com)",
        "Accept": "application/json",
    })
    s.request = _with_timeout(s.request, timeout)
    return s


def _with_timeout(request_func, timeout_default: int):
    def wrapper(method, url, **kwargs):
        if "timeout" not in kwargs:
            kwargs["timeout"] = timeout_default
        return request_func(method, url, **kwargs)
    return wrapper


def fetch_agg_trades_window(
    session: requests.Session,
    symbol: str,
    start_ms: int,
    end_ms: int,
    limit: int = 1000,
    max_pages: int = 1_000,
    retry: int = 5,
    backoff_base: float = 0.5,
) -> List[Dict]:
    """Fetch all aggTrades for a single window [start_ms, end_ms] (inclusive).

    Logic:
    1) First request uses startTime+endTime with limit.
    2) If exactly limit results are returned, continue paging with fromId=last.a+1, filtering by time <= end_ms.
    """
    url = FAPI_BASE + AGG_TRADES_PATH

    results: List[Dict] = []
    seen_ids = set()

    # First page with time filter
    params = {"symbol": symbol.upper(), "startTime": start_ms, "endTime": end_ms, "limit": min(1000, max(1, limit))}

    def do_request(p):
        nonlocal retry
        attempt = 0
        while True:
            attempt += 1
            try:
                resp = session.get(url, params=p)
                if resp.status_code == 429:
                    # rate limit; use Retry-After if present, else backoff
                    ra = resp.headers.get("Retry-After")
                    sleep_s = float(ra) if ra else min(30.0, backoff_base * (2 ** (attempt - 1)))
                    time.sleep(sleep_s)
                    continue
                if resp.status_code >= 500:
                    # server error, retry
                    time.sleep(min(30.0, backoff_base * (2 ** (attempt - 1))))
                    if attempt <= retry:
                        continue
                resp.raise_for_status()
                return resp.json()
            except requests.RequestException as e:
                if attempt > retry:
                    raise
                time.sleep(min(30.0, backoff_base * (2 ** (attempt - 1))))

    data = do_request(params)
    if not isinstance(data, list):
        raise RuntimeError(f"Unexpected response for aggTrades: {data}")

    def append_filtered(items: List[Dict]) -> Tuple[int, Optional[int]]:
        """Append items within time range, dedup by aggId; return (added_count, last_agg_id)."""
        added = 0
        last_id = None
        for it in items:
            t = it.get("T")
            aid = it.get("a")
            if t is None or aid is None:
                continue
            if t > end_ms:
                # Reached beyond window
                break
            if aid in seen_ids:
                continue
            seen_ids.add(aid)
            results.append(it)
            last_id = aid
            added += 1
        return added, last_id

    _, last_id = append_filtered(data)

    # If the page was full, continue with fromId until we pass end_ms or no more data
    pages = 1
    while len(data) == params["limit"] and pages < max_pages and last_id is not None:
        pages += 1
        next_params = {"symbol": symbol.upper(), "fromId": last_id + 1, "limit": params["limit"]}
        data = do_request(next_params)
        if not data:
            break
        added, new_last_id = append_filtered(data)
        if added == 0:
            # Either we crossed end_ms or no new items
            break
        last_id = new_last_id if new_last_id is not None else last_id

    return results


def write_csv(path: str, rows: Iterable[Dict], write_header: bool) -> int:
    os.makedirs(os.path.dirname(path) or ".", exist_ok=True)
    fieldnames = [
        "symbol",
        "agg_id",
        "price",
        "qty",
        "first_id",
        "last_id",
        "time_ms",
        "time_iso",
        "is_buyer_maker",
        "is_best_match",
    ]
    count = 0
    with open(path, "a", newline="", encoding="utf-8") as f:
        writer = csv.DictWriter(f, fieldnames=fieldnames)
        if write_header:
            writer.writeheader()
        for it in rows:
            writer.writerow({
                "symbol": it.get("s"),
                "agg_id": it.get("a"),
                "price": it.get("p"),
                "qty": it.get("q"),
                "first_id": it.get("f"),
                "last_id": it.get("l"),
                "time_ms": it.get("T"),
                "time_iso": format_iso_ms(int(it.get("T", 0)) if it.get("T") is not None else 0),
                "is_buyer_maker": it.get("m"),
                "is_best_match": it.get("M"),
            })
            count += 1
    return count


def default_out_path(symbol: str, start_ms: int, end_ms: int) -> str:
    start_s = datetime.fromtimestamp(start_ms / 1000, tz=timezone.utc).strftime("%Y%m%d_%H%M%S")
    end_s = datetime.fromtimestamp(end_ms / 1000, tz=timezone.utc).strftime("%Y%m%d_%H%M%S")
    return os.path.join("data", "binance_data", f"{symbol.upper()}_futures_aggTrades_{start_s}_{end_s}.csv")


def main():
    parser = argparse.ArgumentParser(description="Fetch Binance USDT-Margined Futures aggTrades over a time range and save to CSV")
    parser.add_argument("--symbol", required=True, help="Trading pair symbol, e.g., BTCUSDT")
    parser.add_argument("--start", required=True, type=parse_time_arg, help="Start time (ISO8601 or epoch s/ms), UTC assumed if no TZ")
    parser.add_argument("--end", required=True, type=parse_time_arg, help="End time (ISO8601 or epoch s/ms), UTC assumed if no TZ")
    parser.add_argument("--out", default=None, help="Output CSV path; default to data/binance_data/<symbol>_futures_aggTrades_<start>_<end>.csv")
    parser.add_argument("--chunk-ms", type=int, default=CHUNK_MS_DEFAULT, help="Chunk size in ms (<= 3600000). Default ~1h")
    parser.add_argument("--limit", type=int, default=1000, help="Per-request limit (max 1000)")
    parser.add_argument("--quiet", action="store_true", help="Less verbose output")

    args = parser.parse_args()

    symbol = args.symbol.upper()
    start_ms = args.start
    end_ms = args.end

    if end_ms <= start_ms:
        print("end must be greater than start", file=sys.stderr)
        sys.exit(2)
    if args.chunk_ms > ONE_HOUR_MS:
        print("chunk-ms must be <= 3600000 (1h)", file=sys.stderr)
        sys.exit(2)

    out_path = args.out or default_out_path(symbol, start_ms, end_ms)
    os.makedirs(os.path.dirname(out_path) or ".", exist_ok=True)

    session = mk_session()

    total = 0
    wrote_header = not os.path.exists(out_path) or os.path.getsize(out_path) == 0

    if not args.quiet:
        print(f"Fetching {symbol} futures aggTrades from {format_iso_ms(start_ms)} to {format_iso_ms(end_ms)}")
        print(f"Output: {out_path}")

    last_global_id: Optional[int] = None
    for idx, (cs, ce) in enumerate(chunk_ranges(start_ms, end_ms, args.chunk_ms), start=1):
        if not args.quiet:
            print(f"Chunk {idx}: {format_iso_ms(cs)} -> {format_iso_ms(ce)} ...", end=" ")
            sys.stdout.flush()
        try:
            rows = fetch_agg_trades_window(session, symbol, cs, ce, limit=args.limit)
            # Dedup against previous chunk by agg id
            if last_global_id is not None:
                rows = [r for r in rows if int(r.get("a", -1)) > last_global_id]
            if rows:
                last_global_id = int(rows[-1].get("a"))
                wrote = write_csv(out_path, rows, write_header=wrote_header)
                wrote_header = False
                total += wrote
            if not args.quiet:
                print(f"{len(rows)} rows")
        except KeyboardInterrupt:
            print("\nInterrupted. Exiting...")
            break
        except Exception as e:
            print(f"\nError in chunk {idx}: {e}", file=sys.stderr)
            raise

    if not args.quiet:
        print(f"Done. Wrote {total} rows to {out_path}")


if __name__ == "__main__":
    main()

