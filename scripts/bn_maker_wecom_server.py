# wecon.py - 企业微信回调处理服务
import logging
from datetime import datetime
from flask import Flask, request, abort
from wechatpy.enterprise.crypto import WeChatCrypto
import re

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.FileHandler(f'wecon_{datetime.now().strftime("%Y%m%d")}.log'), logging.StreamHandler()],
)
logger = logging.getLogger(__name__)

# TOKEN = "qIqPjbC"
# AES_KEY = "IOPUz9gqcpHeUSO8srYIwOGjMMGse088xUg4osMyHjH"
TOKEN = "EpwB5RvUuJtB2FAzD6QBrsvmDEGWMm"
AES_KEY = "w9nPaBGX9fRh2AILX7y7xZOy1nx5Kjy3g9v61x8Sqno"
CORP_ID = "ww95e76226b475125a"

app = Flask(__name__)
crypto = WeChatCrypto(TOKEN, AES_KEY, CORP_ID)

from bn_maker_notify import (
    get_binance_exposures,
    get_gate_exposures,
    get_binance_account_totals,
    get_gate_account_totals,
    compare_and_print,
    set_all_leverage,
    set_binance_leverage,
    set_gate_leverage,
    build_recent_closures_msg,
    build_trades_agg_msg,
    build_funding_details_msg,
    build_funding_details_by_currency_msg,
    flatten_unhedged_positions,
    flatten_symbol_all_positions,
    build_bn_open_orders_msg,
    ABS_TOL,
)

from lxml import etree
import time
import os

import json
import threading
from typing import Any, Dict, Optional

STATE_STOPPED = "stopped"
STATE_STARTED = "started"


class StateStore:
    def __init__(self) -> None:
        self._lock = threading.RLock()
        self._desired_global: str = STATE_STOPPED
        self._desired_per_agent: Dict[str, str] = {}
        self._last_reports: Dict[str, Dict[str, Any]] = {}

    def set_desired(self, state: str, agent: Optional[str] = None) -> None:
        st = normalize_state(state)
        with self._lock:
            if agent:
                self._desired_per_agent[agent] = st
            else:
                self._desired_global = st
        logger.info("Desired state set: agent=%s state=%s", agent or "*", st)

    def get_desired(self, agent: Optional[str]) -> str:
        with self._lock:
            if agent and agent in self._desired_per_agent:
                return self._desired_per_agent[agent]
            return self._desired_global

    def record_report(self, agent: str, payload: Dict[str, Any]) -> None:
        with self._lock:
            payload = dict(payload)
            payload["_received_at"] = time.time()
            self._last_reports[agent] = payload
        logger.debug("Report recorded for agent=%s: %s", agent, payload)

    def snapshot(self) -> Dict[str, Any]:
        with self._lock:
            return {
                "desired_global": self._desired_global,
                "desired_per_agent": dict(self._desired_per_agent),
                "last_reports": dict(self._last_reports),
            }

    def get_last_state(self, agent: str) -> Optional[str]:
        with self._lock:
            rep = self._last_reports.get(agent)
            if not rep:
                return None
            st = rep.get("state")
            return str(st) if st is not None else None

    def list_agents(self) -> list[str]:
        with self._lock:
            return list(self._last_reports.keys())


def normalize_state(val: Any) -> str:
    if isinstance(val, bool):
        return STATE_STARTED if val else STATE_STOPPED
    sval = str(val).strip().lower()
    mapping = {
        "start": STATE_STARTED,
        "started": STATE_STARTED,
        "on": STATE_STARTED,
        "1": STATE_STARTED,
        "true": STATE_STARTED,
        "stop": STATE_STOPPED,
        "stopped": STATE_STOPPED,
        "off": STATE_STOPPED,
        "0": STATE_STOPPED,
        "false": STATE_STOPPED,
        "启动": STATE_STARTED,
        "开始": STATE_STARTED,
        "停止": STATE_STOPPED,
    }
    return mapping.get(sval, STATE_STOPPED)


STORE = StateStore()


DEFAULT_AGENT = "bn_gate"


def resolve_agent(target_agent: Optional[str]) -> Optional[str]:
    if target_agent:
        return target_agent
    if DEFAULT_AGENT:
        return DEFAULT_AGENT
    agents = STORE.list_agents()
    if len(agents) == 1:
        return agents[0]
    return None


def wait_for_agent_state(agent: str, expected: str, timeout: float = 25.0, interval: float = 0.5):
    start = time.time()
    last_state: Optional[str] = None
    while time.time() - start < timeout:
        state = STORE.get_last_state(agent)
        if state == expected:
            return True, state, time.time() - start
        if state is not None:
            last_state = state
        time.sleep(interval)
    return False, last_state, time.time() - start


def create_text_reply(to_user, from_user, content):
    """创建企业微信文本回复消息的XML格式"""
    timestamp = str(int(time.time()))
    msg_id = str(int(time.time() * 1000))

    xml_content = f"""<xml>
<ToUserName><![CDATA[{to_user}]]></ToUserName>
<FromUserName><![CDATA[{from_user}]]></FromUserName>
<CreateTime>{timestamp}</CreateTime>
<MsgType><![CDATA[text]]></MsgType>
<Content><![CDATA[{content}]]></Content>
<MsgId>{msg_id}</MsgId>
</xml>"""

    return xml_content


def encrypt_reply(xml_content, nonce, timestamp):
    """加密回复消息"""
    try:
        encrypted = crypto.encrypt_message(xml_content, nonce, timestamp)
        return encrypted
    except Exception as e:
        logger.error(f"加密回复消息失败: {e}")
        return ""


def get_balance_info():
    """获取余额信息"""
    try:
        bn_totals = get_binance_account_totals()
        gt_totals = get_gate_account_totals()

        bn_bal, bn_upnl = bn_totals
        gt_bal, gt_upnl = gt_totals
        sum_bal = bn_bal + gt_bal
        sum_upnl = bn_upnl + gt_upnl

        lines = []
        lines.append("==账户余额信息==")
        lines.append(f"BN 余额: {bn_bal:.2f} USDT")
        lines.append(f"BN 未实现盈亏: {bn_upnl:.2f} USDT")
        lines.append(f"Gt 余额: {gt_bal:.2f} USDT")
        lines.append(f"Gt 未实现盈亏: {gt_upnl:.2f} USDT")
        lines.append("=" * 20)
        lines.append(f"总余额: {sum_bal:.2f} USDT")
        lines.append(f"总未实现盈亏: {sum_upnl:.2f} USDT")
        lines.append(f"净值: {sum_bal + sum_upnl:.2f} USDT")

        return "\n".join(lines)
    except Exception as e:
        return f"获取余额信息失败: {str(e)}"


def get_position_info():
    """获取持仓信息"""
    try:
        binance_exp = get_binance_exposures()
        gate_exp = get_gate_exposures()
        bn_totals = get_binance_account_totals()
        gt_totals = get_gate_account_totals()

        return compare_and_print(binance_exp, gate_exp, ABS_TOL, bn_totals, gt_totals)
    except Exception as e:
        return f"获取持仓信息失败: {str(e)}"


def get_status_info():
    """获取系统状态信息"""
    try:
        from datetime import datetime

        lines = []
        lines.append("==系统状态==")
        lines.append(f"服务器时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        # 测试API连接
        try:
            get_binance_account_totals()
            lines.append("✅ Binance API 连接正常")
        except Exception as e:
            lines.append(f"❌ Binance API 连接失败: {str(e)[:50]}")

        try:
            get_gate_account_totals()
            lines.append("✅ Gate API 连接正常")
        except Exception as e:
            lines.append(f"❌ Gate API 连接失败: {str(e)[:50]}")

        lines.append("服务运行正常")
        return "\n".join(lines)
    except Exception as e:
        return f"获取状态信息失败: {str(e)}"


def set_leverage_info(leverage: int):
    """设置所有交易对的杠杆"""
    try:
        logger.info(f"开始设置杠杆为 {leverage}x")

        # 验证杠杆范围
        if leverage < 1 or leverage > 125:
            return f"❌ 杠杆倍数无效: {leverage}x\n有效范围: 1-125倍"

        results = set_all_leverage(leverage)

        lines = []
        lines.append(f"==杠杆设置结果 ({leverage}x)==")

        # Binance结果
        bn_success = len(results["binance"]["success"])
        bn_failed = len(results["binance"]["failed"])
        lines.append(f"Binance: ✅{bn_success} ❌{bn_failed}")

        if bn_failed > 0:
            lines.append("Binance失败:")
            for fail in results["binance"]["failed"][:5]:  # 只显示前5个失败
                lines.append(f"  {fail}")
            if bn_failed > 5:
                lines.append(f"  ...还有{bn_failed-5}个失败")

        # Gate结果
        gt_success = len(results["gate"]["success"])
        gt_failed = len(results["gate"]["failed"])
        lines.append(f"Gate: ✅{gt_success} ❌{gt_failed}")

        if gt_failed > 0:
            lines.append("Gate失败:")
            for fail in results["gate"]["failed"][:5]:  # 只显示前5个失败
                lines.append(f"  {fail}")
            if gt_failed > 5:
                lines.append(f"  ...还有{gt_failed-5}个失败")

        lines.append("=" * 20)
        total_success = bn_success + gt_success
        total_failed = bn_failed + gt_failed
        lines.append(f"总计: ✅{total_success} ❌{total_failed}")

        if total_failed == 0:
            lines.append("🎉 所有交易对杠杆设置成功！")
        else:
            lines.append("⚠️ 部分交易对设置失败，请检查日志")

        logger.info(f"杠杆设置完成: 成功{total_success}, 失败{total_failed}")
        return "\n".join(lines)

    except Exception as e:
        error_msg = f"设置杠杆失败: {str(e)}"
        logger.error(error_msg)
        return error_msg


def set_symbol_leverage_info(base: str, leverage: int):
    """在 Binance 与 Gate 为指定币种设置杠杆，例如: BTC 5x"""
    try:
        base_u = (base or "").upper().strip()
        if not base_u.isalpha() or not (2 <= len(base_u) <= 12):
            return f"❌ 币种无效: {base}"

        # 验证杠杆范围
        if leverage < 1 or leverage > 125:
            return f"❌ 杠杆倍数无效: {leverage}x\n有效范围: 1-125倍"

        logger.info(f"开始设置 {base_u} 杠杆为 {leverage}x")

        # 构造交易对/合约
        bn_symbol = f"{base_u}USDT"
        gt_contract = f"{base_u}_USDT"

        # 分别设置两所杠杆
        bn_ok, bn_res = set_binance_leverage(bn_symbol, leverage)
        gt_ok, gt_res = set_gate_leverage(gt_contract, leverage)

        lines: list[str] = []
        lines.append(f"==杠杆设置结果 ({base_u} {leverage}x)==")
        lines.append(f"Binance: {'✅' if bn_ok else '❌'} {bn_symbol} -> {leverage}x")
        if not bn_ok:
            lines.append(f"  失败原因: {str(bn_res)[:200]}")
        lines.append(f"Gate: {'✅' if gt_ok else '❌'} {gt_contract} -> {leverage}x")
        if not gt_ok:
            lines.append(f"  失败原因: {str(gt_res)[:200]}")

        if bn_ok and gt_ok:
            lines.append("🎉 杠杆设置完成")
        else:
            lines.append("⚠️ 部分设置失败，请检查日志")

        logger.info(f"{base_u} 杠杆设置完成: BN={'OK' if bn_ok else 'FAIL'}, GT={'OK' if gt_ok else 'FAIL'}")
        return "\n".join(lines)

    except Exception as e:
        error_msg = f"设置杠杆失败: {str(e)}"
        logger.error(error_msg)
        return error_msg


def process_message_content(content: str):
    """处理消息内容，根据关键词返回相应信息"""
    content = content.strip()
    content_lower = content.lower()
    print(f"收到消息: {content}")

    if "交易状态" in content_lower:
        state = STORE.get_last_state(DEFAULT_AGENT)
        return f"当前状态：{state}"
    elif "开始交易" in content_lower or "停止交易" in content_lower:
        if "开始" in content_lower:
            STORE.set_desired(STATE_STARTED, DEFAULT_AGENT)
            ok, cur, spent = wait_for_agent_state(DEFAULT_AGENT, STATE_STARTED, timeout=25.0, interval=0.5)
            if ok:
                return f"已启动 {DEFAULT_AGENT}（用时 {spent:.1f}s）。当前状态: started"
            else:
                cur_txt = cur or "未上报"
                return (
                    f"已下发启动指令到 {DEFAULT_AGENT}，当前状态: {cur_txt}。\n"
                    "等待超时（25s）。稍后可发送 'status' 查询。"
                )
        if "停止" in content_lower:
            STORE.set_desired(STATE_STOPPED, DEFAULT_AGENT)
            ok, cur, spent = wait_for_agent_state(DEFAULT_AGENT, STATE_STOPPED, timeout=25.0, interval=0.5)
            if ok:
                return f"已停止 {DEFAULT_AGENT}（用时 {spent:.1f}s）。当前状态: stopped"
            else:
                cur_txt = cur or "未上报"
                return (
                    f"已下发停止指令到 {DEFAULT_AGENT}，当前状态: {cur_txt}。\n"
                    "等待超时（25s）。稍后可发送 'status' 查询。"
                )
    elif "余额" in content_lower or "balance" in content_lower:
        return get_balance_info()
    elif "持仓" in content_lower or "position" in content_lower:
        return get_position_info()
    elif "最近平仓" in content or "最近 平仓" in content:

        m = re.search(r"最近\s*平仓\s*(\d+)?", content)
        minutes = int(m.group(1)) if (m and m.group(1)) else 60
        minutes = max(1, min(24 * 60, minutes))  # 1 到 1440 分钟
        return build_recent_closures_msg(minutes)
    elif ("成交" in content_lower) or ("交易" in content_lower) or ("trade" in content_lower):

        # 支持："成交 SOMI 2"、"交易 somi 2小时"、"trade SOMI 3h"
        m = re.search(r"(?:成交|交易|trades?)\s+([a-zA-Z]{2,12})(?:\s+(\d+)(?:\s*(?:h|H|小时))?)?", content)
        base = m.group(1) if m else None
        hours = int(m.group(2)) if (m and m.group(2)) else 2
        if not base:
            return "格式: 成交 <币种> <小时数>，例如: 成交 SOMI 2"
        hours = max(1, min(24 * 7, hours))  # 1-168 小时
        return build_trades_agg_msg(base, hours)

    elif re.search(r"(?:分币种(?:资金)?明细)", content):
        m = re.search(r"(?:分币种(?:资金)?明细)\s*(\d+)?", content)
        hours = int(m.group(1)) if (m and m.group(1)) else 24
        hours = max(1, min(24 * 7, hours))
        return build_funding_details_by_currency_msg(hours)

    elif ("资金" in content) or ("资金明细" in content) or ("funding" in content_lower):

        m = re.search(r"(?:资金(?:明细)?)\s*(\d+)?", content)
        hours = int(m.group(1)) if (m and m.group(1)) else 24
        hours = max(1, min(24 * 7, hours))
        return build_funding_details_msg(hours)

    elif (
        ("当前订单" in content)
        or ("open orders" in content_lower)
        or ("open order" in content_lower)
        or ("open订单" in content_lower)
    ):
        return build_bn_open_orders_msg()

    # 指定币种平仓：支持 "指定币种平仓 BTC" / "平仓 BTC" / "close BTC"
    elif re.search(r"(?:指定(?:币种)?平仓|平仓|close)\s+([A-Za-z]{2,12})", content, re.IGNORECASE):

        m = re.search(r"(?:指定(?:币种)?平仓|平仓|close)\s+([A-Za-z]{2,12})", content, re.IGNORECASE)
        base = m.group(1)
        return flatten_symbol_all_positions(base)

    elif (
        ("平未对冲" in content)
        or ("平暴露" in content)
        or ("平掉暴露" in content)
        or ("平未对冲仓位" in content)
        or ("flatten" in content_lower)
    ):
        return flatten_unhedged_positions()

    elif "状态" in content_lower or "status" in content_lower:
        return get_status_info()
    elif "设置杠杆" in content or "杠杆" in content or "leverage" in content_lower:

        # 优先解析：含币种 + 倍数，例如：设置杠杆 BTC 5 / 杠杆 eth 10 / leverage SOL 20x
        m = re.search(r"(?:设置杠杆|杠杆|leverage)\s+([A-Za-z]{2,12})\s+(\d+)(?:\s*(?:x|倍)?)?", content, re.IGNORECASE)
        if m:
            base = m.group(1)
            lev = int(m.group(2))
            return set_symbol_leverage_info(base, lev)

        # 若仅给出倍数，引导用户带上币种
        m2 = re.search(r"(?:设置杠杆|杠杆|leverage)\s*(\d+)(?:\s*(?:x|倍)?)?$", content_lower)
        if m2:
            return """❌ 杠杆设置格式更新为: 需指定币种

正确格式：
• 设置杠杆 BTC 5
• 杠杆 ETH 10
• leverage SOL 20
• 设置杠杆 BTC 5x / 5倍

有效范围: 1-125倍"""

        # 其他情况认为格式错误
        return """❌ 杠杆设置格式错误

正确格式：
• 设置杠杆 BTC 5
• 杠杆 ETH 10
• leverage SOL 20
• 设置杠杆 BTC 5x / 5倍

有效范围: 1-125倍"""
    elif "帮助" in content_lower or "help" in content_lower:
        return """可用命令：
• 发送 "余额" 查看账户余额信息
• 发送 "持仓" 查看持仓对冲情况
• 发送 "最近平仓 [分钟]" 查看最近一段时间的平仓统计（默认60分钟）
• 发送 "成交 <币种> <小时数>" 或 "trade <symbol> <hours>" 查看过去N小时两所成交汇总（笔数/成交量/名义额/手续费/PnL）
• 发送 "资金 [小时]" 或 "funding [hours]" 查看过去N小时资金明细（PnL/Funding/Commission）
• 发送 "分币种明细 [小时]" 查看过去N小时按币种资金明细（每币种 PnL/Funding/Commission）
• 发送 "平未对冲" / "平暴露" / "flatten" 平掉未对冲仓位（优先减少仓位，Reduce-Only 市价）
• 发送 "平仓 <币种>" 或 "close <symbol>" 指定币种在两所市价 reduce-only 全部平仓

• 发送 "状态" 查看系统状态
• 发送 "设置杠杆 <币种> <倍数>" 在 Binance 与 Gate 同步设置该币种杠杆
• 发送 "帮助" 查看此帮助信息

杠杆设置示例:
• 设置杠杆 BTC 5
• 杠杆 ETH 10
• leverage SOL 20
• 设置杠杆 BTC 5x / 5倍

默认情况下发送任何其他消息都会返回持仓信息。"""
    else:
        # 默认返回持仓信息（保持原有行为）
        return get_position_info()


# --- Agent APIs ---
@app.post("/api/desired/bn_maker")
def api_desired():
    body = request.get_json(silent=True) or {}
    agent = body.get("agent")
    state = STORE.get_desired(agent)
    return {"target_state": state}, 200


@app.post("/api/report/bn_maker")
def api_report():
    body = request.get_json(silent=True) or {}
    agent = str(body.get("agent") or "unknown")
    STORE.record_report(agent, body)
    return {"ok": True}, 200


def api_status():
    return STORE.snapshot()


def api_admin_set(state):
    STORE.set_desired(state, "bn_gate")


@app.get("/wecom/callback/bn_maker")
def verify():
    sig = request.args.get("msg_signature", "")
    ts = request.args.get("timestamp", "")
    nonce = request.args.get("nonce", "")
    echo = request.args.get("echostr", "")
    try:
        plain = crypto.check_signature(sig, ts, nonce, echo)  # 解密得到明文
        logger.info(f"验证请求成功: {plain}")
        return plain, 200, {"Content-Type": "text/plain; charset=utf-8"}
    except Exception as e:
        logger.error(f"验证失败: {e}")
        abort(403)


@app.post("/wecom/callback/bn_maker")
def recv():
    sig = request.args.get("msg_signature", "")
    ts = request.args.get("timestamp", "")
    nonce = request.args.get("nonce", "")

    try:
        logger.info(f"接收到请求数据: {request.data}")
        xml = etree.fromstring(request.data)
        encrypt = xml.findtext("Encrypt")
        if not encrypt:
            logger.error("未找到加密数据")
            abort(400)
    except Exception as e:
        logger.error(f"解析XML错误: {e}")
        abort(400)

    logger.info(f"加密数据: {encrypt}")

    try:
        # 解密得到明文消息
        plain = crypto.check_signature(sig, ts, nonce, encrypt)
        logger.info(f"解密后的消息: {plain}")

        # 解析消息内容
        try:
            msg_xml = etree.fromstring(plain)
            msg_type = msg_xml.findtext("MsgType")
            to_user = msg_xml.findtext("FromUserName", "")  # 发送者成为接收者
            from_user = msg_xml.findtext("ToUserName", "")  # 接收者成为发送者
            print(msg_xml)
            print(f"to_user: {to_user}, from_user: {from_user}")

            if msg_type == "text":
                # 文本消息
                content = msg_xml.findtext("Content", "")
                logger.info(f"收到文本消息: {content}")

                # 根据消息内容返回相应信息
                response_content = process_message_content(content)
                logger.info(f"返回响应长度: {len(response_content)} 字符")

                # 创建回复消息XML
                reply_xml = create_text_reply(to_user, from_user, response_content)

                # 加密回复消息
                encrypted_reply = encrypt_reply(reply_xml, nonce, ts)
                if encrypted_reply:
                    return encrypted_reply, 200, {"Content-Type": "application/xml; charset=utf-8"}
                else:
                    logger.error("加密回复失败，返回空响应")
                    return "", 200, {"Content-Type": "text/plain; charset=utf-8"}
            else:
                # 其他类型消息，返回默认持仓信息

                SENDMSG_RE = re.compile(r"#sendmsg#_(\d+)_(\d+)#(\d+)")

                def parse_sendmsg_event_key(k: str):
                    m = SENDMSG_RE.fullmatch(k)
                    if not m:
                        return None
                    i, j, menu_ver = map(int, m.groups())  # i=一级菜单序号，j=子菜单序号
                    return i, j, menu_ver

                logger.info(f"收到非文本消息，类型: {msg_type}")
                event = msg_xml.findtext("Event", "")
                event_key = msg_xml.findtext("EventKey", "")
                logger.info(f"收到事件: {event}, EventKey={event_key}")
                i, j, _ = parse_sendmsg_event_key(event_key)
                if i == 0 and j == 0:
                    response_content = process_message_content("持仓")
                elif i == 0 and j == 1:
                    response_content = process_message_content("平未对冲仓位")
                elif i == 0 and j == 3:
                    response_content = process_message_content("当前订单")
                elif i == 1 and j == 0:
                    response_content = process_message_content("余额")
                elif i == 1 and j == 1:
                    response_content = process_message_content("资金明细")
                elif i == 1 and j == 2:
                    response_content = process_message_content("分币种明细")
                elif i == 2 and j == 1:
                    response_content = process_message_content("开始交易")
                elif i == 2 and j == 2:
                    response_content = process_message_content("停止交易")
                elif i == 2 and j == 3:
                    response_content = process_message_content("交易状态")
                else:
                    response_content = ""

                # 创建回复消息XML
                reply_xml = create_text_reply(to_user, from_user, response_content)

                # 加密回复消息
                encrypted_reply = encrypt_reply(reply_xml, nonce, ts)
                if encrypted_reply:
                    return encrypted_reply, 200, {"Content-Type": "application/xml; charset=utf-8"}
                else:
                    logger.error("加密回复失败，返回空响应")
                    return "", 200, {"Content-Type": "text/plain; charset=utf-8"}

        except Exception as e:
            logger.error(f"解析消息内容错误: {e}")
            # 解析失败时返回默认持仓信息
            response_content = get_position_info()

            # 尝试创建回复消息（使用默认用户信息）
            try:
                reply_xml = create_text_reply("", "", response_content)
                encrypted_reply = encrypt_reply(reply_xml, nonce, ts)
                if encrypted_reply:
                    return encrypted_reply, 200, {"Content-Type": "application/xml; charset=utf-8"}
            except Exception as e2:
                logger.error(f"创建默认回复失败: {e2}")

            return "", 200, {"Content-Type": "text/plain; charset=utf-8"}

    except Exception as e:
        logger.error(f"解密失败: {e}")
        abort(403)


if __name__ == "__main__":
    logger.info("启动企业微信回调服务...")
    logger.info(f"服务配置: CORP_ID={CORP_ID}")
    logger.info("支持的命令: 启动/停止(可选 agent)、余额、持仓、最近平仓、成交、状态、帮助")

    try:
        app.run(host="0.0.0.0", port=5001, debug=False)
    except KeyboardInterrupt:
        logger.info("服务被用户中断")
    except Exception as e:
        logger.error(f"服务启动失败: {e}")
    finally:
        logger.info("企业微信回调服务已停止")
