#!/usr/bin/env python3
"""
tri-arb-scanner.py — 新逻辑：
1) 找出同时有现货与期货（永续）交易的交易对（按 symbol 精确匹配，如 BTCUSDT）
2) 额外查询 Gate 是否有对应永续合约，若有则获取 Gate 当前资金费率
3) 最终按 Gate 的资金费率倒序排列并输出前 N（默认 10），并同时展示 Binance 的资金费率作为参考
"""

import argparse
import time
import sys

import requests

BINANCE_SPOT = "https://api.binance.com"
BINANCE_FUTURES = "https://fapi.binance.com"
GATE_BASE = "https://api.gateio.ws"
GATE_PREFIX = "/api/v4"


def _fetch_json(base: str, path: str, retries: int = 3, backoff: int = 3, timeout: int = 15, **params):
    for attempt in range(1, retries + 1):
        try:
            r = requests.get(f"{base}{path}", params=params, timeout=timeout)
            r.raise_for_status()
            return r.json()
        except Exception as e:
            if attempt == retries:
                raise
            wait = backoff * attempt
            print(f"⚠️ 请求失败 {base}{path}: {e} | 重试 {attempt}/{retries}，等待 {wait}s", file=sys.stderr)
            time.sleep(wait)


def exchange_info_spot(**kw):
    return _fetch_json(BINANCE_SPOT, "/api/v3/exchangeInfo", **kw).get("symbols", [])


def exchange_info_futures(**kw):
    return _fetch_json(BINANCE_FUTURES, "/fapi/v1/exchangeInfo", **kw).get("symbols", [])


def premium_index(**kw):
    return _fetch_json(BINANCE_FUTURES, "/fapi/v1/premiumIndex", **kw)


def gate_public_get(path: str, **kw):
    return _fetch_json(GATE_BASE, f"{GATE_PREFIX}{path}", **kw)


def _gate_contract_map(**kw):
    """返回 Gate USDT 永续合约的信息映射：{base: contract_obj}
    取 /futures/usdt/contracts 列表，优先使用 name=BASE_USDT 的条目。
    """
    data = gate_public_get("/futures/usdt/contracts", **kw)
    out = {}
    if isinstance(data, list):
        for c in data:
            try:
                name = c.get("name") or ""
                if not name.endswith("_USDT"):
                    continue
                base = name.split("_", 1)[0]
                # 仅保留第一个匹配，或覆盖为 trade_status 更好的（如果提供）
                out[base] = c
            except Exception:
                continue
    return out


def _gate_rate_from_contract(c: dict) -> float:
    """从 Gate 合约对象提取一个当前资金费率数值。
    优先 funding_rate_indicative，其次 funding_rate；均不存在则返回 0.0。
    """
    if not isinstance(c, dict):
        return 0.0
    for k in ("funding_rate_indicative", "funding_rate"):
        v = c.get(k)
        if v is None:
            continue
        try:
            return float(v)
        except Exception:
            continue
    return 0.0


def main():
    parser = argparse.ArgumentParser(description="列出现货+永续同时存在，按 Gate 资金费率倒序的交易对（Binance/Gate）")
    parser.add_argument("--top", type=int, default=10, help="输出前N名（默认10）")
    parser.add_argument("--retries", type=int, default=3, help="API 调用最大重试次数")
    parser.add_argument("--backoff", type=int, default=3, help="第一次重试等待秒数 (随后指数增加)")
    args = parser.parse_args()

    # 1) 同时有现货和永续期货（Binance）的交易对，仅考虑 USDT 计价以便与 Gate 对齐
    spot_symbols = exchange_info_spot(retries=args.retries, backoff=args.backoff)
    spot_trading = {s["symbol"] for s in spot_symbols if s.get("status") == "TRADING"}

    fut_symbols = exchange_info_futures(retries=args.retries, backoff=args.backoff)
    fut_perp_trading = {
        s["symbol"] for s in fut_symbols if s.get("status") == "TRADING" and s.get("contractType") == "PERPETUAL"
    }

    both = {sym for sym in (spot_trading & fut_perp_trading) if sym.endswith("USDT")}
    if not both:
        print("未发现同时有现货与永续合约（USDT）的交易对。")
        return

    # 额外：预取 Binance 的资金费率作为参考
    prem = premium_index(retries=args.retries, backoff=args.backoff)
    bn_rate_map = {}
    if isinstance(prem, list):
        for item in prem:
            sym = item.get("symbol")
            if not sym or not sym.endswith("USDT"):
                continue
            try:
                bn_rate_map[sym] = float(item.get("lastFundingRate", 0.0) or 0.0)
            except Exception:
                bn_rate_map[sym] = 0.0

    # 2) 拉取 Gate USDT 永续合约列表与资金费率
    gmap = _gate_contract_map(retries=args.retries, backoff=args.backoff)

    rows = []
    for sym in both:
        base = sym[:-4]  # e.g., BTCUSDT -> BTC
        c = gmap.get(base)
        if not c:
            continue  # Gate 无对应永续，不纳入
        gate_rate = _gate_rate_from_contract(c)
        rows.append(
            {
                "symbol": sym,
                "gate_contract": c.get("name") or f"{base}_USDT",
                "gate_rate": gate_rate,
                "gate_next_time": int(c.get("funding_next_apply", 0) or 0),
                "bn_rate": float(bn_rate_map.get(sym, 0.0)),
            }
        )

    # 3) 按 Gate 资金费率倒序输出前 N
    rows.sort(key=lambda r: r["bn_rate"], reverse=True)
    top = rows[: args.top]

    print("Symbol        GateFunding(%)  BinanceFunding(%)  GateContract     NextFundingTime(Gate)")
    print("-------------------------------------------------------------------------------------")
    for r in top:
        gate_pct = r["gate_rate"] * 100
        bn_pct = r["bn_rate"] * 100
        nft = r["gate_next_time"]
        ts_str = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(nft / 1000)) if nft else "-"
        print(f"{r['symbol']:<12} {gate_pct:>13.4f}  {bn_pct:>17.4f}  {r['gate_contract']:<16} {ts_str}")


if __name__ == "__main__":
    main()
