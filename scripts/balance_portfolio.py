#!/usr/bin/env python3
"""
Binance现货持仓平衡脚本
自动将所有持仓调整为每个币种持有20 USDT，多余的钱换成USDT
同时对所有现货币种（除了稳定币）进行永续合约对冲

SOL XRP BNB ETH SUI BCH APT ETC NEAR INJ ADA LTC USDT USDC DOT XLM HBAR POL UNI SEI BTC
"""

import json
import time
import logging
from decimal import Decimal, ROUND_DOWN, ROUND_HALF_UP
from typing import Dict, List, Tuple, Optional
from binance.um_futures import UMFutures

from binance.client import Client
from binance.error import ClientError
from binance.exceptions import BinanceAPIException

# 配置日志
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)

MIN_INIT_USDT = "20.0"
TARGET_LEVERAGE = 40


class PortfolioBalancer:
    def __init__(self, config_path: str = "portfolio_config.json"):
        self.config = self._load_config(config_path)

        # 检查必要的配置
        if "api_key" not in self.config:
            raise ValueError("配置文件中缺少 api_key")
        if "api_secret" not in self.config:
            raise ValueError("配置文件中缺少 api_secret，现货交易需要API Secret")

        # 创建客户端（统一账户模式下现货和期货使用相同的客户端）
        self.spot_client = Client(
            api_key=self.config["api_key"], api_secret=self.config["api_secret"], testnet=False  # 设置为True使用测试网
        )
        self.futures_client = client = UMFutures(key=self.config["api_key"], secret=self.config["api_secret"])
        # 最小初始化名义价值（USDT），当现货与期货都为0时用于建仓
        self.min_init_usdt = Decimal(MIN_INIT_USDT)
        # 最小交易金额（USDT）
        self.min_trade_amount = Decimal("1.0")
        # 价格容差（避免频繁小额交易）
        self.tolerance = Decimal("0.05")  # 5%
        # 稳定币列表（不需要对冲）
        self.stable_coins = {"USDT", "USDC", "BUSD", "FDUSD", "TUSD", "DAI"}
        # 目标币种列表（将在运行时设置）
        self.target_assets = set()

    def set_target_assets(self, assets: List[str]):
        """设置目标持仓币种列表"""
        self.target_assets = set(asset.upper() for asset in assets)
        # 确保USDT始终在目标列表中
        self.target_assets.add("USDT")
        logger.info(f"设置目标币种: {', '.join(sorted(self.target_assets))}")

    def identify_assets_to_liquidate(self, balances: Dict[str, Decimal]) -> List[str]:
        """
        识别需要平仓的币种：不在对冲列表(self.target_assets)中的所有现货与期货基础资产。
        期货只考虑 USDT/USDC 计价的合约。
        """
        if not self.target_assets:
            logger.warning("目标币种列表为空，不会平仓任何币种")
            return []

        assets_to_liquidate: set = set()
        # 现货不在目标列表的
        for asset, qty in balances.items():
            if asset not in self.target_assets and qty > 0:
                assets_to_liquidate.add(asset)

        # 期货不在目标列表的
        try:
            futures_positions = self.get_futures_positions()
            for symbol, pos in futures_positions.items():
                base = None
                if symbol.endswith("USDT"):
                    base = symbol[:-4]
                elif symbol.endswith("USDC"):
                    base = symbol[:-4]
                if base and base not in self.target_assets:
                    assets_to_liquidate.add(base)
        except Exception as e:
            logger.error(f"获取期货持仓失败，跳过期货平仓识别: {e}")

        assets_list = sorted(list(assets_to_liquidate))
        if assets_list:
            logger.info(f"发现需要平仓的币种: {', '.join(assets_list)}")
        else:
            logger.info("没有发现需要平仓的币种")
        return assets_list

    def liquidate_spot_positions(self, assets_to_liquidate: List[str]) -> bool:
        """平仓指定的现货持仓"""
        if not assets_to_liquidate:
            return True

        logger.info("开始平仓现货持仓...")
        successful_liquidations = 0
        failed_liquidations = []

        for asset in assets_to_liquidate:
            try:
                # 获取当前余额
                account_info = self.spot_client.get_account()
                asset_balance = None

                for balance in account_info["balances"]:
                    if balance["asset"] == asset:
                        free = Decimal(balance["free"])
                        locked = Decimal(balance["locked"])
                        total = free + locked
                        if total > 0:
                            asset_balance = total
                        break

                if not asset_balance or asset_balance <= 0:
                    logger.info(f"{asset} 余额为0，跳过")
                    continue

                # 构造交易对符号
                symbol = f"{asset}USDT"

                # 获取交易对信息
                try:
                    symbol_info = self.spot_client.get_symbol_info(symbol)
                except:
                    logger.error(f"无法获取 {symbol} 交易对信息，跳过平仓")
                    failed_liquidations.append((asset, "无法获取交易对信息"))
                    continue

                # 应用数量精度和检查最小名义价值
                quantity = asset_balance
                min_notional = None
                for filter_item in symbol_info["filters"]:
                    if filter_item["filterType"] == "LOT_SIZE":
                        step_size = Decimal(filter_item["stepSize"])
                        # 按照 step_size 进行量化：先除以 step_size，向下取整，再乘以 step_size
                        quantity = (quantity / step_size).quantize(Decimal("1"), rounding=ROUND_DOWN) * step_size
                    elif filter_item["filterType"] == "MIN_NOTIONAL":
                        min_notional = Decimal(filter_item["minNotional"])

                if quantity <= 0:
                    logger.warning(f"{asset} 调整后数量为0，跳过")
                    continue

                # 获取当前价格并检查最小名义价值
                try:
                    ticker = self.spot_client.get_symbol_ticker(symbol=symbol)
                    current_price = Decimal(ticker["price"])
                    notional_value = quantity * current_price

                    if min_notional and notional_value < min_notional:
                        logger.info(
                            f"平仓 {asset}: 作为 reduce-only 卖出尝试（现货无真实 reduceOnly 标志），名义 {notional_value} < 最小 {min_notional}"
                        )
                    logger.info(
                        f"平仓现货 {asset}: 数量 {quantity}, 当前价格 {current_price}, 名义价值 {notional_value} USDT"
                    )
                except Exception as e:
                    logger.error(f"获取 {symbol} 价格失败: {e}")
                    continue

                order = self.spot_client.order_market_sell(symbol=symbol, quantity=str(quantity))
                logger.info(f"现货平仓成功: {order['orderId']}")
                successful_liquidations += 1
                time.sleep(1)  # 避免API限制

            except BinanceAPIException as e:
                logger.error(f"平仓现货 {asset} 失败: {e}")
                failed_liquidations.append((asset, str(e)))
            except Exception as e:
                logger.error(f"平仓现货 {asset} 出错: {e}")
                failed_liquidations.append((asset, str(e)))

        # 总结结果
        logger.info(f"现货平仓结果: 成功 {successful_liquidations} 个，失败 {len(failed_liquidations)} 个")
        if failed_liquidations:
            for asset, reason in failed_liquidations:
                logger.error(f"  {asset}: {reason}")

        return len(failed_liquidations) == 0

    def get_futures_positions(self) -> Dict[str, dict]:
        """获取期货持仓信息"""
        try:
            positions = self.futures_client.get_position_risk()
            result = {}

            for position in positions:
                symbol = position["symbol"]
                position_amt = Decimal(position["positionAmt"])

                if position_amt != 0:  # 只关注有持仓的
                    result[symbol] = {
                        "symbol": symbol,
                        "positionAmt": position_amt,
                        "markPrice": Decimal(position["markPrice"]),
                        "unRealizedProfit": Decimal(position["unRealizedProfit"]),
                        "positionSide": position["positionSide"],
                    }

            logger.info(f"获取到 {len(result)} 个期货持仓")
            return result
        except BinanceAPIException as e:
            logger.error(f"获取期货持仓失败: {e}")
            raise

    def liquidate_futures_positions(self, assets_to_liquidate: List[str]) -> bool:
        """平仓指定币种的期货持仓"""
        if not assets_to_liquidate:
            return True

        logger.info("开始平仓期货持仓...")

        # 获取当前期货持仓
        futures_positions = self.get_futures_positions()

        if not futures_positions:
            logger.info("没有期货持仓需要平仓")
            return True

        successful_liquidations = 0
        failed_liquidations = []

        for asset in assets_to_liquidate:
            # 查找相关的期货持仓
            related_positions = []
            for symbol, position in futures_positions.items():
                if symbol.startswith(asset):  # 例如 BTCUSDT, BTCUSDC
                    related_positions.append((symbol, position))

            if not related_positions:
                logger.info(f"{asset} 没有相关的期货持仓")
                continue

            # 平仓所有相关持仓
            for symbol, position in related_positions:
                try:
                    position_amt = position["positionAmt"]

                    if position_amt == 0:
                        continue

                    # 确定平仓方向
                    side = "BUY" if position_amt < 0 else "SELL"
                    quantity = abs(position_amt)

                    logger.info(f"平仓期货 {symbol}: {side} 数量 {quantity}")
                    order = self.futures_client.new_order(
                        symbol=symbol, side=side, type="MARKET", quantity=str(quantity), reduceOnly=True  # 只减仓
                    )
                    logger.info(f"期货平仓成功: {order}")
                    successful_liquidations += 1
                    time.sleep(1)  # 避免API限制

                except BinanceAPIException as e:
                    logger.error(f"平仓期货 {symbol} 失败: {e}")
                    failed_liquidations.append((symbol, str(e)))
                except Exception as e:
                    logger.error(f"平仓期货 {symbol} 出错: {e}")
                    failed_liquidations.append((symbol, str(e)))

        # 总结结果
        logger.info(f"期货平仓结果: 成功 {successful_liquidations} 个，失败 {len(failed_liquidations)} 个")
        if failed_liquidations:
            for symbol, reason in failed_liquidations:
                logger.error(f"  {symbol}: {reason}")

        return len(failed_liquidations) == 0

    def _load_config(self, config_path: str) -> dict:
        """加载配置文件"""
        try:
            with open(config_path, "r") as f:
                return json.load(f)
        except FileNotFoundError:
            logger.error(f"配置文件 {config_path} 不存在")
            raise
        except json.JSONDecodeError:
            logger.error(f"配置文件 {config_path} 格式错误")
            raise

    def get_spot_balances(self) -> Dict[str, Decimal]:
        account = self.spot_client.get_account()
        balances = account["balances"]
        # 过滤掉空余额，更直观
        non_zero = [b for b in balances if float(b["free"]) > 0 or float(b["locked"]) > 0]
        logger.info(f"获取到 {len(non_zero)} 个账户余额")
        result = {}
        for b in non_zero:
            result[b["asset"]] = Decimal(b["free"]) + Decimal(b["locked"])
        return result

    def get_available_futures_symbols(self) -> Dict[str, dict]:
        """获取可用的期货交易对信息"""
        try:
            exchange_info = self.futures_client.exchange_info()
            futures_symbols = {}

            for symbol_info in exchange_info["symbols"]:
                if symbol_info["status"] == "TRADING" and symbol_info["contractType"] == "PERPETUAL":
                    base_asset = symbol_info["baseAsset"]
                    quote_asset = symbol_info["quoteAsset"]
                    futures_symbols[f"{base_asset}{quote_asset}"] = symbol_info

            logger.info(f"获取到 {len(futures_symbols)} 个可用期货交易对")
            return futures_symbols
        except BinanceAPIException as e:
            logger.error(f"获取期货交易对信息失败: {e}")
            raise

    def find_futures_symbol_for_asset(self, asset: str, futures_symbols: Dict[str, dict]) -> Optional[str]:
        """为现货资产找到合适的期货交易对"""
        # 优先使用USDT计价的期货
        usdt_symbol = f"{asset}USDT"
        if usdt_symbol in futures_symbols:
            return usdt_symbol

        # 其次使用USDC计价的期货
        usdc_symbol = f"{asset}USDC"
        if usdc_symbol in futures_symbols:
            return usdc_symbol

        return None

    def get_futures_position_for_asset(
        self, asset: str, futures_positions: Dict[str, dict]
    ) -> Tuple[Decimal, Optional[str]]:
        """获取某个资产在期货上的总持仓数量（带方向）及其对应symbol。
        返回 (position_amt, symbol)。若无仓位，返回 (Decimal('0'), None)。
        仅考虑 USDT/USDC 计价的永续合约。
        """
        usdt_sym = f"{asset}USDT"
        usdc_sym = f"{asset}USDC"
        if usdt_sym in futures_positions:
            amt = Decimal(futures_positions[usdt_sym]["positionAmt"])
            if amt != 0:
                return amt, usdt_sym
        if usdc_sym in futures_positions:
            amt = Decimal(futures_positions[usdc_sym]["positionAmt"])
            if amt != 0:
                return amt, usdc_sym
        return Decimal("0"), None

    def _extract_spot_filters(self, symbol_info: dict) -> Tuple[Decimal, Decimal, Decimal]:
        """从现货 symbol_info 里提取 (step_size, min_qty, min_notional)。不存在则用合理默认值。"""
        step_size = Decimal("1")
        min_qty = Decimal("0")
        min_notional = Decimal("0")
        for f in symbol_info.get("filters", []):
            if f.get("filterType") == "LOT_SIZE":
                ss = Decimal(f.get("stepSize", "1"))
                step_size = ss if ss > 0 else Decimal("1")
                mq = Decimal(f.get("minQty", "0"))
                min_qty = mq if mq > 0 else min_qty
            elif f.get("filterType") in ("MIN_NOTIONAL", "NOTIONAL"):
                mn = Decimal(f.get("minNotional", "0"))
                min_notional = mn if mn > 0 else min_notional
        return step_size, min_qty, min_notional

    def _extract_futures_filters(self, symbol_info: dict) -> Tuple[Decimal, Decimal, Decimal]:
        """从期货 symbol_info 里提取 (step_size, min_qty, min_notional)。不存在则用合理默认值。"""
        step_size = Decimal("1")
        min_qty = Decimal("0")
        min_notional = Decimal("0")
        for f in symbol_info.get("filters", []):
            if f.get("filterType") == "LOT_SIZE":
                ss = Decimal(f.get("stepSize", "1"))
                step_size = ss if ss > 0 else Decimal("1")
                mq = Decimal(f.get("minQty", "0"))
                min_qty = mq if mq > 0 else min_qty
            elif f.get("filterType") in ("MIN_NOTIONAL", "NOTIONAL"):
                mn = Decimal(f.get("minNotional", "0"))
                min_notional = mn if mn > 0 else min_notional
        return step_size, min_qty, min_notional

    def _round_up_to_step(self, qty: Decimal, step: Decimal) -> Decimal:
        if step <= 0:
            return qty
        # 向上取整到步长的整数倍
        k = (qty / step).to_integral_value(rounding=ROUND_HALF_UP)
        if k * step < qty:
            k += 1
        return k * step

    def compute_base_size(self, asset: str, futures_symbols: Dict[str, dict]) -> Tuple[Decimal, Optional[str]]:
        """当现货和期货都没有持仓时，计算开仓所需的基础数量（base asset size），并返回对应期货symbol。
        依据：现货和期货的 LOT_SIZE(minQty/stepSize)、MIN_NOTIONAL/NOTIONAL 以及 self.min_init_usdt。
        """
        # 现货 symbol 优先使用 USDT 计价
        spot_symbol = f"{asset}USDT"
        spot_info = self.spot_client.get_symbol_info(spot_symbol)
        if not spot_info:
            logger.error(f"找不到现货交易对 {spot_symbol}，无法为 {asset} 计算基础开仓数量")
            return Decimal("0"), None

        fut_symbol = self.find_futures_symbol_for_asset(asset, futures_symbols)
        if not fut_symbol:
            logger.error(f"找不到 {asset} 对应的期货交易对，无法计算基础开仓数量")
            return Decimal("0"), None

        # 提取过滤器
        spot_step, spot_min_qty, spot_min_notional = self._extract_spot_filters(spot_info)
        fut_info = futures_symbols.get(fut_symbol, {})
        fut_step, fut_min_qty, fut_min_notional = self._extract_futures_filters(fut_info)

        # 使用现货价格作为名义价值的价格基准
        price = Decimal(self.spot_client.get_symbol_ticker(symbol=spot_symbol)["price"])

        # 候选数量（满足各自 minQty/minNotional 以及最小初始化名义价值）
        req_by_spot_qty = spot_min_qty if spot_min_qty > 0 else Decimal("0")
        req_by_spot_notional = (spot_min_notional / price) if spot_min_notional > 0 else Decimal("0")
        req_by_fut_qty = fut_min_qty if fut_min_qty > 0 else Decimal("0")
        req_by_fut_notional = (fut_min_notional / price) if fut_min_notional > 0 else Decimal("0")
        req_by_min_init = (self.min_init_usdt / price) if self.min_init_usdt > 0 else Decimal("0")

        base_qty = max(req_by_spot_qty, req_by_spot_notional, req_by_fut_qty, req_by_fut_notional, req_by_min_init)

        # 对齐到共同步长（取较大的步长保证两边都合法）
        common_step = max(spot_step, fut_step)
        if base_qty <= 0:
            base_qty = common_step
        base_qty = self._round_up_to_step(base_qty, common_step)

        # 确保名义价值均满足最小要求，不满足则逐步加到满足为止
        max_iterations = 1000
        i = 0
        while i < max_iterations:
            spot_notional_ok = (spot_min_notional == 0) or (base_qty * price >= spot_min_notional)
            fut_notional_ok = (fut_min_notional == 0) or (base_qty * price >= fut_min_notional)
            min_init_ok = (self.min_init_usdt == 0) or (base_qty * price >= self.min_init_usdt)
            if spot_notional_ok and fut_notional_ok and min_init_ok:
                break
            base_qty += common_step
            i += 1
        if i == max_iterations:
            logger.warning(f"为 {asset} 计算基础数量时达到迭代上限，最终数量: {base_qty}")

        return base_qty, fut_symbol

    def check_futures_balance(self) -> Decimal:
        balances = self.futures_client.balance(recvWindow=6_000)  # list[dict]
        usdt = next(b for b in balances if b["asset"] == "USDT")
        logger.info(f"总余额       : {usdt['balance']} USDT")
        logger.info(f"可用余额     : {usdt['availableBalance']} USDT")
        return Decimal(usdt["availableBalance"])

    def calculate_hedge_quantity(self, symbol: str, balance: Decimal, futures_symbols: Dict[str, dict]) -> Decimal:
        """计算对冲数量，考虑最小下单量等限制"""
        symbol_info = futures_symbols.get(symbol)
        if not symbol_info:
            return balance

        # 获取数量精度
        quantity_precision = 0
        for filter_info in symbol_info.get("filters", []):
            if filter_info["filterType"] == "LOT_SIZE":
                step_size = Decimal(filter_info["stepSize"])
                # 计算精度位数
                if step_size < 1:
                    quantity_precision = len(str(step_size).split(".")[-1].rstrip("0"))
                break

        # 按精度调整数量
        if quantity_precision > 0:
            return balance.quantize(Decimal("0." + "0" * (quantity_precision - 1) + "1"), rounding=ROUND_DOWN)
        else:
            return balance.quantize(Decimal("1"), rounding=ROUND_DOWN)

    def place_futures_hedge_order(
        self, symbol: str, quantity: Decimal, futures_symbols: Dict[str, dict], dry_run: bool = True
    ) -> bool:
        """下期货对冲订单（做空）"""
        try:
            # 检查当前期货持仓，避免重复对冲
            self.futures_client.change_leverage(symbol=symbol, leverage=TARGET_LEVERAGE)
            current_positions = self.get_futures_positions()
            if symbol in current_positions:
                current_position = current_positions[symbol]
                current_size = abs(Decimal(current_position["positionAmt"]))

                if current_size > 0:
                    logger.info(f"{symbol} 已有期货持仓 {current_size}，检查是否需要调整")

                    # 计算目标对冲数量
                    target_hedge_quantity = self.calculate_hedge_quantity(symbol, quantity, futures_symbols)

                    if target_hedge_quantity <= 0:
                        logger.error(f"计算的目标对冲数量无效: {target_hedge_quantity}")
                        return False

                    # 计算需要调整的数量
                    quantity_diff = target_hedge_quantity - current_size

                    if abs(quantity_diff) < Decimal("0.001"):  # 差异很小，不需要调整
                        logger.info(f"{symbol} 当前持仓 {current_size} 接近目标 {target_hedge_quantity}，无需调整")
                        return True

                    if quantity_diff > 0:
                        # 需要增加空头持仓
                        hedge_quantity = quantity_diff
                        logger.info(f"{symbol} 需要增加空头持仓 {hedge_quantity}")
                        side = "SELL"
                    else:
                        hedge_quantity = abs(quantity_diff)
                        logger.info(f"{symbol} 需要减少空头持仓 {hedge_quantity}")
                        side = "BUY"
                else:
                    hedge_quantity = self.calculate_hedge_quantity(symbol, quantity, futures_symbols)
                    side = "SELL"
            else:
                hedge_quantity = self.calculate_hedge_quantity(symbol, quantity, futures_symbols)
                side = "SELL"

            if hedge_quantity <= 0:
                logger.error(f"计算的对冲数量无效: {hedge_quantity}")
                return False

            # 任何减仓操作使用 reduceOnly 以避免 NOTIONAL 限制
            reduce_only_flag = False
            if symbol in current_positions:
                try:
                    pos_amt_signed = Decimal(current_positions[symbol]["positionAmt"])  # 正=多，负=空
                    # 若当前为空头且买入 -> 减仓；若当前为多头且卖出 -> 减仓
                    if (pos_amt_signed < 0 and side == "BUY") or (pos_amt_signed > 0 and side == "SELL"):
                        reduce_only_flag = True
                except Exception:
                    pass

            try:
                order = self.futures_client.new_order(
                    symbol=symbol,
                    side=side,
                    type="MARKET",
                    quantity=str(hedge_quantity),
                    reduceOnly=reduce_only_flag,
                )

                logger.info(f"期货下单成功: {order}")
                return True
            except ClientError as e:
                logger.error(f"期货下单失败 {symbol}: {e}")
                return False

        except BinanceAPIException as e:
            logger.error(f"期货下单失败 {symbol}: {e}")
            return False

    def adjust_spot_quantity_to(self, asset: str, target_qty: Decimal) -> bool:
        """将现货持仓数量调整为目标数量（按USDT现货交易对下单）。
        返回是否执行了下单（True/False）。
        """
        try:
            symbol = f"{asset}USDT"
            symbol_info = self.spot_client.get_symbol_info(symbol)
            if not symbol_info:
                logger.warning(f"找不到现货交易对 {symbol}，跳过 {asset} 数量同步")
                return False

            # 当前现货数量
            account_info = self.spot_client.get_account()
            current_qty = Decimal("0")
            for b in account_info["balances"]:
                if b["asset"] == asset:
                    current_qty = Decimal(b["free"]) + Decimal(b["locked"])
                    break

            diff = target_qty - current_qty
            if abs(diff) <= Decimal("0"):
                logger.info(f"{asset} 现货已与目标数量一致: {current_qty}")
                return False

            # 过滤器与价格
            step_size = None
            min_notional = None
            for f in symbol_info["filters"]:
                if f["filterType"] == "LOT_SIZE":
                    step_size = Decimal(f["stepSize"]) if Decimal(f["stepSize"]) > 0 else Decimal("1")
                elif f["filterType"] in ("MIN_NOTIONAL", "NOTIONAL") and "minNotional" in f:
                    min_notional = Decimal(f["minNotional"])
            if step_size is None:
                step_size = Decimal("1")

            ticker = self.spot_client.get_symbol_ticker(symbol=symbol)
            price = Decimal(ticker["price"])

            qty = abs(diff)
            # 按步长向下取整，避免超卖/超买
            qty = (qty / step_size).quantize(Decimal("1"), rounding=ROUND_DOWN) * step_size
            if qty <= 0:
                logger.info(f"{asset} 需要调整的数量太小，忽略")
                return False

            side = "BUY" if diff > 0 else "SELL"
            notional = qty * price
            if side == "BUY" and min_notional and notional < min_notional:
                logger.warning(f"{asset} 买入名义价值 {notional} < 最小名义价值 {min_notional}，跳过")
                return False
            # 卖出视为 reduce-only 语义：不强制最小名义价值检查（交易所若仍拒单将在异常中体现）
            logger.info(f"调整现货 {asset} 至目标数量: 当前 {current_qty}, 目标 {target_qty}, 下单 {side} {qty}")
            if side == "BUY":
                order = self.spot_client.order_market_buy(symbol=symbol, quantity=str(qty))
            else:
                order = self.spot_client.order_market_sell(symbol=symbol, quantity=str(qty))
            logger.info(f"{asset} 数量同步下单成功: {order['orderId']}")
            time.sleep(1)
            return True
        except BinanceAPIException as e:
            logger.error(f"调整现货 {asset} 数量失败: {e}")
            return False
        except Exception as e:
            logger.error(f"调整现货 {asset} 数量出错: {e}")
            return False

    def balance_portfolio(self):
        """执行投资组合平衡和对冲（优化：优先以合约仓位为准同步现货；无合约仓位时按TARGET_POSITION_USDT建仓再对冲）"""
        logger.info("开始投资组合平衡...")

        # 检查是否设置了目标币种
        if not self.target_assets:
            logger.error("未设置目标币种列表，请先调用 set_target_assets() 方法")
            return

        # 1. 获取当前余额
        logger.info("获取现货账户余额...")
        balances = self.get_spot_balances()
        if not balances:
            logger.warning("没有发现任何现货余额")
            return

        # 2. 先清理不在目标集合内的现货/合约
        logger.info("识别需要平仓的币种...")
        assets_to_liquidate = self.identify_assets_to_liquidate(balances)
        if assets_to_liquidate:
            logger.info("\n=== 开始平仓不在套利集合中的币种 ===")
            logger.info("平仓期货持仓...")
            futures_liquidation_success = self.liquidate_futures_positions(assets_to_liquidate)
            logger.info("平仓现货持仓...")
            spot_liquidation_success = self.liquidate_spot_positions(assets_to_liquidate)
            if not (futures_liquidation_success and spot_liquidation_success):
                logger.warning("部分平仓操作失败，但继续执行后续操作")
            logger.info("重新获取现货账户余额...")
            balances = self.get_spot_balances()

        # 3) 获取期货交易对信息与当前期货持仓
        futures_symbols = self.get_available_futures_symbols()
        futures_positions = self.get_futures_positions()
        self.check_futures_balance()

        # 4) 对每个目标币种执行规则
        target_assets = sorted([a for a in self.target_assets if a != "USDT"])  # 不对 USDT 做对冲
        for asset in target_assets:
            try:
                spot_qty = balances.get(asset, Decimal("0"))
                fut_signed_amt, fut_symbol = self.get_futures_position_for_asset(asset, futures_positions)
                fut_qty = abs(fut_signed_amt)

                if spot_qty == 0 and fut_qty == 0:
                    base_qty, base_fut_symbol = self.compute_base_size(asset, futures_symbols)
                    if base_qty > 0 and base_fut_symbol:
                        logger.info(f"{asset}: 无持仓，按基础数量建仓 base_qty={base_qty}")
                        self.adjust_spot_quantity_to(asset, base_qty)
                        self.place_futures_hedge_order(base_fut_symbol, base_qty, futures_symbols)
                    else:
                        logger.warning(f"{asset}: 无法计算基础建仓数量，跳过")

                elif fut_qty > 0 and spot_qty == 0:
                    logger.info(f"{asset}: 期货有仓 {fut_qty}，现货无仓 -> 现货对齐期货")
                    self.adjust_spot_quantity_to(asset, fut_qty)

                elif fut_qty == 0 and spot_qty > 0:
                    logger.info(f"{asset}: 现货有仓 {spot_qty}，期货无仓 -> 期货做空对齐现货")
                    fut_symbol2 = fut_symbol or self.find_futures_symbol_for_asset(asset, futures_symbols)
                    if fut_symbol2:
                        self.place_futures_hedge_order(fut_symbol2, spot_qty, futures_symbols)
                    else:
                        logger.error(f"{asset}: 找不到期货交易对，无法对冲")

                else:
                    target_qty = fut_qty
                    if target_qty > 0 and spot_qty != target_qty:
                        logger.info(f"{asset}: 双边有仓，现货 {spot_qty} -> 对齐期货 {target_qty}")
                        self.adjust_spot_quantity_to(asset, target_qty)
                    else:
                        logger.info(f"{asset}: 双边数量一致 {spot_qty}，无需调整")

                time.sleep(1)
            except Exception as e:
                logger.error(f"处理 {asset} 时出错: {e}")

        logger.info("投资组合平衡完成")


def main():
    balancer = PortfolioBalancer()
    assets_input = [
        "BNB",
        "ETH",
        "USDT",
        "USDC",
        "BTC",
        "WLFI",
        "PUMP",
        "DENT",
        "ALPINE",
    ]
    balancer.set_target_assets(assets_input)
    balancer.balance_portfolio()
    return 0


if __name__ == "__main__":
    exit(main())
